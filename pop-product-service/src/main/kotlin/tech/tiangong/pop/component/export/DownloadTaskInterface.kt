package tech.tiangong.pop.component.export

import cn.hutool.core.date.DatePattern
import org.apache.commons.io.FileUtils
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.service.settings.DownloadTaskProcessor
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.util.*

@Slf4j
abstract class DownloadTaskInterface : DownloadTaskProcessor {

    /**
     * 创建临时目录
     */
    @Throws(IOException::class)
    private fun createTempDirectory(taskId: Long): File {
        val tempDirStr = (FileUtils.getTempDirectoryPath() + File.separator
                + "export_task_" + taskId + File.separator
                + LocalDateTime.now().format(DatePattern.PURE_DATETIME_FORMATTER))
        val tempDir = File(tempDirStr)
        FileUtils.forceMkdir(tempDir)
        return tempDir
    }

    /**
     * 清理临时文件
     */
    private fun cleanupTempFiles(tempDir: File?) {
        try {
            if (Objects.nonNull(tempDir)) {
                FileUtils.deleteDirectory(tempDir)
            }
        } catch (e: IOException) {
            e.printStackTrace()
            log.error(e) { "清理临时文件失败: $tempDir" }
        }
    }

    /**
     * 业务处理返回文件数组
     * @param task 任务
     * @return java.util.List<tech.tiangong.aigc.common.oss.dto.FileUploadDTO>
     * <AUTHOR>
     * @date  2024/9/6
    </tech.tiangong.aigc.common.oss.dto.FileUploadDTO> */
    override fun processDownloadTask(task: DownloadTask): List<FileUploadDTO> {
        val taskId = task.downloadTaskId!!
        var tempDir: File? = null

        try {
            tempDir = createTempDirectory(taskId)
            val fileUploadDTO: FileUploadDTO = export(task.parameters, tempDir, task)

            return listOf(fileUploadDTO)
        } catch (e: BusinessException) {
            log.warn { "导出业务处理异常: taskId=$taskId, message=${e.message}" }
            throw e
        } catch (e: Exception) {
            log.error(e) { "导出系统处理异常: taskId=$taskId, req=${task.parameters}, message=${e.message}" }
            throw BusinessException("导出任务处理失败", e)
        } finally {
            cleanupTempFiles(tempDir)
        }
    }

    @Throws(IOException::class)
    abstract fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO
}