package tech.tiangong.pop.component.price

import team.aikero.blade.core.enums.Bool
import tech.tiangong.pop.bo.ProductPriceAlertSkcData
import tech.tiangong.pop.bo.ProductPriceAlertSkuData
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.resp.product.ProductPriceAlertCheckResp

/**
 * 价格兜底参数对象
 */
data class PriceAlertValidationParam(
    /**
     * 商品实体，包含商品基础信息
     */
    val product: Product,

    /**
     * 平台ID（如 Lazada、Shopee 等）
     */
    val platformId: Long,

    /**
     * 商品品类ID
     */
    val categoryId: Long,

    /**
     * 国家或站点编码
     */
    val countryCode: String,

    /**
     * SKC（单品款色码）数据列表
     */
    val skcDataList: List<ProductPriceAlertSkcData>,

    /**
     * SKU（单品）数据映射，key 为 SKC ID，value 为对应的 SKU 列表
     */
    val skuDataMap: Map<Long, List<ProductPriceAlertSkuData>>,

    /**
     * 是否跳过价格兜底（为 true 时仅提示，不阻断流程）
     */
    val skipValidation: Int = Bool.NO.code,

    /**
     * 是否发送价格异常通知（默认 true）
     */
    val sendNotification: Int = Bool.YES.code,
)

/**
 * 价格兜底结果
 */
data class PriceAlertValidationResult(
    /**
     * 是否校验通过
     */
    val valid: Boolean,

    /**
     * 校验结果消息
     */
    val message: String,

    /**
     * 详细的校验结果
     */
    val checkResult: ProductPriceAlertCheckResp? = null,

    /**
     * 是否已添加标签
     */
    val tagged: Boolean = false,
)