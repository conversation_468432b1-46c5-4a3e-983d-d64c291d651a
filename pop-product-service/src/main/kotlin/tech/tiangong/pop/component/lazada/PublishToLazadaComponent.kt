package tech.tiangong.pop.component.lazada

import com.lazada.lazop.util.ApiException
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.config.LazadaProperties
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductSyncLog
import tech.tiangong.pop.dao.entity.SaleGoods
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.resp.sdk.lazada.LazadaUpdatePriceQuantityResponse
import tech.tiangong.pop.resp.sdk.lazada.UpdatePriceQuantityReq
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.ProductCreateV2Service
import java.util.function.Consumer

/**
 * 发布到Lazada
 * <AUTHOR>
 * @date 2025-2-14 15:51:33
 */
@Slf4j
@Component
class PublishToLazadaComponent(
    private val lazadaApiService: LazadaApiService,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val publishPlatformRepository: PublishPlatformRepository,
    private val productRepository: ProductRepository,
    private val transactionManager: PlatformTransactionManager,
    private val lazadaProperties: LazadaProperties,
    private val productCreateV2Service: ProductCreateV2Service,
    private val saleSkuRepository: SaleSkuRepository,
) {
    /**
     * 处理Lazada价格更新主流程
     */
    fun processLazadaPriceUpdate(lazadaPriceMqDtoList: List<LazadaPriceMqDto?>) {

        lazadaPriceMqDtoList
            // 过滤掉 null 元素
            .filterNotNull()
            .groupBy(LazadaPriceMqDto::lazadaCountry)
            .forEach { (country, skuList) ->
                skuList.groupBy(LazadaPriceMqDto::lazadaShopToken)
                    .forEach { (shopToken, shopSku) ->
                        // 过滤掉 shopToken 为 null 的情况
                        if (shopToken != null && shopSku.isNotEmpty()) {
                            processShopSkuUpdate(country!!, shopToken, shopSku)
                        }
                    }
            }
    }

    /**
     * 处理店铺SKU更新
     */
    private fun processShopSkuUpdate(country: String, shopToken: String, shopSku: List<LazadaPriceMqDto>) {
        shopSku.groupBy(LazadaPriceMqDto::saleProductId)
            .forEach { (_, lazadaPriceList) -> updateProductPrice(country, shopToken, lazadaPriceList) }
    }

    /**
     * 更新商品价格
     */
    private fun updateProductPrice(country: String, shopToken: String, lazadaPriceList: List<LazadaPriceMqDto>) {
        val productIds = mutableListOf<Long>()

        // 分批处理SKU列表
        val batchList = lazadaPriceList.chunked(lazadaProperties.updatePriceMaxBatchSize)

        // 获取所有商品ID，用于状态更新
        val allSaleGoodsIds = lazadaPriceList.mapNotNull { it.saleProductId }.distinct()

        log.info { "开始处理Lazada价格更新, 商品总数=${allSaleGoodsIds.size}, 批次数=${batchList.size}" }

        // 更新初始状态（单独事务）
        updateInitialStatus(allSaleGoodsIds)

        try {
            // 处理每一批SKU
            var hasError = false

            for ((currentBatch, batch) in batchList.withIndex()) {
                log.info { "处理第${currentBatch}批数据, 批次大小=${batch.size}" }

                val updatePriceQuantity = buildUpdatePriceRequest(batch)
                try {
                    for (lazadaPriceMqDto in batch) {
                        lazadaPriceMqDto.productId?.let { productIds.add(it) }
                    }
                    val response = lazadaApiService.updatePriceQuantity(country, shopToken, updatePriceQuantity, batch.first().platformId!!)
                    if (!response.isSuccess) {
                        log.error { "Lazada API调用失败, 批次=${currentBatch}/${batchList.size}, 响应信息=${response.toJson()}" }
                        hasError = true
                        // 保存错误日志（单独事务）
                        saveErrorLog(allSaleGoodsIds, batch, updatePriceQuantity, response)
                        break
                    }
                    log.info { "成功更新第${currentBatch}批数据" }
                } catch (e: ApiException) {
                    log.error(e) { "调用Lazada API异常, 批次=${currentBatch}/${batchList.size}, 批次大小=${batch.size}" }
                    hasError = true
                    break
                }
            }

            // 更新最终状态（单独事务）
            updateFinalStatus(allSaleGoodsIds, hasError)

            if (hasError) {
                throw BusinessException("同步Lazada价格异常")
            }

            log.info { "完成Lazada价格更新, 商品总数=${allSaleGoodsIds.size}, 处理结果=${if (hasError) "失败" else "成功"}" }
        } catch (e: Exception) {
            log.error(e) { "批量修改价格处理异常, 商品总数=${allSaleGoodsIds.size}" }
            // 确保最终状态更新（单独事务）
            updateFinalStatus(allSaleGoodsIds, true)
            throw BusinessException("同步Lazada价格异常")
        }

        // 无论成功/失败, 都要调用product状态更新
        if (CollectionUtils.isNotEmpty(productIds)) {
            val productList = productRepository.listByIds(productIds.distinct())
            if (CollectionUtils.isNotEmpty(productList)) {
                productList!!.forEach(Consumer { product: Product? -> productCreateV2Service.updateProductStateByCreateError(product!!) })
            }
        }
    }

    /**
     * 更新初始状态
     */
    private fun updateInitialStatus(saleGoodsIds: List<Long>) {
        TransactionTemplate(transactionManager).executeWithoutResult {
            try {
                updateSyncStatusBySaleGoodsIdList(saleGoodsIds, PlatformSyncStateEnum.PROCESSING)
                log.info { "更新初始状态成功, 商品数量=${saleGoodsIds.size}" }
            } catch (e: java.lang.Exception) {
                log.error(e) { "更新初始状态失败, 商品数量=${saleGoodsIds.size}" }
                throw e
            }
        }
    }

    /**
     * 保存错误日志
     */
    private fun saveErrorLog(
        saleGoodsIds: List<Long>,
        batch: List<LazadaPriceMqDto>,
        updatePriceQuantity: UpdatePriceQuantityReq,
        response: LazadaUpdatePriceQuantityResponse,
    ) {
        TransactionTemplate(transactionManager).executeWithoutResult {
            try {
                val lazadaPriceMqDto = batch.first()
                addErrorSyncLog(lazadaPriceMqDto, updatePriceQuantity, response)
                log.info { "保存错误日志成功, 商品数量=${saleGoodsIds.size}" }
            } catch (e: java.lang.Exception) {
                log.error(e) { "保存错误日志失败, 商品数量=${saleGoodsIds.size}" }
                // 这里我们不抛出异常，因为这是非关键操作
                log.error(e) { "错误日志保存失败，但继续处理主流程" }
            }
        }
    }

    /**
     * 记录同步失败日志
     * @param lazadaPriceMqDto SPU级别
     * @param request
     * @param response
     */
    private fun addErrorSyncLog(
        lazadaPriceMqDto: LazadaPriceMqDto,
        request: UpdatePriceQuantityReq,
        response: LazadaUpdatePriceQuantityResponse,
    ) {
        val opType = PlatformOperatorTypeEnum.MODIFY_PRICE
        val productSyncLog = ProductSyncLog()
        productSyncLog.productId = lazadaPriceMqDto.productId
        productSyncLog.saleGoodId = lazadaPriceMqDto.saleProductId
        productSyncLog.errorMsg = "[" + opType.desc + "]" + response.message
        productSyncLog.opType = opType.code
        productSyncLog.platformHttpResp = response.toJson()
        productSyncLog.platformRequestParams = request.toJson()
        if (lazadaPriceMqDto.platformId != null) {
            val publishPlatform = publishPlatformRepository.getById(lazadaPriceMqDto.platformId)
            if (publishPlatform != null) {
                productSyncLog.platformName = publishPlatform.platformName
            }
        }
        productSyncLog.shopId = lazadaPriceMqDto.shopId
        productSyncLog.shopName = lazadaPriceMqDto.shopName
        productSyncLog.deleted = YesOrNoEnum.NO.code
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 更新最终状态
     */
    private fun updateFinalStatus(saleGoodsIds: List<Long>, hasError: Boolean) {
        TransactionTemplate(transactionManager).executeWithoutResult {
            try {
                val finalState = if (hasError) PlatformSyncStateEnum.FAILURE else PlatformSyncStateEnum.SUCCESS
                updateSyncStatusBySaleGoodsIdList(saleGoodsIds, finalState)
                log.info { "更新最终状态成功, 商品数量=${saleGoodsIds.size}, 状态=${finalState}" }
            } catch (e: java.lang.Exception) {
                log.error(e) { "更新最终状态失败, 商品数量=${saleGoodsIds.size}" }
                throw e
            }
        }
    }


    /**
     * 构建更新价格请求
     */
    private fun buildUpdatePriceRequest(lazadaPriceList: List<LazadaPriceMqDto>): UpdatePriceQuantityReq {
        val updatePriceQuantity = UpdatePriceQuantityReq()
        val product = UpdatePriceQuantityReq.Product()
        val skus = UpdatePriceQuantityReq.Product.Skus()

        updatePriceQuantity.product = product
        product.skus = skus
        skus.skuList = buildSkuList(lazadaPriceList)

        return updatePriceQuantity
    }

    /**
     * 构建SKU列表
     */
    private fun buildSkuList(lazadaPriceList: List<LazadaPriceMqDto>): List<UpdatePriceQuantityReq.Product.Skus.Sku> {
        return lazadaPriceList
            .map { item ->
                val saleSku = saleSkuRepository.getById(item.saleSkuId)
                val sku = UpdatePriceQuantityReq.Product.Skus.Sku()
                sku.itemId = item.lazadaItemId
                sku.skuId = item.lazadaSkuId
                sku.sellerSku = item.lazadaSellerSku
                sku.price = saleSku.retailPrice?.toString()
                sku.salePrice = saleSku.salePrice?.toString()
                sku.quantity = saleSku.stockQuantity?.toString()
                sku
            }
    }

    /**
     * 更新销售商品的同步状态
     * @param saleGoodsIdList
     * @param platformSyncStateEnum
     */
    private fun updateSyncStatusBySaleGoodsIdList(saleGoodsIdList: List<Long>, platformSyncStateEnum: PlatformSyncStateEnum) {
        // 更新同步状态: 同步中
        val processingSaleGoodsList = saleGoodsIdList.map {
            val saleGoods = SaleGoods()
            saleGoods.saleGoodsId = it
            saleGoods.platformSyncState = platformSyncStateEnum.code
            saleGoods
        }
        if (CollectionUtils.isNotEmpty(processingSaleGoodsList)) {
            saleGoodsRepository.updateBatchById(processingSaleGoodsList)
        }
    }
}
