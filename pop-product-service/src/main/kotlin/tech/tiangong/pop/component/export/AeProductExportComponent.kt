package tech.tiangong.pop.component.export

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.support.ExcelTypeEnum
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.ProductAeTaxTypeEnum
import tech.tiangong.pop.common.enums.SdpStyleTypeEnum
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.ProductTemplateAeSpu
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.ImportAeProductDTO
import tech.tiangong.pop.dto.product.ImportBaseProductDTO
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.InvDeductionEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.AttributePairReq
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePageReq
import tech.tiangong.pop.resp.settings.ProductTitleConfigResp
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.service.settings.ProductTitleConfigService
import tech.tiangong.pop.utils.ExcelUtils
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 导出-待上架-AE商品
 * <AUTHOR>
 * @date 2025-4-16 17:57:52
 */
@Component
@Slf4j
class AeProductExportComponent(
    private val uploaderOssHelper: UploaderOssHelper,
    private val productRepository: ProductRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val productTitleConfigService: ProductTitleConfigService,
    private val commonProperties: CommonProperties,
    private val shopRepository: ShopRepository,
    private val downloadTaskService: DownloadTaskService,
) : DownloadTaskInterface() {


    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductPendingAePageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productTemplateAeSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出待上架AE商品_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PENDING_LISTING_AE_TASK,
                req.toJson()
            );
            log.info { "导出上架失败日志-AE待上架-创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出上架失败日志-AE待上架-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("提交导出待上架发布失败任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ProductPendingAePageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 分页所有数据
        var pageNum: Long = 1
        val pageSize: Long = 1000
        // key=sheet(品类), value=sheetData
        val sheetDataMap = mutableMapOf<String, MutableList<ImportAeProductDTO>>()
        val titleConfig = productTitleConfigService.findByPlatformId(AE.platformId)

        while (true) {
            val pageVo = productTemplateAeSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (pageVo.records.isEmpty()) {
                break
            }
            pageVo.records.forEach { templateSpu ->
                val dataList = exportProductByAe(templateSpu, titleConfig)
                if (dataList.isNotEmpty()) {
                    dataList.forEach { data ->
                        sheetDataMap.getOrPut(data.categoryName ?: "") { mutableListOf() }.add(data)
                    }
                }
            }
            pageNum++
        }
        if (sheetDataMap.isEmpty()) {
            throw BusinessException("导出数据为空")
        }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {
                val excelWriter = EasyExcel.write(excelFile).excelType(ExcelTypeEnum.XLSX).build()
                // 将 MutableMap 的 entries 转换为 List 后使用 forEachIndexed
                sheetDataMap.entries.toList().forEachIndexed { index, entry ->
                    val sheetName = entry.key
                    val dataList = entry.value
                    // Ae属性转为动态列
                    val resultAeMap = ImportBaseProductDTO.convertToMapList(dataList)
                    val totalAe = resultAeMap.map { map -> map.values.toList() }

                    val writeSheet = EasyExcel.writerSheet(index, ExcelUtils.replaceSheetName(sheetName))
                        .head(ExcelUtils.extractHeaders(resultAeMap))
                        .build()
                    excelWriter.write(totalAe, writeSheet)
                }
                excelWriter.finish()
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败")
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }

    /**
     * 处理AE数据导出
     */
    private fun exportProductByAe(templateSpu: ProductTemplateAeSpu, titleConfig: ProductTitleConfigResp?): MutableList<ImportAeProductDTO> {
        val allAeData: MutableList<ImportAeProductDTO> = mutableListOf()
        // 获取product
        val product = productRepository.getById(templateSpu.productId) ?: return allAeData
        // 获取上架shop
        val listingShop = shopRepository.getById(templateSpu.shopId) ?: return allAeData
        // 获取Ae模板SKC
        val skcAeList = productTemplateAeSkcRepository.listByAeSpuId(templateSpu.aeSpuId!!)
            .filter { it.state == Bool.YES.code }
            .filter { it.combo == Bool.NO.code }
        if (skcAeList.isEmpty()) {
            return allAeData
        }

        // 获取Ae模板SKU
        val skuAeList = productTemplateAeSkuRepository.listByAeSkcIdList(skcAeList.mapNotNull { it.aeSkcId })
            .filter { it.enableState == Bool.YES.code }

        if (skuAeList.isEmpty()) {
            return allAeData
        }

        if (templateSpu.productTitle.isBlank()) {
            val generatedTitleResp = productTitleGenerateService.previewTitle(ProductTitleGenerateReq().apply {
                this.productId = product.productId
                this.product = product
            })
            if (generatedTitleResp.productTitleRuleId.isNotNull()) {
                templateSpu.productTitle = generatedTitleResp.title
                    .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(AE) }
                    ?: templateSpu.productTitle
                templateSpu.generatedTitleOverLengthFlag = if (generatedTitleResp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                templateSpu.generatedTitleMissingFieldsJson = generatedTitleResp.missingFields.toJson()
            }
        }

        val titleCount = titleConfig?.titleCount ?: 1
        // 处理多标题：从 generatedTitlesJson 解析多标题，用逗号分隔
        val generatedTitles = templateSpu.getParsedGeneratedTitles()

        val productTitle = if (generatedTitles != null && generatedTitles.titles.isNotEmpty()) {
            generatedTitles.titles
                .filter { it.title?.isNotBlank() == true }
                .mapNotNull { it.title }
                .take(titleCount)
                .joinToString(",")
        } else {
            templateSpu.productTitle
        }

        skcAeList.forEach { skcAe ->
            val importDto = ImportAeProductDTO().apply {
                this.supplyMode = SupplyModeEnum.getByCode(product.supplyMode)?.desc
                this.productTitle = productTitle
                this.spuCode = templateSpu.spuCode
                this.categoryName = product.categoryName
                this.shopName = product.shopName
                this.listingShopName = listingShop.shopName
                this.brandName = templateSpu.brandName
                this.localPrice = skcAe.localPrice?.toEngineeringString()
                this.purchasePrice = skcAe.purchasePrice?.toEngineeringString()
                product.spotTypeCode?.let { this.spotType = SpotTypeOpsEnum.getByCode(it)?.desc }
                product.pricingType?.let { this.pricingType = ProductPricingTypeEnum.getByCode(it)?.desc }
                this.stockQuantity = skuAeList.first().stockQuantity?.toString()
                this.sizeGroupName = product.sizeGroupName
                this.sizeNames = skuAeList.mapNotNull { it.sizeName }.distinct().joinToString(",")
                this.skcCode = skcAe.skc
                this.packageWeight = templateSpu.packageWeight
                this.color = skcAe.color
                this.invDeductionName = templateSpu.invDeduction?.let { InvDeductionEnum.getByCode(it)?.desc }
                this.originPlaceName = templateSpu.getParsedOriginPropertyValueItems()?.mapNotNull { it.attributeValueName }?.joinToString("-")
                //选款店铺
                var shopId = product.shopId
                if (shopId == null) {
                    //没有选款店铺，则根据市场编码指定默认店铺
                    if (product.marketCode != null && product.marketCode.isNotBlank()) {
                        shopId = commonProperties.marketDefaultShop.firstOrNull { it.shopId != null && it.marketCode == product.marketCode }?.shopId
                    }
                }
                // 以SKC维度，处理多发货地和对应的多价格：按发货地顺序导出价格，用逗号分隔
                val enabledSkuList = skuAeList.filter { it.enableState == Bool.YES.code && it.aeSkcId == skcAe.aeSkcId }
                val shipsFromList = enabledSkuList.mapNotNull { it.shipsFromAttributeValueName }.distinct()

                if (shipsFromList.isNotEmpty()) {
                    val retailPriceList = mutableListOf<String>()
                    val salePriceList = mutableListOf<String>()

                    // 有发货地信息，按发货地顺序导出价格
                    this.shipsFrom = shipsFromList.joinToString(",")

                    shipsFromList.forEach { shipsFromName ->
                        val sku = if (shopId != null) {
                            enabledSkuList.filter { shopId == it.shopId }.find { it.shipsFromAttributeValueName == shipsFromName }
                        } else {
                            enabledSkuList.find { it.shipsFromAttributeValueName == shipsFromName }
                        }
                        val naConfig = sku?.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                        retailPriceList.add(naConfig?.firstOrNull { it.defaultFLag == 1 }?.price?.toEngineeringString() ?: sku?.retailPrice?.toEngineeringString() ?: "")
                        salePriceList.add(sku?.salePrice?.toEngineeringString() ?: "")
                    }
                    this.retailPrice = retailPriceList.filter { it.isNotBlank() }.joinToString(",")
                    this.salePrice = salePriceList.filter { it.isNotBlank() }.joinToString(",")
                } else {
                    // 没有发货地信息，使用第一个SKU的价格
                    val firstSku = enabledSkuList.firstOrNull()
                    this.retailPrice = firstSku?.retailPrice?.toEngineeringString() ?: ""
                    this.salePrice = firstSku?.salePrice?.toEngineeringString() ?: ""
                }

                this.taxType = ProductAeTaxTypeEnum.getByValue(templateSpu.taxType!!).description

                // 处理款式类型：使用枚举转中文
                product.styleType?.let { styleTypeCode ->
                    this.styleType = SdpStyleTypeEnum.getByCode(styleTypeCode)?.desc ?: "未知类型"
                }
                // 是否区域定价
                this.regionalPricingFlag = "是"
            }
            // 设置属性
            val attributes = productAttributesRepository.listByProductId(product.productId!!, AE.platformId)
            if (attributes.isNotEmpty()) {
                val attributePairRespList = productRepository.selectAttributesById(
                    attributes.map { att ->
                        AttributePairReq().apply {
                            this.attributeId = att.attributeId
                            this.attributeValueId = att.attributeValueId
                        }
                    }
                )
                if (attributePairRespList.isNotEmpty()) {
                    importDto.attributePairResps = attributePairRespList
                }
            }
            allAeData.add(importDto)
        }
        return allAeData
    }
}