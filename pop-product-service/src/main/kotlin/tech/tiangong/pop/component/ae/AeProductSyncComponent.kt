package tech.tiangong.pop.component.ae

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.entity.AeOriginalProductSku
import tech.tiangong.pop.dao.entity.AeOriginalProductSpu
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dao.repository.AeOriginalProductSkuRepository
import tech.tiangong.pop.dao.repository.AeOriginalProductSpuRepository
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository
import tech.tiangong.pop.enums.ProductAeAttributeTypeEnum
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressProductQueryResponse
import tech.tiangong.pop.service.AliexpressService
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 同步AE商品
 * <AUTHOR>
 * @date 2025-5-3 00:21:20
 */
@Slf4j
@Service
class AeProductSyncComponent(
    private var aliexpressService: AliexpressService,
    private var aeOriginalProductSpuRepository: AeOriginalProductSpuRepository,
    private var aeOriginalProductSkuRepository: AeOriginalProductSkuRepository,
    private var aeSaleGoodsRepository: AeSaleGoodsRepository,
    private var aliexpressProperties: AliexpressProperties,
    private var aliexpressServiceHelper: AliexpressServiceHelper,
) {

    /**
     * 获取Ae商品详情
     *
     * @param itemId    saleGoods.platformProductId
     * @param shop
     * @return
     */
    @Transactional(rollbackFor = [Exception::class])
    fun getAeProductDetail(
        itemId: Long,
        shop: Shop,
    ): Pair<AliexpressProductQueryResponse, Pair<AeOriginalProductSpu?, List<AeOriginalProductSku>?>?> {
        val result = aliexpressService.queryProduct(itemId, shop.token!!)
        if (!result.isSuccess()) {
            if (Objects.equals("isv.10THD_IC_ERR_F_IC_SERVICE_QUERY_002", result.subCode)) {
                log.warn { "获取AE商品详情, SKU不存在 itemId: $itemId, message: ${result.subMsg}" }
                // 逻辑删除spu
                aeOriginalProductSpuRepository.logicDeleteByProductId(itemId)
                return Pair(result, null)
            }
            log.error { "获取AE商品详情失败, itemId: $itemId, resp_json: ${result.toJson()}" }
            return Pair(result, null)
        }


        var resultSpu: AeOriginalProductSpu? = null
        var resultSkuList = mutableListOf<AeOriginalProductSku>()
        val spu = result.result
        if (spu == null) {
            return Pair(result, Pair(resultSpu, resultSkuList))
        }

        // 记录源数据
        // SPU
        val aeOriginalProductSpu = AeOriginalProductSpu().apply {
            this.productId = spu.productId
            this.title = spu.subjectList?.firstOrNull { it.locale == "en_US" }?.value
            this.shopId = shop.shopId
            this.productStatusType = spu.productStatusType
            this.gmtCreate = spu.gmtCreate?.let { LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) }
            this.gmtModified = spu.gmtModified?.let { LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) }
            this.deliveryTime = spu.deliveryTime
            this.categoryId = spu.categoryId?.toLong()
            this.mobileDetailImages = spu.detailSourceList?.firstOrNull()?.getMobileDetailRoot()?.toJson()
            this.webDetailImages = spu.detailSourceList?.firstOrNull()?.getWebDetailRoot()?.toJson()
            this.aeopAeProductProperty = spu.productPropertys?.toJson()
            this.packageHeight = spu.packageHeight?.toString()
            this.packageLength = spu.packageLength?.toString()
            this.packageWidth = spu.packageWidth?.toString()
            this.packageWeight = spu.grossWeight
            this.taxType = spu.taxType?.toIntOrNull()
            this.promiseTemplateId = spu.promiseTemplateId?.toString()
            this.freightTemplateId = spu.freightTemplateId?.toString()
            this.sizechartId = spu.sizechartId?.toString()
            this.msrEuId = spu.msrEuId
            this.manufacturerId = spu.manufacturerId?.toString()
            this.sourceDetailJson = spu.toJson()
        }

        // 先查本地有没有pid记录, 有则直接用SPU_CODE
        val saleGoods = aeSaleGoodsRepository.getByPlatformProductId(aeOriginalProductSpu.productId!!)

        // 获取发货地
        val shipsFormMap = getShipsFromAttr(shop.shopId!!, aeOriginalProductSpu.categoryId!!)

        resultSpu = aeOriginalProductSpu
        aeOriginalProductSpuRepository.upsert(aeOriginalProductSpu)
        // SKU
        val skus = spu.productSkus
        if (CollectionUtils.isNotEmpty(skus)) {
            val skuList = mutableListOf<AeOriginalProductSku>()
            skus?.forEach {
                val sku = AeOriginalProductSku().apply {
                    this.skuId = it.skuId
                    this.id = it.id
                    this.productId = spu.productId
                    this.skuCode = it.skuCode
                    this.extractSpuCode = if (saleGoods != null) saleGoods.spuCode else getSpuCodeBySellerSku(it.skuCode)
                    this.aeopAeProductSku = it.toJson()
                    this.color = it.skuProperties?.filter { it.skuImage.isNotBlank() }?.firstOrNull { it.propertyValueDefinitionName.isNotBlank() }?.propertyValueDefinitionName ?: getColorBySellerSku(it.skuCode)
                    this.colorPropertyValueId = it.skuProperties
                        ?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.colorPropertyId.toInt() }
                        ?.firstOrNull { it.propertyValueId.isNotNull() }?.propertyValueId
                    this.colorPropertyValueName = it.skuProperties
                        ?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.colorPropertyId.toInt() }
                        ?.firstOrNull { it.propertyValueDefinitionName.isNotBlank() }?.propertyValueDefinitionName
                    this.size = getSizeBySellerSku(it.skuCode)
                    this.sizePropertyValueId = it.skuProperties?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.sizePropertyId.toInt() }
                        ?.firstOrNull { it.propertyValueId.isNotNull() }?.propertyValueId

                    this.shipsFromAttributeId = it.skuProperties?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.shipsFromPropertyId.toInt() }?.firstOrNull { it.skuPropertyId.isNotNull() }?.skuPropertyId?.toLong()
                    this.shipsFromAttributeName = "Ships From"
                    this.shipsFromAttributeValueId = it.skuProperties?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.shipsFromPropertyId.toInt() }?.firstOrNull { it.propertyValueId.isNotNull() }?.propertyValueId
                    this.shipsFromAttributeValueName = shipsFormMap[this.shipsFromAttributeValueId]
                    it.skuProperties?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.shipsFromPropertyId.toInt() }?.firstOrNull { it.propertyValueDefinitionName.isNotNull() }?.propertyValueDefinitionName

                    this.skuImage = it.skuProperties?.mapNotNull { it.skuImage }?.joinToString(",")
                    this.currencyCode = it.currencyCode
                    this.skuPrice = it.skuPrice?.toBigDecimal()
                    this.quantity = it.ipmSkuStock?.toInt()
                }
                skuList.add(sku)
            }
            if (CollectionUtils.isNotEmpty(skuList)) {
                resultSkuList = skuList
                // 根据itemId拿到SKU源表内的所有数据, 已表内数据为基准, 比较lzd最新数据, lzd已删除的, 库内要标记为逻辑删除
                val skuListInDb = aeOriginalProductSkuRepository.list(
                    KtQueryWrapper(AeOriginalProductSku::class.java)
                        .eq(AeOriginalProductSku::productId, aeOriginalProductSpu.productId)
                )
                if (CollectionUtils.isNotEmpty(skuListInDb)) {
                    val oldSkuIds = skuListInDb.map { it.skuId }.distinct()
                    val newSkuIds = skuList.map { it.skuId }.distinct()
                    oldSkuIds.forEach { skuId ->
                        if (!newSkuIds.contains(skuId)) {
                            aeOriginalProductSkuRepository.logicDelete(skuId!!)
                        }
                    }
                }
                aeOriginalProductSkuRepository.upsertBatch(skuList)
            }
        }
        return Pair(result, Pair(resultSpu, resultSkuList))
    }

    /**
     * 提取SPU编码
     *
     * @param sellerSku
     * @return
     */
    private fun getSpuCodeBySellerSku(sellerSku: String?): String? {
        if (StringUtils.isNotEmpty(sellerSku)) {
            val strList = sellerSku?.split("-")
            if (strList == null || strList.size == 1) {
                return sellerSku?.trim()
            }
            // 若有3个元素, 则取第1个
            if (strList.size == 3) {
                return strList.first().trim()
            }
            // 若有>3个元素, 则移除后面2个元素, 再拼接 (数码印花格式)
            if (strList.size > 3) {
                return strList.subList(0, strList.size - 2).joinToString("-").trim()
            }
        }
        return sellerSku?.trim()
    }

    /**
     * 提取颜色
     *
     * @param sellerSku
     * @return
     */
    private fun getColorBySellerSku(sellerSku: String?): String {
        if (StringUtils.isNotEmpty(sellerSku)) {
            val strList = sellerSku?.split("-")
            if (strList == null || strList.size == 1) {
                return ""
            }
            // 若有3个元素, 则取第2个
            if (strList.size == 3) {
                return strList[1].replace("_", " ").trim()
            }
            // 若有>3个元素, 则取倒数第2个 (数码印花格式)
            if (strList.size > 3) {
                return strList[strList.size - 2].replace("_", " ").trim()
            }
        }
        return ""
    }

    /**
     * 尺码
     *
     * @param sellerSku
     * @return
     */
    private fun getSizeBySellerSku(sellerSku: String?): String? {
        if (StringUtils.isNotEmpty(sellerSku)) {
            val strList = sellerSku?.split("-")
            if (strList == null || strList.size == 1) {
                return null
            }
            // 若>1个元素, 则取最后一个
            if (strList.size > 1) {
                return strList.last()
            }
        }
        return null
    }

    /**
     * 获取发货地属性
     */
    private fun getShipsFromAttr(shopId: Long, categoryId: Long): Map<Long?, String?> {
        val attributeId = ProductAeAttributeTypeEnum.SHIPS_FROM.getPropertyId(aliexpressProperties)
        val categoryAttributes = aliexpressServiceHelper.getCachedCategoryAttributes(
            shopId,
            categoryId
        )
        val attributeValues = categoryAttributes.result
            ?.attributes
            ?.find {
                it.id == attributeId
            }
            ?.values
        // 转成Map
        return attributeValues?.associate { it.id to it.names?.get("en") } ?: emptyMap()
    }
}