package tech.tiangong.pop.component.export.pending.error.publish

import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.ProductSyncLog
import tech.tiangong.pop.dao.repository.ProductSyncLogRepository
import tech.tiangong.pop.dao.repository.ProductTemplateLazadaSpuRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.PublishErrorExportDto
import tech.tiangong.pop.dto.product.ProductBatchUpdateTitleFailDTO
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.lazada.ProductPendingLazadaPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.FileExportUtils
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 导出上架失败日志-AE待上架
 */
@Component
@Slf4j
class LazadaPublishErrorLogExportComponent(
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val downloadTaskService: DownloadTaskService,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductPendingLazadaPageReq) {
        // 只导出上架失败的数据
        req.taskStateList = listOf(ProductPendingTaskStatusEnum.FAILED.code)
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productTemplateLazadaSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出Lazada待上架_上架失败日志_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.LAZADA_PENDING_PUBLISH_ERROR_TASK,
                req.toJson()
            );
            log.info { "导出上架失败日志-AE待上架-创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出上架失败日志-AE待上架-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("提交导出待上架发布失败任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductPendingLazadaPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        var pageNum: Long = 1
        val pageSize: Long = 1000

        val dataList = mutableListOf<PublishErrorExportDto>()

        while (true) {
            val page = productTemplateLazadaSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                break
            }
            pageNum++

            // 提取待上架SPU ID
            val templateSpuIds = page.records.mapNotNull { it.lazadaSpuId }
            if (templateSpuIds.isEmpty()) {
                continue
            }

            // 查询异常日志
            val logList = productSyncLogRepository.ktQuery()
                .eq(ProductSyncLog::logType, Bool.NO.code)
                .`in`(ProductSyncLog::templateSpuId, templateSpuIds)
                .orderByDesc(ProductSyncLog::createdTime)
                .list()
            if (logList.isEmpty()) {
                continue
            }
            // 筛选出每个templateSpuId对应的最新记录
            val maxLogList = logList
                // 按templateSpuId分组
                .groupBy { it.templateSpuId }
                // 对每个分组内的记录按时间倒序排序
                .mapValues { (_, logs) -> logs.sortedByDescending { it.createdTime } }
                // 取每个分组排序后的第一条（最新记录）
                .mapNotNull { it.value.firstOrNull() }
                // 最终结果可以再按时间排序
                .sortedByDescending { it.createdTime }

            maxLogList.forEach { log ->
                    val logDto = PublishErrorExportDto().apply {
                        this.syncTime = log.createdTime
                        this.platformName = log.platformName
                        this.shopName = log.shopName
                        this.errorMsg = log.errorMsg
                    }
                    dataList.add(logDto)
                }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, ProductBatchUpdateTitleFailDTO::class.java, dataList)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}