package tech.tiangong.pop.component.export

import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformProductUpdateStateEnum
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.ProductSystemUpdateFailLog
import tech.tiangong.pop.dao.repository.ProductSystemUpdateFailLogRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.SystemUpdatePlatformFailDTO
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.ae.ExportByUpdateReq
import tech.tiangong.pop.utils.FileExportUtils
import java.io.File
import java.io.IOException

/**
 * 导出-系统更新日志
 * <AUTHOR>
 * @date 2025-6-2 21:42:24
 */
@Component
@Slf4j
class SystemUpdatePlatformExportComponent(
    private val uploaderOssHelper: UploaderOssHelper,
    private val productSystemUpdateFailLogRepository: ProductSystemUpdateFailLogRepository,
) : DownloadTaskInterface() {

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ExportByUpdateReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 获取数据
        val failLogList = productSystemUpdateFailLogRepository.ktQuery()
            .eq(ProductSystemUpdateFailLog::platformId, req.platform!!.platformId)
            .eq(req.updateState!=null,ProductSystemUpdateFailLog::updateState,req.updateState)
            .between(ProductSystemUpdateFailLog::createdTime, req.createdStartTime, req.createdEndTime)
            .list()

        // 组装excel dto集合
        val dataDtoList = failLogList.map {
            SystemUpdatePlatformFailDTO().apply {
                this.country = it.country
                this.spuCode = it.spuCode
                this.skc = it.skc
                this.color = it.color
                this.tag = it.tagValueNames
                if(it.updateState==null){
                    this.updateState = "未知状态"
                }else{
                    this.updateState = PlatformProductUpdateStateEnum.getByCode(it.updateState!!)?.desc?:"未知状态"
                }
                this.operate = it.operation
                this.failReason = it.failureReason
                this.createdTime = it.createdTime
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, SystemUpdatePlatformFailDTO::class.java, dataDtoList)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
