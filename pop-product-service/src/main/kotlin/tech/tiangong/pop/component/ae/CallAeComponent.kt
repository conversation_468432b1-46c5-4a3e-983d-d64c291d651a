package tech.tiangong.pop.component.ae

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.ofp.open.common.req.SpuItemStatusQueryReq
import tech.tiangong.pop.common.enums.ChannelEnum
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.constant.RedisConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.product.ProductAePropertyValueItemDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.EtaClientExternal
import tech.tiangong.pop.helper.ProductTitleSelectionHelper
import tech.tiangong.pop.req.product.ae.ProductAeExistingPublishReq
import tech.tiangong.pop.req.product.ae.ProductPendingAePlatformUpdateReq
import tech.tiangong.pop.service.product.ProductBarcodeService
import tech.tiangong.pop.service.product.ProductSaleAttributeV2Service
import tech.tiangong.pop.service.product.price.sale.AeSalePricingService
import tech.tiangong.pop.utils.getRootMessage
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ExecutorService


/**
 * 调用Lazada逻辑组件
 * <AUTHOR>
 * @date 2025-4-1 11:13:37
 */
@Component
@Slf4j
class CallAeComponent(
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val lazadaBrandRepository: LazadaBrandRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val productBarcodeService: ProductBarcodeService,
    private val etaClientExternal: EtaClientExternal,
    private val transactionManager: PlatformTransactionManager,
    private val sellerSkuFlatInfoRepository: SellerSkuFlatInfoRepository,
    private val aeUpdateProductComponent: AeUpdateProductComponent,
    private val lockComponent: LockComponent,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val aeProductPriceAlertComponent: AeProductPriceAlertComponent,
    private val productSyncLogRepository: ProductSyncLogRepository,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val productTitleSelectionHelper: ProductTitleSelectionHelper,
    private val aeSalePricingService: AeSalePricingService,
    private val aeProductManageComponent: AeProductManageComponent,
    private val aliexpressProperties: AliexpressProperties,
    private val productSaleAttributeV2Service: ProductSaleAttributeV2Service,
) {

    private val platform = PlatformEnum.AE
    private val channel = ChannelEnum.ALIBABA

    /**
     * 上架新商品-AE
     * @param req
     */
    fun publishProduct(req: ProductPendingAePlatformUpdateReq) {
        val templateSpu = productTemplateAeSpuRepository.getById(req.aeSpuId) ?: throw PublishAscBizException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw PublishAscBizException("商品不存在")
        val shop = shopRepository.getById(templateSpu.shopId) ?: throw PublishAscBizException("商品绑定的店铺不存在")
        val currentUser = CurrentUserHolder.get()

        // 获取SKC模板信息
        val aeSkcList = productTemplateAeSkcRepository.getByAeSpuId(templateSpu.aeSpuId!!)
        // 获取SKU模板信息
        val aeSkcIdList = aeSkcList.mapNotNull { it.aeSkcId }.distinct()
        val aeSkuList = productTemplateAeSkuRepository.listByAeSkcIdList(aeSkcIdList)
        val aeSkuMap = aeSkuList.groupBy { it.aeSkcId }

        // 获取发货期(默认期货)
        val stockTypeEnum = StockTypeEnum.FUTURES
        var delayDeliveryDays: Int? = null
        val etaConfigList = etaClientExternal.getAllEtaConfig()
        etaConfigList
            ?.filter { it.platformId == AE.platformId }
            ?.filter { it.styleType == product.supplyMode }
            ?.forEach { etaConfig ->
                delayDeliveryDays = etaConfig.futures
            }

        //校验品类
        val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
            product.categoryId!!,
            AE.platformId,
            ALIBABA.channelId
        ) ?: throw BusinessException("找不到品类映射信息")

        if (Objects.isNull(categoryMapping.platformCategoryId)) {
            throw PublishAscBizException("找不到平台品类ID")
        }

        // 锁5分钟
        val expiredDuration = Duration.ofMinutes(5)
        lockComponent.lock(RedisConstants.AE_CREATE_PRODUCT_LOCK + templateSpu.spuCode, expiredDuration) {

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->

                // 计算售价
                val priceResp = aeSalePricingService.autoCalPriceByTemplate(templateSpu.aeSpuId!!, listOf(shop.shopId!!))
                if (!priceResp.success) {
                    log.error { "[SPU:${templateSpu.spuCode} 店铺:${shop.shopName}]计算售价失败:${priceResp.errorMessage}" }
                }
                val priceList = priceResp.results

                val now = LocalDateTime.now()
                try {
                    // 获取sale系列的表, 是否已经存在
                    val existSaleGoods = aeSaleGoodsRepository.getByProductIdAndShopId(templateSpu.productId!!, shop.shopId!!)
                    if (existSaleGoods != null) {
                        throw PublishAscBizException("[SPU:${templateSpu.spuCode} 店铺:${shop.shopName}]已上过架")
                    }
                    val lazadaBrand = shop.brandId?.let { lazadaBrandRepository.getById(it.toLong()) }
                    // 创建sale相关表
                    val saleGoods = AeSaleGoods().apply {
                        this.saleGoodsId = IdHelper.getId()
                        this.productId = templateSpu.productId
                        this.publishState = ProductPublishStateEnum.ACTIVE.code
                        this.channelId = channel.channelId
                        this.platformId = platform.platformId
                        this.isHistory = Bool.NO.code
                        this.productTitle = productTitleSelectionHelper.selectTitleForPublish(
                            generatedTitles = templateSpu.getParsedGeneratedTitles(),
                            shopId = shop.shopId!!,
                        ) ?: templateSpu.productTitle
                        this.spuCode = templateSpu.spuCode
                        this.publishTime = now
                        this.latestPublishTime = now
                        this.publishUserId = currentUser.id
                        this.latestPublishUserId = currentUser.id
                        this.publishUserName = currentUser.name
                        this.latestPublishUserName = currentUser.name
                        this.shopId = shop.shopId
                        this.shopName = shop.shopName
                        this.brandId = lazadaBrand?.brandId?.toLong()
                        this.brandName = lazadaBrand?.name
                        this.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                        this.platformCategoryId = categoryMapping.platformCategoryId
                        this.platformCategoryName = categoryMapping.platformCategoryName
                        this.categoryCode = product.categoryCode
                        this.stockType = stockTypeEnum.code
                        this.delayDeliveryDays = delayDeliveryDays
                        this.invDeduction = templateSpu.invDeduction
                        this.originPlaceName = templateSpu.originPlaceName
                        this.packageWeight = templateSpu.packageWeight
                        this.packageDimensionsLength = templateSpu.packageDimensionsLength
                        this.packageDimensionsHeight = templateSpu.packageDimensionsHeight
                        this.packageDimensionsWidth = templateSpu.packageDimensionsWidth
                        this.taxType = templateSpu.taxType
                        this.invDeduction = templateSpu.invDeduction
                        this.freightTemplateId = req.freightTemplateId
                        this.freightTemplateName = req.freightTemplateName
                        this.promiseTemplateId = req.promiseTemplateId
                        this.promiseTemplateName = req.promiseTemplateName
                        this.msrId = req.msrId
                        this.msrName = req.msrName
                        this.manufactureId = req.manufactureId
                        this.manufactureName = req.manufactureName
                        this.hsCode = templateSpu.hsCode
                        this.hsPvList = templateSpu.hsPvList
                        this.hsExtendInfo = templateSpu.hsExtendInfo
                        this.originPropertyValueItems = templateSpu.originPropertyValueItems
                        this.priceCalculateRule = PriceCalculateRuleEnum.V2.code
                        setProductGroupList(req.productGroups)
                    }

                    aeSaleGoodsRepository.save(saleGoods)

                    //商品属性值
                    productSaleAttributeV2Service.insertSaleAttributes(templateSpu.aeSpuId!!, saleGoods.saleGoodsId!!, platform)

                    // 创建sale skc相关表
                    val saveSkcList = mutableListOf<AeSaleSkc>()
                    val saveSkuList = mutableListOf<AeSaleSku>()
                    aeSkcList.forEach { aeSkc ->

                        val saleSkc = AeSaleSkc().apply {
                            this.saleSkcId = IdHelper.getId()
                            this.saleGoodsId = saleGoods.saleGoodsId
                            this.productSkcId = aeSkc.productSkcId
                            this.skc = aeSkc.skc
                            this.color = aeSkc.color
                            this.platformColor = aeSkc.platformColor
                            this.colorCode = aeSkc.colorCode
                            this.colorAbbrCode = aeSkc.colorAbbrCode
                            this.pictures = aeSkc.pictures
                            this.combo = aeSkc.combo
                            this.state = aeSkc.state
                            this.cbPrice = aeSkc.cbPrice
                            this.localPrice = aeSkc.localPrice
                            this.purchasePrice = aeSkc.purchasePrice
                            this.costPrice = aeSkc.costPrice
                        }
                        saveSkcList.add(saleSkc)

                        // req发货地(如果为空, 则默认给一个中国发货地)
                        val shipsFromPropertyValueItemIds = req.shipsFromPropertyValueItems?.map { ships -> ships.attributeValueId } ?: listOf(ProductAePropertyValueItemDTO().apply {
                            this.attributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                            this.attributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                            this.attributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                            this.attributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                        })

                        // 创建sale sku相关表
                        val aeSkuList = aeSkuMap[aeSkc.aeSkcId]
                            // req发货地为空(默认所有), 若不为空, 则只获取对应发货地的sku
                            ?.filter { aeSku -> shipsFromPropertyValueItemIds.isEmpty() || shipsFromPropertyValueItemIds.contains(aeSku.getParsedShipsFromPropertyValueItem()?.attributeValueId) }
                        if (aeSkuList.isNullOrEmpty()) {
                            throw PublishAscBizException("SKC:${aeSkc.skc} SKU信息为空(无设置SKU或发货地不匹配)")
                        }

                        // 复制填充无SKU的店铺的SKU数据
                        val skuIdAeSkuMap = aeSkuList.groupBy { it.shopId }
                        val tmpShopSkuMap = aeProductManageComponent.fillShopSkuData(skuIdAeSkuMap, mapOf(shop.shopId!! to shop))
                        tmpShopSkuMap.values
                            .flatMap { it }
                            .filter { aeSku -> aeSku.shopId == shop.shopId }
                            .forEach { aeSku ->
                                val saleSku = AeSaleSku().apply {
                                    this.saleSkuId = IdHelper.getId()
                                    this.saleGoodsId = saleGoods.saleGoodsId
                                    this.saleSkcId = saleSkc.saleSkcId
                                    this.productId = templateSpu.productId
                                    this.productSkcId = aeSkc.productSkcId
                                    this.stockQuantity = aeSku.stockQuantity
                                    this.sizeName = aeSku.sizeName
                                    this.lzdSizeName = aeSku.sizeName
                                    this.shopName = saleGoods.shopName
                                    this.brandId = saleGoods.brandId
                                    this.brandName = saleGoods.brandName
                                    this.enableState = aeSku.enableState
                                    // ------ 计算价格
                                    // 匹配SKC和shopId的价格集合
                                    val skcShopPriceList = priceList.filter { pl -> pl.skc == saleSkc.skc && pl.shopId == shop.shopId }
                                    // 默认价格
                                    val defaultPrice = skcShopPriceList.find { pl -> pl.isDefaultShipFrom && pl.isDefaultShipToForShipFrom }
                                    // 匹配发货地默认价格
                                    val matchShipFromDefaultPrice = skcShopPriceList.find { pl -> pl.shipFrom == aeSku.shipsFromAttributeValueId.toString() && pl.isDefaultShipToForShipFrom }

                                    // 赋值价格
                                    this.salePrice = aeSku.salePrice ?: matchShipFromDefaultPrice?.salePrice ?: defaultPrice?.salePrice
                                    this.retailPrice = aeSku.retailPrice ?: matchShipFromDefaultPrice?.retailPrice ?: defaultPrice?.retailPrice

                                    val nationalQuoteConfig = aeSku.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                                    if (nationalQuoteConfig != null) {
                                        val newNationalQuoteConfig = nationalQuoteConfig.map { nq ->
                                            // 匹配发货地、目的地价格
                                            val matchShipFromToPrice = skcShopPriceList.find { pl -> pl.shipFrom == aeSku.shipsFromAttributeValueId.toString() && pl.shipTo == nq.shipToCountry }
                                            val dto = AeNationalQuoteConfigDto().apply {
                                                this.shipToCountry = nq.shipToCountry
                                                this.price = nq.price ?: matchShipFromToPrice?.retailPrice ?: matchShipFromDefaultPrice?.retailPrice ?: defaultPrice?.retailPrice
                                                this.defaultFLag = nq.defaultFLag
                                            }
                                            dto
                                        }
                                        if (newNationalQuoteConfig.isNotEmpty()) {
                                            this.nationalQuoteConfig = newNationalQuoteConfig.toJson()
                                        }
                                    }
                                    // ------ 计算价格
                                    this.purchaseSalePrice = aeSku.purchaseSalePrice
                                    this.purchaseRetailPrice = aeSku.purchaseRetailPrice
                                    this.regularSalePrice = aeSku.regularSalePrice
                                    this.regularRetailPrice = aeSku.regularRetailPrice
                                    this.platformCategoryId = saleGoods.platformCategoryId
                                    this.barcode = aeSku.barcode
                                    this.barcodes = aeSku.barcodes
                                    this.platformCategoryName = saleGoods.platformCategoryName
                                    this.publishState = if (aeSku.flagFrontend == Bool.YES.code) ProductPublishStateEnum.ACTIVE.code else ProductPublishStateEnum.IN_ACTIVE.code
                                    this.delayDeliveryDays = saleGoods.delayDeliveryDays
                                    this.sellerSku = aeSku.sellerSku ?: generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, this)
                                    this.shipsFromAttributeId = aeSku.shipsFromAttributeId
                                    this.shipsFromAttributeName = aeSku.shipsFromAttributeName
                                    this.shipsFromAttributeValueId = aeSku.shipsFromAttributeValueId
                                    this.shipsFromAttributeValueName = aeSku.shipsFromAttributeValueName
                                }
                                saveSkuList.add(saleSku)
                            }
                    }
                    if (saveSkcList.isNotEmpty()) {
                        aeSaleSkcRepository.saveBatch(saveSkcList)
                    }
                    if (saveSkuList.isNotEmpty()) {
                        aeSaleSkuRepository.saveBatch(saveSkuList)
                    }

                    // 绑定AE条码
                    bindBarcode(product, saleGoods)

                    val validationResult = aeProductPriceAlertComponent.validateBeforePublish(
                        product = product,
                        saleGoods = saleGoods,
                        saleSkcList = null,
                        saleSkuList = null,
                    )
                    if (!validationResult.valid) {
                        throw BusinessException("商品价格兜底不通过，无法上架：${validationResult.message}")
                    }

                    // 创建/更新 AE商品
                    aeUpdateProductComponent.createOrUpdateProduct(shop.shopId!!, saleGoods, aeSpuId = templateSpu.aeSpuId)
                    // 标记提交成功
                    tagSubmitPlatform(templateSpu.productId!!)

                    // 更新待上架状态
                    productTemplateAeSpuRepository.ktUpdate()
                        .set(ProductTemplateAeSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
                        .set(ProductTemplateAeSpu::taskCompleteTime, LocalDateTime.now())
                        .eq(ProductTemplateAeSpu::aeSpuId, templateSpu.aeSpuId)
                        .update()
                    null
                } catch (e: Exception) {
                    log.error(e) { "事务执行过程中出现异常，触发回滚 ${e.message}" }

                    runAsync(asyncExecutor) {
                        if (e !is PublishGlobalBizException) {
                            productSyncLogRepository.addErrorSyncLog(
                                productId = product.productId!!,
                                templateSpuId = templateSpu.aeSpuId,
                                platformName = platform.platformName,
                                shopId = shop.shopId,
                                shopName = shop.shopName,
                                error = e.getRootMessage(),
                                opType = PlatformOperatorTypeEnum.ACTIVE.code
                            )
                        }

                        // 更新待上架状态
                        productTemplateAeSpuRepository.ktUpdate()
                            .set(ProductTemplateAeSpu::taskStatus, ProductPendingTaskStatusEnum.FAILED.code)
                            .eq(ProductTemplateAeSpu::aeSpuId, templateSpu.aeSpuId)
                            .update()
                    }

                    // 手动标记事务回滚
                    status.setRollbackOnly()
                    null
                }
            }

            // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
            setBarcodeImage(templateSpu.productId!!, shop.shopId!!)
        }
    }

    /**
     * 标记提交平台
     */
    private fun tagSubmitPlatform(productId: Long) {
        // 重新获取最新数据库, 多站点场景
        val newProduct = productRepository.getById(productId)
        var submitPlatformIdList: List<Long>? = newProduct.submitPlatform?.parseJsonList(Long::class.java)
        if (submitPlatformIdList == null) {
            submitPlatformIdList = mutableListOf()
        }
        if (!submitPlatformIdList.contains(AE.platformId)) {
            val updateSubmit = mutableListOf<Long>()
            updateSubmit.addAll(submitPlatformIdList)
            updateSubmit.add(AE.platformId)
            val updateProduct = Product().apply {
                this.productId = newProduct.productId
                this.isSyncPlatform = Bool.YES.code
                this.submitPlatform = updateSubmit.toJson()
            }
            productRepository.updateById(updateProduct)
        }
    }

    /**
     * 绑定AE条码
     * @param product
     * @param saleGoods
     */
    fun bindBarcode(product: Product, saleGoods: AeSaleGoods) {
        val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        saleSkcList.forEach { saleSkc ->
            // 组合商品 且 组合颜色为空, 则生成生成sellerSku用的comoColorCode
            if (saleSkc.combo == Bool.YES.code && saleSkc.comboColorCode.isNullOrBlank()) {
                val comboColorCode = sellerSkuFlatInfoRepository.generateComboColorCode(saleGoods.spuCode!!, saleSkc.colorCode!!)
                saleSkc.comboColorCode = comboColorCode
                aeSaleSkcRepository.updateById(saleSkc)
            }
            val saleSkuList = aeSaleSkuRepository.findBySaleSkcId(saleSkc.saleSkcId!!).filter { it.enableState == Bool.YES.code }
            saleSkuList.forEach { saleSku ->
                if (saleSkc.combo == Bool.YES.code && saleSku.platformSkuId.isNullOrBlank()) {
                    // 组合商品 & 新sku(对于平台), 校验sellerSku是否存在, 存在则重新生成
                    val newSellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, saleSku, saleSkc.comboColorCode)
                    if (!Objects.equals(newSellerSku, saleSku.sellerSku)) {
                        saleSku.sellerSku = newSellerSku
                        aeSaleSkuRepository.updateById(saleSku)
                    }
                }
                productBarcodeService.snapshotSellerSkuToBarcodeByAe(AE.platformId, saleGoods.shopId!!, saleSkc, saleSku)
            }
        }
    }

    /**
     * 更新或新增AE数据
     * @param req 请求参数，包含多个产品和店铺组合以及目标国家
     */
    @Transactional(rollbackFor = [Exception::class])
    fun publishExistingOrAddCountry(req: ProductAeExistingPublishReq) {

        val saleGoodsList = aeSaleGoodsRepository.listByIds(req.saleGoodsIds).takeIf { it.isNotEmpty() }
            ?: throw IllegalArgumentException("销售商品不存在")

        val currentUser = CurrentUserHolder.get()
        saleGoodsList.forEach { saleGoods ->

            val productId = saleGoods.productId!!
            val shopId = saleGoods.shopId!!

            // 获取商品信息
            val product = productRepository.getById(productId) ?: throw PublishAscBizException("商品 ID:$productId 不存在")
            // 查找品类映射
            val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
                product.categoryId!!,
                AE.platformId,
                ALIBABA.channelId
            ) ?: throw PublishAscBizException("该品类未设置关联属性，请在品类管理设置关联")

            if (Objects.isNull(categoryMapping.platformCategoryId)) {
                throw PublishAscBizException("找不到平台品类ID")
            }

            // 远程调用履约，获取发货期信息
            val etaReqList = listOf(SpuItemStatusQueryReq().apply {
                this.spuCode = saleGoods.spuCode
                this.platformId = AE.platformId
            })
            val etaRespList = etaClientExternal.querySpuItemStatus(etaReqList)
            if (!etaRespList.isNullOrEmpty()) {
                val etaResp = etaRespList.firstOrNull() ?: throw PublishAscBizException("获取发货期信息失败")
                // 更新SALE_SPU发货信息
                saleGoods.stockType = etaResp.stockType
                saleGoods.delayDeliveryDays = etaResp.delayDeliveryDays
            } else {
                log.warn { "获取发货期信息失败，商品ID:$productId, SPU:${saleGoods.spuCode}" }
            }

            aeSaleGoodsRepository.updateById(AeSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.publishState = ProductPublishStateEnum.ACTIVE.code
                this.latestPublishTime = LocalDateTime.now()
                this.latestPublishUserId = currentUser.id
                this.latestPublishUserName = currentUser.name
                this.stockType = saleGoods.stockType
                this.delayDeliveryDays = saleGoods.delayDeliveryDays
            })

            // 更新所有SKU的发货期信息 - 批量更新提高性能
            val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
            val updateSaleSkuList = saleSkuList.map { sku ->
                AeSaleSku().apply {
                    this.saleSkuId = sku.saleSkuId
                    this.publishState = if (sku.enableState == Bool.YES.code) {
                        ProductPublishStateEnum.ACTIVE.code
                    } else {
                        sku.publishState
                    }
                    this.delayDeliveryDays = saleGoods.delayDeliveryDays
                }
            }
            if (updateSaleSkuList.isNotEmpty()) {
                aeSaleSkuRepository.updateBatchById(updateSaleSkuList)
            }

            // 创建/更新 AE商品
            aeUpdateProductComponent.createOrUpdateProduct(shopId, aeSaleGoodsRepository.getById(saleGoods.saleGoodsId!!))
        }
    }

    /**
     * 修复barcode关系表的skc图片
     * (后续需要改造成定时任务修复, 通过flat空图片的数据, when 平台回填图片)
     */
    fun setBarcodeImage(productId: Long, shopId: Long) {
        // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
        val existSaleGoods = aeSaleGoodsRepository.getByProductIdAndShopId(productId, shopId)
        if (existSaleGoods != null) {
            // 批量获取所有SKC和SKU信息
            val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(existSaleGoods.saleGoodsId!!)
            val skcMap = saleSkcList.associateBy { it.saleSkcId }
            val skcIds = saleSkcList.mapNotNull { it.saleSkcId }
            val saleSkuList = aeSaleSkuRepository.findBySaleSkcIds(skcIds)

            // 生成 flatPictureMap
            val flatPictureMap = saleSkuList.asSequence()
                .mapNotNull { saleSku ->
                    val flatId = saleSku.sellerSkuFlatId
                    val saleSkc = skcMap[saleSku.saleSkcId]
                    val picture = saleSkc?.pictures?.split(",")?.firstOrNull()
                    if (saleSkc != null && flatId != null && picture != null) {
                        flatId to picture
                    } else {
                        null
                    }
                }
                .toMap()

            // 批量获取 flatInfo 列表
            if (flatPictureMap.isEmpty()) {
                log.error { "修复回填条码图片, 为空" }
                return
            }
            val flatInfoList = sellerSkuFlatInfoRepository.listByIds(flatPictureMap.keys)

            // 批量更新 flatInfo 信息
            val updatedFlatInfoList = flatInfoList.mapNotNull { flatInfo ->
                val picture = flatPictureMap[flatInfo.sellerSkuFlatId]
                if (picture != null) {
                    flatInfo.apply { image = picture }
                } else {
                    null
                }
            }
            if (updatedFlatInfoList.isNotEmpty()) {
                sellerSkuFlatInfoRepository.updateBatchById(updatedFlatInfoList)
            }
        }
    }
}
