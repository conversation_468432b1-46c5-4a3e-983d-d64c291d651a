package tech.tiangong.pop.component.ae

import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationDTO
import tech.tiangong.pop.dto.product.ProductTitleConfigGenerationItemDTO
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.external.DictClientExternal
import tech.tiangong.pop.helper.ImagePackCollectionHelper
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.image.ImagePackCollectionResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeDetailDateResp
import tech.tiangong.pop.resp.product.center.ProductAttributesV2Vo
import tech.tiangong.pop.service.category.PublishCategoryAttributeService
import tech.tiangong.pop.service.settings.ProductTitleConfigService

/**
 * AE待上架逻辑组件
 */
@Slf4j
@Component
class AeProductManageComponent(
    private val shopRepository: ShopRepository,
    private val productTemplateAeSkcRepository: ProductTemplateAeSkcRepository,
    private val productTemplateAeSkuRepository: ProductTemplateAeSkuRepository,
    private val regionPriceRuleLogisticsRepository: RegionPriceRuleLogisticsRepository,
    private val dictClientExternal: DictClientExternal,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val imagePackCollectionHelper: ImagePackCollectionHelper,
    private val productTitleConfigService: ProductTitleConfigService,
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
    private val productTemplateAttributesV2Repository: ProductTemplateAttributesV2Repository,
    private val publishPlatformAttrRepository: PublishPlatformAttrRepository,
    private val publishCategoryAttributeService: PublishCategoryAttributeService,
) {
    private val platformEnum = AE

    /**
     * AE待上架-详情
     */
    fun detailV2(templateSpu: ProductTemplateAeSpu, product: Product): ProductPendingAeDetailDateResp {

        return ProductPendingAeDetailDateResp().apply {
            // 获取所有AE店铺
            val shopAeAllList = shopRepository.ktQuery()
                .eq(Shop::platformId, platformEnum.platformId)
                .eq(Shop::isAuth, Bool.YES.code)
                .list()
            val shopMap = shopAeAllList.associateBy { it.shopId!! }

            // 获取OPS默认店铺
            val shopDictVo = dictClientExternal.getTopByDictCode(DictEnum.AE_LIST_REG)

            this.aeSpuId = templateSpu.aeSpuId
            this.productTitle = templateSpu.productTitle
            this.aiTitleStatus = templateSpu.aiTitleStatus
            this.brandId = templateSpu.brandId
            this.brandName = templateSpu.brandName
            this.categoryId = product.categoryId
            val platformCategory = getPlatformCategory(product, platformEnum.platformId)
            this.categoryMappingId = platformCategory?.categoryMappingId
            this.platformCategoryName = platformCategory?.platformCategoryName
            this.platformName = platformEnum.platformName
            this.sizeGroupName = templateSpu.sizeGroupName
            this.sizeGroupCode = templateSpu.sizeGroupCode
            this.invDeduction = templateSpu.invDeduction
            this.originPlaceName = templateSpu.originPlaceName
            this.packageDimensionsLength = templateSpu.packageDimensionsLength
            this.packageDimensionsWidth = templateSpu.packageDimensionsWidth
            this.packageDimensionsHeight = templateSpu.packageDimensionsHeight
            this.packageWeight = templateSpu.packageWeight
            this.taxType = templateSpu.taxType

            //商品属性值
            val attrs = productTemplateAttributesV2Repository.listByTemplateId(templateSpu.aeSpuId!!)
            this.productAttributeValueList = attrs.map { attr ->
                ProductAttributesV2Vo().apply {
                    BeanUtils.copyProperties(attr, this)
                }
            }

            if (platformCategory != null) {
                //已关联的属性值
                this.attributeMapperList = publishPlatformAttrRepository.listPlatformAttributeMappingByCategoryMappingId(platformCategory.categoryMappingId!!)
                //平台属性
                this.platformAttributeList = publishCategoryAttributeService.listPlatformCategoryAttribute(platformEnum, platformCategory.platformCategoryId!!, platformCategory.country!!)
                //品类属性选项
                val platformAttrs = publishPlatformAttrRepository.listByCategoryMappingIdAndPlatformId(platformCategory.categoryMappingId!!, platformEnum.platformId)
                if (platformAttrs.isNotEmpty()) {
                    val attrIds = platformAttrs.map { it.platformAttrId!! }
                    this.categoryAttributeList = publishCategoryAttrRepository.listAttributesByCategoryId(product.categoryId!!, Bool.YES.code)
                        .filter { it -> attrIds.contains(it.attributeId) }  // 有该平台映射的属性
                }
            }

            // 为待上架商品构建图包集合列表（获取平台下的全部规则）
            this.imagePackCollections = getImagesV2(product.spuCode!!, platformEnum)
            this.hsCode = templateSpu.hsCode
            this.hsPvList = templateSpu.getParsedHsPvList()
            this.hsExtendInfo = templateSpu.hsExtendInfo

            this.originPropertyValueItems = templateSpu.getParsedOriginPropertyValueItems()
            this.generatedTitleMissingFieldsJson = templateSpu.getParsedGeneratedTitleMissingFields()
            this.generatedTitleOverLengthFlag = templateSpu.generatedTitleOverLengthFlag
            this.generatedTitlesJson = buildCompatibleTitlesJson(platformEnum, templateSpu.getParsedGeneratedTitles(), templateSpu.productTitle)

            val skcAeList = productTemplateAeSkcRepository.listByAeSpuId(templateSpu.aeSpuId!!)
                .filter { skcAe ->
                    skcAe.state == Bool.YES.code
                }
            if (CollectionUtils.isNotEmpty(skcAeList)) {
                val aeSkcIdList = skcAeList.mapNotNull { it.aeSkcId }
                val skuAeList = productTemplateAeSkuRepository.listByAeSkcIdList(aeSkcIdList).filter { skuAe -> skuAe.enableState == Bool.YES.code }
                val skuMapBySkc = skuAeList.groupBy { it.aeSkcId }

                val shopIds = skuAeList.mapNotNull { s -> s.shopId }.toSet()
                if (shopIds.isEmpty()) {
                    log.warn { "查询AE详情: sku店铺为空" }
                    return@apply
                }

                val logisticsRuleConfigMap = regionPriceRuleLogisticsRepository.getAeDefaultLogisticsCostConfig()

                this.skcList = skcAeList.map { skcAe ->
                    ProductPendingAeDetailDateResp.ProductPendingAeSkcResp().apply {
                        this.aeSkcId = skcAe.aeSkcId
                        this.aeSpuId = skcAe.aeSpuId
                        this.skc = skcAe.skc
                        this.color = skcAe.color
                        this.colorCode = skcAe.colorCode
                        this.platformColor = skcAe.platformColor
                        this.pictures = skcAe.pictures
                        this.cbPrice = skcAe.cbPrice
                        this.localPrice = skcAe.localPrice
                        this.purchasePrice = skcAe.purchasePrice
                        this.costPrice = skcAe.costPrice
                        this.combo = skcAe.combo
                        val skuIdAeList = skuMapBySkc[skcAe.aeSkcId]
                        val skuIdAeSkuMap = skuIdAeList?.groupBy { it.shopId } ?: emptyMap()

                        // 复制填充无SKU的店铺的SKU数据
                        val tmpShopSkuMap = fillShopSkuData(skuIdAeSkuMap, shopMap)
                        this.skuList = tmpShopSkuMap.values
                            .flatMap { it }
                            .map { skuAe ->
                                ProductPendingAeDetailDateResp.ProductPendingAeSkuResp().apply {
                                    this.aeSkuId = skuAe.aeSkuId
                                    this.aeSkcId = skuAe.aeSkcId
                                    this.aeSpuId = skuAe.aeSpuId
                                    this.shopId = skuAe.shopId
                                    this.shopName = shopMap[skuAe.shopId]?.shopName
                                    this.defaultShopFlag = if (shopDictVo?.children?.find { sd -> sd.dictName == this.shopName } != null) {
                                        Bool.YES.code
                                    } else {
                                        Bool.NO.code
                                    }
                                    this.sizeName = skuAe.sizeName
                                    this.sellerSku = skuAe.sellerSku
                                    this.barcode = skuAe.barcode
                                    this.barcodes = skuAe.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                    this.flagFrontend = skuAe.flagFrontend
                                    this.stockQuantity = skuAe.stockQuantity
                                    this.salePrice = skuAe.salePrice
                                    this.retailPrice = skuAe.retailPrice
                                    this.lastSalePrice = skuAe.lastSalePrice
                                    this.lastRetailPrice = skuAe.lastRetailPrice
                                    this.nationalQuoteConfigList = skuAe.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                                        ?.map { nqc ->
                                            AeNationalQuoteConfigDto().apply {
                                                this.shipToCountry = nqc.shipToCountry
                                                this.price = nqc.price
                                                this.defaultFLag = Bool.NO.code
                                                // 发货地-目的地 获取配置, 得到是否默认
                                                val config = logisticsRuleConfigMap[skuAe.shipsFromAttributeValueId]
                                                if (config != null && config.receivingPlace == nqc.shipToCountry) {
                                                    this.defaultFLag = Bool.YES.code
                                                }
                                            }
                                        }
                                    this.purchaseSalePrice = skuAe.purchaseSalePrice
                                    this.purchaseRetailPrice = skuAe.purchaseRetailPrice
                                    this.regularSalePrice = skuAe.regularSalePrice
                                    this.regularRetailPrice = skuAe.regularRetailPrice
                                    this.enable = skuAe.enableState
                                    this.shipsFromPropertyValueItem = skuAe.getParsedShipsFromPropertyValueItem()
                                }
                            }
                    }
                }
            }
        }
    }

    /**
     * 复制填充店铺SKU数据
     * @param skuIdAeSkuMap 待上架SKU数据 key=shopId value=待上架SKU集合
     * @param shopMap   店铺Map key=shopId value=店铺
     * @return 店铺SKU数据 key=shopId value=店铺SKU集合(里面包含了skuIdAeSkuMap, 加上新填充的SKU) 新SKU没有id主键
     */
    fun fillShopSkuData(skuIdAeSkuMap: Map<Long?, List<ProductTemplateAeSku>>, shopMap: Map<Long, Shop>): Map<Long, List<ProductTemplateAeSku>> {
        // 复制填充没有待上架SKU的店铺的SKU数据
        // key=shopId value=待上架SKU集合
        val tmpShopSkuMap = mutableMapOf<Long, List<ProductTemplateAeSku>>()
        shopMap.keys.forEach { tmpShopId ->
            if (skuIdAeSkuMap.containsKey(tmpShopId)) {
                // sku存在这个shop, 不处理
                tmpShopSkuMap.put(tmpShopId, skuIdAeSkuMap[tmpShopId] ?: emptyList())
            } else {
                // 取出第一个shop的sku, 复制
                val tmpSkuList = skuIdAeSkuMap[skuIdAeSkuMap.keys.first()]
                val tmpResultSkuList = tmpSkuList?.map { skuAe ->
                    skuAe.copy(
                        aeSkuId = null,
                        shopId = tmpShopId,
                        salePrice = null,
                        lastSalePrice = null,
                        retailPrice = null,
                        lastRetailPrice = null,
                        nationalQuoteConfig = null,
                        purchaseSalePrice = null,
                        purchaseRetailPrice = null,
                        regularSalePrice = null,
                        regularRetailPrice = null
                    )
                }
                tmpShopSkuMap.put(tmpShopId, tmpResultSkuList ?: emptyList())
            }
        }
        // 填充目的地
        // 获取物流配置(发货地-目的地)
        val logisticsRuleConfigMap = regionPriceRuleLogisticsRepository.getAeLogisticsCostConfig()
        // 循环sku的发货地, 匹配物流配置, 拿到所有目的地
        tmpShopSkuMap.forEach { shopId, skuList ->
            skuList.forEach { skuAe ->
                // 物流配置
                val configDtoList = logisticsRuleConfigMap[skuAe.shipsFromAttributeValueId]
                if (configDtoList.isNotEmpty()) {
                    // SKU上的区域定价配置
                    val naConfig = skuAe.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                    if (naConfig.isNotEmpty()) {
                        val newNaConfig = mutableListOf<AeNationalQuoteConfigDto>()
                        // 转为map 方便比较
                        val naConfigMap = naConfig?.associateBy { it.shipToCountry }

                        // 循环物流配置
                        configDtoList?.forEach { configDto ->
                            // 比较SKU是否有不存在的目的地, 若不存在, 则复制, 填充物流配置对应的目的地(发货地对应)
                            if (naConfigMap?.containsKey(configDto.receivingPlace) == true) {
                                // 存在, add
                                newNaConfig.add(naConfigMap[configDto.receivingPlace]!!)
                            } else {
                                // 不存在, 新增
                                newNaConfig.add(AeNationalQuoteConfigDto().apply {
                                    this.shipToCountry = configDto.receivingPlace
//                                    this.price =
                                    this.defaultFLag = configDto.defaultRule ?: Bool.NO.code
                                })
                            }
                        }
                        skuAe.nationalQuoteConfig = newNaConfig.toJson()
                    }
                }
            }
        }
        return tmpShopSkuMap
    }

    /**
     * 获取映射的平台品类
     *
     * @param product
     * @param platformId
     * @return
     */
    private fun getPlatformCategory(product: Product, platformId: Long): PublishCategoryMapping? {
        if (product.categoryId != null) {
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryIdAndPlatformId(product.categoryId!!, platformId)
            if (categoryMapping != null) {
                return categoryMapping
            }
        }
        return null
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     * @param platformEnum
     */
    private fun getImagesV2(spuCode: String, platformEnum: PlatformEnum): List<ImagePackCollectionResp> {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return imagePackCollectionHelper.buildImageCollectionsForPending(
            platformId = platformEnum.platformId,
            images = imageRepository?.imageUrls?.parseJsonList(ImageAniVo::class.java) ?: emptyList(),
            spuCode = spuCode
        )
    }

    /**
     * 创建兼容历史数据的标题生成数据
     * 如果generatedTitles为空，则构建一个兼容对象
     */
    private fun buildCompatibleTitlesJson(
        platform: PlatformEnum,
        existingTitles: ProductTitleConfigGenerationDTO?,
        fallbackTitle: String?,
        fallbackTitleEn: String? = null,
    ): ProductTitleConfigGenerationDTO {
        val config = productTitleConfigService.findByPlatformId(platform.platformId)
        val configId = config?.productTitleConfigId
        val count = config?.titleCount ?: 1

        return existingTitles?.apply {
            productTitleConfigId = configId
            titleCount = count
        } ?: ProductTitleConfigGenerationDTO(
            productTitleConfigId = configId,
            titleCount = count,
            titles = listOf(
                ProductTitleConfigGenerationItemDTO(
                    index = 0,
                    title = fallbackTitle,
                    titleEn = fallbackTitleEn,
                )
            ),
            lastGeneratedTime = null
        )
    }
}