package tech.tiangong.pop.component.ae

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import jakarta.annotation.Resource
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.BooleanUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.ProductConstant.AE_REVERSE_SIZE_MAP
import tech.tiangong.pop.common.constant.ProductConstant.AE_SIZE_MAP
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto
import tech.tiangong.pop.common.enums.*
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.config.AliexpressProperties.AePlatformConfig
import tech.tiangong.pop.constant.ImageConstants.FileName.SEP
import tech.tiangong.pop.constant.ImageConstants.Url
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.constant.RedisConstants.AE_UPDATE_PRODUCT_SYNC_LOCK
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.mq.MessageRecord
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.AePlatformNationalQuoteConfigDto
import tech.tiangong.pop.dto.image.ImagePackCollectionDTO
import tech.tiangong.pop.dto.product.AeProductUpdateOption
import tech.tiangong.pop.dto.product.ProductAeGroupDTO
import tech.tiangong.pop.dto.product.PublishPlatformAttributeDTO
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImagePackCollectionHelper
import tech.tiangong.pop.helper.ProductPublishAeHelper
import tech.tiangong.pop.helper.ProductPublishHelper
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.req.sdk.ae.*
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressProductQueryResponse
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressSellerRelationResponse
import tech.tiangong.pop.resp.sdk.aliexpress.Attribute
import tech.tiangong.pop.service.AliexpressService
import tech.tiangong.pop.service.mq.MessageRecordService
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService
import tech.tiangong.pop.utils.AssertUtils.requireBusiness
import tech.tiangong.pop.utils.ImageUtils
import tech.tiangong.pop.utils.getRootMessage
import tech.tiangong.sdp.common.req.ProductOnlineNoticeReq
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.util.concurrent.ExecutorService

/**
 * AE速卖通创建/更新商品
 */
@Slf4j
@Component
class AeUpdateProductComponent(
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val inspirationClientExternal: InspirationClientExternal,
    private val messageRecordService: MessageRecordService,
    private val aliexpressService: AliexpressService,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productPublishAeHelper: ProductPublishAeHelper,
    private val aliexpressServiceHelper: AliexpressServiceHelper,
    private val aliexpressProperties: AliexpressProperties,
    private val imagePackCollectionHelper: ImagePackCollectionHelper,
    private val productOperateLogRepository: ProductOperateLogRepository,
    private val lockComponent: LockComponent,
    private val transactionManager: PlatformTransactionManager,
    private val currencyExchangeRateService: CurrencyExchangeRateService,
    private val productSaleAttributesV2Repository: ProductSaleAttributesV2Repository,
) {
    companion object {
        const val IMAGE_URLS_DELIMITER = ";"

        // 匹配非法字符的正则表达式
        private val INVALID_CHARS_REGEX = Regex("[^a-zA-Z0-9\\s\\p{L}\\-_]")

        // 匹配连续空白字符的正则表达式
        private val MULTIPLE_SPACES_REGEX = Regex("\\s+")
    }

    fun testConfig(): AePlatformConfig {
        return aliexpressProperties.aePlatform
    }

    fun testUpdate(saleGoodsId: Long) {
        val saleGoods = aeSaleGoodsRepository.getById(saleGoodsId)!!
        createOrUpdateProduct(saleGoods.shopId!!, saleGoods)
    }

    /**
     * 创建/更新商品
     */
    fun createOrUpdateProduct(
        shopId: Long,
        saleGoods: AeSaleGoods,
        option: AeProductUpdateOption = AeProductUpdateOption(),
        aeSpuId: Long? = null,
    ) {
        val logPrefix = "[AeProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId}"

        log.info { "$logPrefix - 开始创建/更新商品" }

        // 构造唯一锁 key
        val lockKey = AE_UPDATE_PRODUCT_SYNC_LOCK + saleGoods.saleGoodsId

        lockComponent.doInLock(
            key = lockKey,
            inLockAction = {
                log.info { "$logPrefix - 获取锁成功" }
                createOrUpdateProductInner(shopId = shopId, saleGoods = saleGoods, option = option, aeSpuId = aeSpuId)
            },
            unLockAction = {
                log.warn { "$logPrefix - 获取锁失败，可能存在并发操作" }
                // 你可以抛异常，也可以直接 return（建议业务语义明确，抛出更合理）
                throw BusinessException("并发操作冲突，请勿重复提交。$logPrefix")
            }
        )
    }

    /**
     * 更新价格
     */
    fun updatePrice(saleGoods: AeSaleGoods, shop: Shop): List<String> {
        val errorMsgList = mutableListOf<String?>()
        val logPrefix = "[AeProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        // 找出每个spu下的sku
        val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (saleSkuList.isNotEmpty()) {
            // 通过pid分组
            saleSkuList
                .filter { it.platformProductId != null }
                .groupBy { it.platformProductId }
                .forEach { (platformProductId, saleSkuList) ->
                    try {// 批量更新价格
                        productOperateLogRepository.saveProductOperateLog("编辑库存/价格", PlatformOperatorTypeEnum.MODIFY_PRICE, saleGoods)

                        // 处理AE币种
                        val aliexpressCurrencyType = aliexpressServiceHelper.getCachedSellerRelations(shop.shopId!!).sellerRelationList?.firstOrNull()?.channelCurrency
                            ?: aliexpressProperties.aePlatform.currencyCode // 默认CNY
                        if (CurrencyEnum.USD.code.equals(aliexpressCurrencyType, ignoreCase = true)) {
                            // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                            val exchangeRate = getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code) ?: throw BusinessException("获取CNY兑换USD汇率失败")
                            saleSkuList.forEach { sku ->
                                sku.retailPrice = sku.retailPrice!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                            }
                        }

                        val priceMap = HashMap<String, String>()
                        saleSkuList
                            .filter { saleSku -> saleSku.shopSku.isNotBlank() }
                            .forEach { saleSku ->
                                priceMap[saleSku.shopSku!!] = saleSku.retailPrice?.toString() ?: ""
                            }
                        if (priceMap.isNotEmpty()) {
                            aliexpressService.updateProductSkuPrices(shop.token!!, platformProductId!!, priceMap)

                            // 判断如果ae_sale_goods状态为审核失败, 调用接口成功后改为审核中
                            if (saleGoods.publishState == ProductAePublishStateEnum.REFUSE.code) {
                                aeSaleGoodsRepository.updatePublishState(saleGoods.saleGoodsId!!, ProductAePublishStateEnum.AUDITING)
                            }
                        }
                    } catch (e: Exception) {
                        log.error(e) { "${logPrefix}更新价格失败, platformProductId: ${platformProductId}, saleSkuList: ${saleSkuList.toJson()}" }
                        errorMsgList.add(e.message)
                    }
                }
        }
        return errorMsgList.mapNotNull { it }
    }

    /**
     * 更新库存
     * @return 异常信息
     */
    fun updateStock(saleGoods: AeSaleGoods, shop: Shop): List<String> {
        val errorMsgList = mutableListOf<String?>()
        val logPrefix = "[AeProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        // 找出每个spu下的sku
        val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (saleSkuList.isNotEmpty()) {
            // 通过pid分组
            saleSkuList
                .filter { it.platformProductId != null }
                .groupBy { it.platformProductId }
                .forEach { (platformProductId, saleSkuList) ->
                    try {// 批量更新库存
                        productOperateLogRepository.saveProductOperateLog("编辑库存/价格", PlatformOperatorTypeEnum.MODIFY_STOCK, saleGoods)
                        val stockMap = HashMap<String, Long>()
                        saleSkuList
                            .filter { saleSku -> saleSku.shopSku.isNotBlank() }
                            .forEach { saleSku ->
                                stockMap[saleSku.shopSku!!] = saleSku.stockQuantity ?: 0L
                            }
                        if (stockMap.isNotEmpty()) {
                            aliexpressService.updateProductSkuStocks(shop.token!!, platformProductId!!, stockMap)

                            // 判断如果ae_sale_goods状态为审核失败, 调用接口成功后改为审核中
                            if (saleGoods.publishState == ProductAePublishStateEnum.REFUSE.code) {
                                aeSaleGoodsRepository.updatePublishState(saleGoods.saleGoodsId!!, ProductAePublishStateEnum.AUDITING)
                            }
                        }
                    } catch (e: Exception) {
                        log.error(e) { "${logPrefix}更新库存失败, platformProductId: $platformProductId" }
                        errorMsgList.add(e.message)
                    }
                }
        }
        return errorMsgList.mapNotNull { it }
    }

    /**
     * 批量下架
     */
    fun batchOffline(saleGoodsIds: List<Long>) {
        val saleGoodsList = aeSaleGoodsRepository.listByIds(saleGoodsIds)?.toList()
        if (saleGoodsList.isNullOrEmpty()) {
            throw IllegalArgumentException("销售商品不存在")
        }
        val currentUser = CurrentUserHolder.get()
        saleGoodsList
            .groupBy { it.shopId }
            .forEach { (shopId, offlineSaleGoodsList) ->
                // 获取店铺token
                val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")
                // 手动事务
                TransactionTemplate(transactionManager).execute { status ->
                    try {

                        // 调用第三方下架接口(SPU)
                        val offlinePidList = offlineSaleGoodsList.mapNotNull { it.platformProductId.toString() }.distinct()
                        val offlineResp = aliexpressService.offlineAeProduct(shop.token!!, offlinePidList)
                        val errorPidList = offlineResp.result?.errorDetails?.mapNotNull { it.productIds }?.flatten()?.distinct() ?: emptyList()

                        val now = LocalDateTime.now()

                        // SPU改为下架
                        val successSaleGoodsList = offlineSaleGoodsList
                            .filter { saleGoods -> saleGoods.platformProductId != null }
                            .filter { saleGoods -> !errorPidList.contains(saleGoods.platformProductId) }
                            .map { saleGoods ->
                                saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                                saleGoods.latestOfflineTime = now
                                saleGoods.latestOfflineUserId = currentUser.id
                                saleGoods.latestOfflineUserName = currentUser.name
                                saleGoods
                            }
                        if (successSaleGoodsList.isNotEmpty()) {
                            aeSaleGoodsRepository.updateBatchById(successSaleGoodsList)
                            // SKU改为下架
                            val offlineSaleGoodsIdList = successSaleGoodsList.mapNotNull { it.saleGoodsId }.distinct()
                            val offlineSaleSkuList = aeSaleSkuRepository.findBySaleGoodsIds(offlineSaleGoodsIdList)
                            offlineSaleSkuList.forEach { saleSku ->
                                saleSku.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                                saleSku.latestOfflineTime = now
                            }
                            aeSaleSkuRepository.updateBatchById(offlineSaleSkuList)

                            successSaleGoodsList.forEach { saleGoods ->
                                productOperateLogRepository.saveProductOperateLog("批量下架", PlatformOperatorTypeEnum.INACTIVE, saleGoods)
                            }
                        }
                        null
                    } catch (e: Exception) {
                        log.error(e) { "事务执行过程中出现异常，触发回滚 ${e.message}" }
                        // 手动标记事务回滚
                        status.setRollbackOnly()
                        null
                    }
                }
            }
    }

    /**
     * 创建/更新商品
     */
    private fun createOrUpdateProductInner(
        shopId: Long,
        saleGoods: AeSaleGoods,
        option: AeProductUpdateOption = AeProductUpdateOption(),
        aeSpuId: Long? = null,
    ) {
        val logPrefix = "[AeProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        aeSaleGoodsRepository.updateSyncState(saleGoods.saleGoodsId!!, PlatformSyncStateEnum.PROCESSING)

        val productId = saleGoods.productId!!
        val product = productRepository.getById(productId) ?: throw BusinessException("未设置商品Product!")
        val spuCode = saleGoods.spuCode!!
        val saleGoodsId = saleGoods.saleGoodsId!!
        var requestParams: String? = null
        var respParams: String? = null
        var opType: Int? = null

        try {
            val shop = shopRepository.getById(shopId) ?: throw BusinessException("店铺不存在 shopId=$shopId")
            requireBusiness(shop.token.isNotBlank()) { "${shop.shopName} 店铺没有 token" }

            saleGoods.productTitle.takeIf { it.isNotBlank() } ?: throw BusinessException("商品标题不能为空")
            saleGoods.hsCode.takeIf { it.isNotBlank() } ?: throw BusinessException("商品税号不能为空")

            val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoodsId)
                .filter { it.state == Bool.YES.code }
                .takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKC!")

            val saleSkuList = aeSaleSkuRepository.ktQuery()
                .`in`(AeSaleSku::saleSkcId, saleSkcList.map { it.saleSkcId }.toSet())
                .eq(AeSaleSku::enableState, Bool.YES.code)
                .list().toList().takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKU!")

            //查询商品属性
//            val platformAttributes = productAttributesRepository
//                .findPlatformAttributes(product.categoryId!!, AE.platformId, productId)
//                .filter { k -> StringUtils.isNoneBlank(k.platformAttributeCode, k.platformAttributeKeyName) }
//                .takeIf { it.isNotEmpty() }
//                ?: throw BusinessException("属性不能为空，请在品类管理关联属性，或检查平台属性是否正确设置")
            val platformAttributes = productSaleAttributesV2Repository
                .findPlatformAttributes(product.categoryId!!, AE.platformId,saleGoods.saleGoodsId!!)
                .filter { k -> StringUtils.isNoneBlank(k.platformAttributeCode, k.platformAttributeKeyName) }
                .takeIf { it.isNotEmpty() }
                ?: throw BusinessException("属性不能为空，请在品类管理关联属性，或检查平台属性是否正确设置")
            //校验品类
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(product.categoryId!!, AE.platformId, ALIBABA.channelId)
                ?: throw BusinessException("找不到品类映射信息")

            val platformCategoryId = categoryMapping.platformCategoryId?.toLong() ?: throw BusinessException("找不到关联平台品类ID")
            saleGoods.apply {
                if (this.platformCategoryId != platformCategoryId.toString()) this.platformCategoryId = platformCategoryId.toString()
                if (platformCategoryName != categoryMapping.platformCategoryName) platformCategoryName = categoryMapping.platformCategoryName
            }

            validateSaleSku(saleSkcList, saleSkuList)

            val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
                ?: throw BusinessException("图库不存在，请先上传图片")
            validateImageData(imageRepository)

            // 创建同步上下文对象
            val ctx = ProductSyncContext(
                logPrefix = logPrefix,
                product = product,
                saleGoods = saleGoods,
                shop = shop,
                imageRepository = imageRepository,
            )

            validatePlatformProductStatus(ctx)

            ctx.picture = getPicture(ctx)
            // 上传图片到速卖通
            ctx.imageCollection = uploadImgToAliexpress(ctx, saleSkcList.mapNotNull { it.colorCode }.toSet())

            val newSkcList = saleSkcList.map { skc ->
                skc.pictures = ImageUtils.filterImagesBySkc(
                    images = ctx.requiredImageCollection.skcImages,
                    spuCode = spuCode,
                    colorCode = skc.colorCode!!
                ).firstOrNull()?.ossImageUrl

                skc
            }

            validateSkcPictures(newSkcList)

            aeSaleSkcRepository.updateBatchById(
                newSkcList.map { AeSaleSkc().apply { saleSkcId = it.saleSkcId; pictures = it.pictures } }
            )

            ctx.aliexpressSellerRelationResponse = aliexpressServiceHelper.getCachedSellerRelations(shopId)
            ctx.aliexpressCurrencyType = ctx.requiredSellerRelationResponse.sellerRelationList?.firstOrNull()?.channelCurrency
                ?: aliexpressProperties.aePlatform.currencyCode // 默认CNY

            val platformProductId = saleGoods.platformProductId
            // 发布或更新商品
            if (platformProductId == null) {
                // 创建商品
                opType = PlatformOperatorTypeEnum.ACTIVE.code
                val aliexpressProductInfo = buildAliexpressProductInfo(
                    ctx = ctx,
                    platformCategoryId = platformCategoryId,
                    skcList = newSkcList,
                    skuList = saleSkuList,
                    platformAttributes = platformAttributes,
                )
                requestParams = aliexpressProductInfo.toJson()

                val createResponse = aliexpressService.postProduct(productId, ctx.shopToken, aliexpressProductInfo)

                respParams = createResponse.toJson()
                val newPlatformProductId = createResponse.getProductId()
                if (!createResponse.isSuccess() || newPlatformProductId == null) {
                    val errorMsg = "调用速卖通创建商品异常:${createResponse.getJoinErrorMsg()}"
                    throw BusinessException(errorMsg)
                }

                // 更新saleGoods信息
                saleGoods.platformProductId = newPlatformProductId
                saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                saleGoods.publishState = ProductAePublishStateEnum.AUDITING.code

                runAsync(asyncExecutor) { pushToAIDC(ctx) }
            } else {
                opType = PlatformOperatorTypeEnum.UPDATE_PRODUCT.code

                // 考虑编辑可能触发审批，修改分组不会触发审批，故提前设置分组
                if (option.callSetGroupApi) {
                    saleGoods.getProductGroupList()
                        ?.let { setProductGroups(ctx, platformProductId, it) }
                }

                val aliexpressProductInfo = buildAliexpressProductInfo(
                    ctx = ctx,
                    platformCategoryId = platformCategoryId,
                    skcList = newSkcList,
                    skuList = saleSkuList,
                    platformAttributes = platformAttributes,
                    isEdit = true,
                )
                aliexpressProductInfo.productId = platformProductId
                requestParams = aliexpressProductInfo.toJson()

                val updateResponse = aliexpressService.editProduct(productId, ctx.shopToken, aliexpressProductInfo)

                respParams = updateResponse.toJson()
                if (!updateResponse.isSuccess()) {
                    val errorMsg = "调用速卖通更新商品异常:${updateResponse.getJoinErrorMsg()}"
                    throw BusinessException(errorMsg)
                }

                saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                saleGoods.publishState = ProductAePublishStateEnum.AUDITING.code
            }

            // 更新图包规则相关字段
            updateImagePackRuleFields(ctx)

            aeSaleGoodsRepository.updateById(saleGoods)
            // 更新product状态
            updateProductState(ctx)

            //发布后记录发布时使用的图包版本
            updateImageVersionNumAfterPublish(saleGoods.productId!!)

            runAsync(asyncExecutor) { updateSkuId(ctx, saleSkuList) }
            runAsync(asyncExecutor) { handleSuccessSyncLog(ctx, requestParams, respParams, opType) }
            runAsync(asyncExecutor) { pushPublishImage(ctx, newSkcList) }
        } catch (e: Exception) {
            runAsync(asyncExecutor) {
                handleFailure(product, saleGoods, requestParams, respParams, opType, e.getRootMessage(), logPrefix, aeSpuId)
            }
            throw PublishGlobalBizException(e)
        }
    }


    fun updateImageVersionNumAfterPublish(productId: Long) {
        val product = productRepository.getById(productId)
        if (product.spuCode != null && product.spuCode.isNotBlank()) {
            val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
            if (imageRepository != null) {
                val updateProduct = Product()
                updateProduct.productId = product.productId
                updateProduct.imageVersionNum = imageRepository.versionNum
                if (product.imagePackageState == ProductImagePackageStateEnum.UPDATED.code) {
                    updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                }
                productRepository.updateById(updateProduct)
            }
        }

    }

    /**
     * 构建速卖通商品信息
     *
     * @param ctx 同步上下文
     * @param platformCategoryId 平台类目ID
     * @param skcList SKC列表
     * @param skuList SKU列表
     * @param platformAttributes 平台属性列表
     * @return 速卖通商品信息请求对象
     */
    private fun buildAliexpressProductInfo(
        ctx: ProductSyncContext,
        platformCategoryId: Long,
        skcList: List<AeSaleSkc>,
        skuList: List<AeSaleSku>,
        platformAttributes: List<PublishPlatformAttributeDTO>,
        isEdit: Boolean = false,
    ): AliexpressProductInfoRequest {
        val saleGoods = ctx.saleGoods

        log.info { "${ctx.logPrefix}开始构建速卖通商品信息" }

        return AliexpressProductInfoRequest().apply {
            // 1. 基本信息
            this.categoryId = platformCategoryId

            // 税号
            this.hscode = HsCode().apply {
                this.hsCode = saleGoods.hsCode
                this.pvList = saleGoods.getParsedHsPvList()?.map {
                    HsCodePropertyValueItem(
                        propertyId = it.propertyId,
                        propertyName = it.propertyName,
                        valueId = it.valueId,
                        valueName = it.valueName
                    )
                }
                this.extendInfo = saleGoods.hsExtendInfo
            }.toJson()

            // 多语言标题
            listOf(
                Subject(
                    locale = aliexpressProperties.aePlatform.locale,
                    value = saleGoods.productTitle!!
                )
            ).also { this.subjectList = it }

            this.locale = aliexpressProperties.aePlatform.locale // 默认英文

            // 图片URL列表，从图片库中获取
            val imageCollection = ctx.requiredImageCollection
            this.imageURLs =
                imageCollection.distinctLimitedMainImages()
                    .joinToString(IMAGE_URLS_DELIMITER) { it.ossImageUrl!! }
            this.marketImages = imageCollection.marketingImagesAe

            val categoryAttributes = aliexpressServiceHelper.getCachedCategoryAttributes(ctx.shop.shopId, platformCategoryId)
            if (!categoryAttributes.isSuccess() || categoryAttributes.result == null) {
                throw BusinessException("获取AE速卖通类目属性失败，请检查类目ID是否正确或联系管理员")
            }
            val categoryAttributeMap = categoryAttributes.result.attributes
                ?.associateBy { it.id.toString() }!!
            // 产品属性转换为AE要求的格式
            this.productPropertys = buildProductProperties(ctx, categoryAttributeMap, platformAttributes)

            // 2. 价格和库存
            this.productUnit = aliexpressProperties.aePlatform.productUnit // 默认为"件/个" (piece/pieces)
            this.isPackSell = aliexpressProperties.aePlatform.isPackSell // 默认非打包销售
            this.currencyCode = ctx.requiredAliexpressCurrencyType
            // SKU信息
            if (CurrencyEnum.USD.code.equals(this.currencyCode, ignoreCase = true)) {
                // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                val exchangeRate = getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code) ?: throw BusinessException("获取CNY兑换USD汇率失败")
                skuList.forEach { sku ->
                    sku.retailPrice = sku.retailPrice!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                }
            }
            this.productPrice = skuList.maxByOrNull { it.retailPrice!! }?.retailPrice.toString()

            this.productSKUs = buildProductSKUs(ctx, skcList, skuList, categoryAttributeMap)

            // 国家区域报价配置
            val nationalQuoteConfigList = this.productSKUs?.flatMap { ps ->
                ps.aePlatformNationalQuoteConfigDtoList ?: emptyList()
            }
            if (nationalQuoteConfigList.isNotEmpty()) {
                this.nationalQuoteConfig = NationalQuoteConfig().apply {
                    // 配置数据
                    this.configurationData = nationalQuoteConfigList?.map {
                        val priceMap = mutableMapOf<String, String>()
                        it.absoluteQuoteMap?.forEach { (key, value) ->
                            if (CurrencyEnum.USD.code.equals(currencyCode, ignoreCase = true) && value.isNotBlank()) {
                                // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                                val exchangeRate = getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code) ?: throw BusinessException("获取CNY兑换USD汇率失败")
                                priceMap.put(key, value.toBigDecimal().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP).toString())
                            } else {
                                priceMap.put(key, value)
                            }
                        }
                        AePlatformNationalQuoteConfigDto(
                            shiptoCountry = it.shiptoCountry,
                            absoluteQuoteMap = priceMap
                        )
                    }?.toJson()
                    // 配置类型
                    this.configurationType = "absolute"
                }
            }

            // 3. 商品详情
            this.detailSourceList = buildDetailSources(ctx)

            // 4. 包装和物流
            this.deliveryTime = saleGoods.delayDeliveryDays ?: aliexpressProperties.aePlatform.delayDeliveryDays
            this.grossWeight = saleGoods.packageWeight ?: aliexpressProperties.aePlatform.grossWeight // 默认重量
            this.lotNum = aliexpressProperties.aePlatform.lotNum // 默认每包1件

            // 包装尺寸
            this.packageHeight = saleGoods.packageDimensionsHeight?.toInt() ?: aliexpressProperties.aePlatform.packageHeight
            this.packageLength = saleGoods.packageDimensionsLength?.toInt() ?: aliexpressProperties.aePlatform.packageLength
            this.packageWidth = saleGoods.packageDimensionsWidth?.toInt() ?: aliexpressProperties.aePlatform.packageWidth
            this.packageType = aliexpressProperties.aePlatform.packageType // 非打包销售

            this.freightTemplateId = saleGoods.freightTemplateId
            this.promiseTemplateId = saleGoods.promiseTemplateId

            if (!isEdit) {
                // 编辑接口，无法设置分组，需单独走设置分组的接口，所以编辑不做设置
                saleGoods.getProductGroupList()
                    ?.singleOrNull()
                    ?.let { group ->
                        this.groupId = group.childGroupId ?: group.parentGroupId
                    }
            }

            // 5. 其他设置
            this.reduceStrategy = ProductAeInventoryDeductStrategyEnum.getByInvDeduction(saleGoods.invDeduction)?.code
                ?: aliexpressProperties.aePlatform.reduceStrategy // 默认支付减库存

            this.manufacturerId = saleGoods.manufactureId
            this.msrEuId = saleGoods.msrId?.toString()

            this.taxType = saleGoods.taxType?.toString()
        }
    }

    /**
     * 将平台属性转换为速卖通产品属性
     *
     * @param ctx 同步上下文
     * @param categoryAttributeMap 类目属性映射，key为属性ID字符串
     * @param platformAttributes 平台属性列表
     * @return 转换后的速卖通产品属性列表
     */
    private fun buildProductProperties(
        ctx: ProductSyncContext,
        categoryAttributeMap: Map<String, Attribute>,
        platformAttributes: List<PublishPlatformAttributeDTO>,
    ): List<ProductProperty> {
        log.info { "${ctx.logPrefix}开始转换平台属性到速卖通产品属性，属性数量: ${platformAttributes.size}" }

        // 记录已处理的属性ID，用于后续检查必填属性
        val processedAttributeIds = mutableSetOf<Long>()

        // 处理必填属性
        val aeProductProperties = buildRequiredProductProperties(
            ctx = ctx,
            categoryAttributeMap = categoryAttributeMap,
            processedAttributeIds = processedAttributeIds
        )

        processedAttributeIds.addAll(aeProductProperties.map { it.attrNameId!!.toLong() }.toSet())
        processedAttributeIds.addAll(listOf(aliexpressProperties.aePlatform.colorPropertyId, aliexpressProperties.aePlatform.sizePropertyId))

        addInputTypeRequiredAttributesIfConfigured(
            ctx = ctx,
            categoryAttributeMap = categoryAttributeMap,
            processedAttributeIds = processedAttributeIds,
            aeProductProperties = aeProductProperties
        )

        // 将平台属性转换为产品属性
        platformAttributes.forEach { platformAttr ->
            // 使用安全调用和let来简化空检查
            val attrKeyName = platformAttr.platformAttributeKeyName!!
            val attrCode = platformAttr.platformAttributeCode!!

            // 查找并处理类目属性
            categoryAttributeMap[attrKeyName]?.let { attribute ->
                processedAttributeIds.add(attribute.id!!)

                // 根据属性展示类型判断是否需要查找属性值
                val isInputType = attribute.attributeShowTypeValue?.let {
                    aliexpressProperties.aePlatform.attributeInputTypeValue.contains(it)
                } ?: false

                // 查找属性值
                val attributeValue = if (!isInputType) {
                    attribute.values?.find { it.id == attrCode.toLong() }
                        ?: run {
                            log.warn { "${ctx.logPrefix}未找到属性值ID对应的类目属性值: $attrCode, 属性ID: $attrKeyName" }
                            return@forEach
                        }
                } else null

                // 构建产品属性
                val aeProperty = ProductProperty(
                    attrNameId = attribute.id.toInt(),
                    attrValueId = attrCode.toLongOrNull(),
                    attrName = attribute.getName() ?: attribute.id.toString(),
                    attrValue = attributeValue?.getName() ?: platformAttr.platformAttributeValue ?: ""
                ).apply {
                    // 处理材质比例
                    attribute.features?.get("AE_FEATURE_material_ratio")?.takeIf { it.isNotBlank() }?.let {
                        // 根据属性类型设置百分比
                        percent = when {
                            attribute.attributeShowTypeValue == "radio" || attribute.values?.size == 1 -> 100
                            else -> platformAttr.platformAttributeValue
                                ?.substringAfterLast(" ", "")
                                ?.removeSuffix("%")
                                ?.toIntOrNull() ?: 100
                        }

                        log.info { "${ctx.logPrefix}设置属性百分比: ${attribute.getName()} = $percent%" }
                    }
                }

                aeProductProperties.add(aeProperty)
            } ?: log.warn { "${ctx.logPrefix}未找到属性ID对应的类目属性: $attrKeyName" }
        }

        // 检查必填属性是否都已包含
        checkRequiredAttributes(categoryAttributeMap, processedAttributeIds, ctx)

        // 返回去重后的属性列表
        return aeProductProperties.distinctBy { it.attrNameId }
    }

    /**
     * 构建商品SKU列表
     */
    private fun buildProductSKUs(
        ctx: ProductSyncContext,
        skcList: List<AeSaleSkc>,
        skuList: List<AeSaleSku>,
        categoryAttributeMap: Map<String, Attribute>?,
    ): List<ProductSKU> {
        log.info { "${ctx.logPrefix}构建商品SKU，SKC数量: ${skcList.size}, SKU数量: ${skuList.size}" }

        // 固定的属性ID
        val colorPropertyId = aliexpressProperties.aePlatform.colorPropertyId
        val sizePropertyId = aliexpressProperties.aePlatform.sizePropertyId

        // 获取颜色和尺寸属性
        val colorAttribute = categoryAttributeMap?.get(colorPropertyId.toString())
        val sizeAttribute = categoryAttributeMap?.get(sizePropertyId.toString())

        // 用于存储已分配的属性值映射，避免重复的同时确保相同颜色/尺寸使用相同的属性值
        val colorToValueMap = mutableMapOf<String, AttributeValueResult>()
        val sizeToValueMap = mutableMapOf<String, AttributeValueResult>()

        // 跟踪已使用的属性值ID
        val usedColorValueIds = mutableSetOf<Long>()
        val usedSizeValueIds = mutableSetOf<Long>()

        // 预处理：确保每个独特的平台颜色都有一个对应的属性值
        skcList.forEach { skc ->
            val platformColor = skc.platformColor.orEmpty()
            if (platformColor.isNotBlank() && platformColor !in colorToValueMap) {
                // 优先使用SKC中已存在的colorPropertyValueId
                val colorValueMapping = if (skc.colorPropertyValueId != null) {
                    log.info { "${ctx.logPrefix} 使用SKC中已存在的颜色属性值ID: ${skc.colorPropertyValueId} for color: $platformColor" }
                    AttributeValueResult(
                        id = skc.colorPropertyValueId!!,
                    ).also {
                        usedColorValueIds.add(skc.colorPropertyValueId!!)
                    }
                } else {
                    // 如果SKC中没有colorPropertyValueId，则查询匹配的属性值
                    log.info { "${ctx.logPrefix} SKC中没有颜色属性值ID，开始查询匹配: $platformColor" }

                    findBestAttributeValueMatch(
                        attribute = colorAttribute,
                        valueToMatch = platformColor,
                        usedValueIds = usedColorValueIds
                    )?.also { result ->
                        usedColorValueIds.add(result.id)
                        // 更新SKC，保存查询到的颜色属性值ID
                        aeSaleSkcRepository.updateById(
                            AeSaleSkc().apply {
                                saleSkcId = skc.saleSkcId
                                colorPropertyValueId = result.id
                            }
                        )
                        log.info { "${ctx.logPrefix} 已更新SKC颜色属性值ID: ${result.id} for SKC: ${skc.saleSkcId}" }
                    } ?: throw BusinessException("找不到平台颜色属性值: $platformColor")
                }

                colorToValueMap[platformColor] = colorValueMapping
            }
        }

        // 预处理：收集所有尺码并为每个独特的尺码分配属性值
        val allSizeNames = skuList.mapNotNull { it.sizeName }.distinct()
        allSizeNames.forEach { sizeName ->
            if (sizeName !in sizeToValueMap) {
                // 检查是否有SKU已经有了sizePropertyValueId
                val skuWithSizeId = skuList.find { it.sizeName == sizeName && it.sizePropertyValueId != null }

                val sizeValueMapping = if (skuWithSizeId != null) {
                    log.info { "${ctx.logPrefix} 使用SKU中已存在的尺码属性值ID: ${skuWithSizeId.sizePropertyValueId} for size: $sizeName" }
                    AttributeValueResult(
                        id = skuWithSizeId.sizePropertyValueId!!,
                    ).also {
                        usedSizeValueIds.add(skuWithSizeId.sizePropertyValueId!!)
                    }
                } else {
                    // 如果没有SKU有sizePropertyValueId，则查询匹配的属性值
                    log.info { "${ctx.logPrefix} SKU中没有尺码属性值ID，开始查询匹配: $sizeName" }

                    findBestSizeAttributeMatch(
                        attribute = sizeAttribute,
                        sizeToMatch = sizeName,
                        usedValueIds = usedSizeValueIds
                    )?.also { result ->
                        usedSizeValueIds.add(result.id)
                        // 批量更新所有相同尺码的SKU
                        val skusToUpdate = skuList.filter { it.sizeName == sizeName && it.sizePropertyValueId == null }
                        if (skusToUpdate.isNotEmpty()) {
                            skusToUpdate.forEach { sku ->
                                aeSaleSkuRepository.updateById(
                                    AeSaleSku().apply {
                                        saleSkuId = sku.saleSkuId
                                        sizePropertyValueId = result.id
                                    }
                                )
                            }
                            log.info { "${ctx.logPrefix} 已更新${skusToUpdate.size}个SKU的尺码属性值ID: ${result.id} for size: $sizeName" }
                        }
                    } ?: throw BusinessException("找不到平台尺寸值：$sizeName")
                }

                sizeToValueMap[sizeName] = sizeValueMapping
            }
        }

        // 构建最终的SKU列表
        return skcList.flatMap { skc ->
            val skusForThisSkc = skuList.filter { it.saleSkcId == skc.saleSkcId }
            requireNotNull(skc.colorCode) { "${ctx.logPrefix} colorCode 不能为空" }
            val skcImages = skc.pictures?.split(Url.DELIM)?.filter { it.trim().isNotBlank() } ?: emptyList()
            val platformColor = skc.platformColor.orEmpty()

            // 获取此颜色已分配的属性值，必定存在
            val colorValueMapping = colorToValueMap[platformColor]
                ?: throw BusinessException("${ctx.logPrefix} 系统错误：颜色映射丢失 - $platformColor")

            skusForThisSkc.mapNotNull { sku ->
                val sizeName = sku.sizeName ?: return@mapNotNull null

                // 获取此尺码已分配的属性值，必定存在
                val sizeValueMapping = sizeToValueMap[sizeName]
                    ?: throw BusinessException("${ctx.logPrefix} 系统错误：尺码映射丢失 - $sizeName")

                // 构建SKU属性
                val colorProperty = SKUProperty(
                    skuPropertyId = colorPropertyId.toInt(),
                    skuImage = skcImages.firstOrNull()?.takeIf { it.isNotBlank() } ?: throw BusinessException("${skc.colorCode} skc图片不能为空"),
                    propertyValueId = colorValueMapping.id,
                    propertyValueDefinitionName = sanitizePropertyValueName(platformColor),
                )

                val sizeProperty = SKUProperty(
                    skuPropertyId = sizePropertyId.toInt(),
                    propertyValueId = sizeValueMapping.id
                )

                // 发货地
                val shipFromProperty: SKUProperty? = sku.getParsedShipsFromPropertyValueItem()
                    ?.takeIf { it.isNotNull() }?.let {
                        // 中国则忽略
                        if (it.attributeValueId != aliexpressProperties.aePlatform.shipsFromPropertyValueId) {
                            SKUProperty(
                                skuPropertyId = it.attributeId!!.toInt(),
                                propertyValueId = it.attributeValueId,
                                propertyValueDefinitionName = it.attributeValueName
                            )
                        } else null
                    }

                // sku需要考虑spec，目前升序顺序是 颜色 尺码 发货地
                val skuProperties = listOfNotNull(colorProperty, sizeProperty, shipFromProperty)
                // 拼接SKU销售属性ID
                val tmpId = skuProperties.joinToString(";") { property -> "${property.skuPropertyId}:${property.propertyValueId}" }
                // 处理区域定价
                val aePlatformNationalQuoteConfigDtoList = mutableListOf<AePlatformNationalQuoteConfigDto>()
                val skuPriceConfigList = sku.nationalQuoteConfig?.parseJsonList(AeNationalQuoteConfigDto::class.java)
                if (skuPriceConfigList.isNotEmpty()) {
                    skuPriceConfigList?.forEach { config ->
                        val dto = AePlatformNationalQuoteConfigDto().apply {
                            this.shiptoCountry = config.shipToCountry
                            this.absoluteQuoteMap = mutableMapOf<String, String>().apply {
                                put(tmpId, config.price.toString())
                            }
                        }
                        aePlatformNationalQuoteConfigDtoList.add(dto)
                    }
                }

                ProductSKU(
                    skuProperties = skuProperties,
                    currencyCode = ctx.requiredAliexpressCurrencyType,
                    ipmSkuStock = sku.stockQuantity ?: aliexpressProperties.aePlatform.defaultStockQuantity,
                    skuPrice = sku.retailPrice.toString(),
                    skuCode = sku.sellerSku,
                    aePlatformNationalQuoteConfigDtoList = aePlatformNationalQuoteConfigDtoList
                ).apply {
//                    sku.platformSkuId?.takeIf { it.isNotBlank() }?.let {
//                        skuId = it.toLong() // TODO 不传递会怎样?
//                    }
                    sku.shopSku?.takeIf { it.isNotBlank() }?.let {
                        id = sku.shopSku
                    }
                }
            }
        }
    }

    /**
     * 查找最佳匹配的属性值
     * 使用多级匹配策略，优先精确匹配
     */
    private fun findBestAttributeValueMatch(
        attribute: Attribute?,
        valueToMatch: String,
        usedValueIds: Set<Long> = emptySet(),
    ): AttributeValueResult? {
        if (attribute == null || valueToMatch.isBlank()) return null

        val valueLower = valueToMatch.lowercase()

        // 尝试精确匹配（不区分大小写）
        attribute.values?.find {
            it.id !in usedValueIds && it.getName()?.lowercase() == valueLower
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 如果值包含加号，尝试用第一部分进行匹配
        if (valueLower.contains("+")) {
            val firstPart = valueLower.split("+")[0].trim()
            if (firstPart.isNotBlank()) {
                attribute.values?.find {
                    it.getName()?.lowercase() == firstPart && it.id !in usedValueIds
                }?.let {
                    return AttributeValueResult(it.id!!)
                }
            }
        }

        // 尝试包含匹配
        attribute.values?.find { value ->
            value.id !in usedValueIds && value.getName()?.lowercase()?.contains(valueLower) == true
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 尝试反向包含匹配
        attribute.values?.find { value ->
            val name = value.getName()
            value.id !in usedValueIds && name != null && name.isNotBlank() && valueLower.contains(name.lowercase())
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 如果所有匹配方式都失败，但还有可用属性值，随机选择一个未使用的
        attribute.values?.filter { it.id !in usedValueIds }?.let { availableValues ->
            if (availableValues.isNotEmpty()) {
                // 随机选择一个未使用的属性值
                val randomValue = availableValues.random()
                return AttributeValueResult(randomValue.id!!, isCustomValue = true)
            }
        }

        // 如果所有属性值都已使用，无法分配新的值
        return null
    }

    /**
     * 构建商品详情
     */
    private fun buildDetailSources(
        ctx: ProductSyncContext,
    ): List<DetailSource> {
        val mobileDetail = buildDetailHtml(ctx.requiredImageCollection, true)
        val webDetail = buildDetailHtml(ctx.requiredImageCollection, false)

        return listOf(
            DetailSource(
                locale = aliexpressProperties.aePlatform.locale,
                mobileDetail = mobileDetail,
                webDetail = webDetail
            )
        )
    }

    /**
     * 构建详情HTML
     */
    private fun buildDetailHtml(imageCollection: ImagePackCollectionDTO, isMobile: Boolean): String {
        val moduleList = mutableListOf<Map<String, Any>>()

        // 添加图片模块
        val imageUrls = imageCollection.detailImageList.map { it.ossImageUrl }

        if (imageUrls.isNotEmpty()) {
            val imagesModule = mapOf(
                "type" to "image",
                "images" to imageUrls.map { url ->
                    mapOf(
                        "url" to url,
                        "style" to mapOf(
                            "width" to 700
                        )
                    )
                }
            )
            moduleList.add(imagesModule)
        }

        val detailObject = mapOf(
            "version" to "2.0.0",
            "moduleList" to moduleList
        )

        return detailObject.toJson()
    }

    /**
     * 上传图片到速卖通
     *
     * @param ctx 同步上下文
     * @param skcCodes SKC颜色编码列表
     * @return 图片集合DTO
     */
    private fun uploadImgToAliexpress(
        ctx: ProductSyncContext,
        skcCodes: Set<String>,
    ): ImagePackCollectionDTO {
        log.info { "${ctx.logPrefix}上传图片到速卖通，shopId: ${ctx.shop.shopId}, 图片数量: ${skcCodes.size}" }

        val picture = ctx.requiredPicture
        val imageAniVos = ctx.requiredImageRepository.imageUrls!!.parseJsonList(ImageAniVo::class.java)
        val saleGoods = ctx.saleGoods

        // 使用新的图包规则助手构建图片集合（已上架商品）
        val imagePackCollectionResp = imagePackCollectionHelper.buildImageCollectionForPublished(
            platformId = AE.platformId,
            shopId = ctx.shop.shopId!!,
            images = imageAniVos,
            spuCode = ctx.spu,
            currentRuleId = saleGoods.imagePackRuleId,
            currentRuleVersion = saleGoods.imagePackRuleVersion
        )

        // 如果规则发生变更，更新AeSaleGoods的规则信息
        if (BooleanUtils.isTrue(imagePackCollectionResp.imagePackRuleChanged)) {
            log.info { "${ctx.logPrefix}图包规则发生变更，更新规则信息" }
            saleGoods.imagePackRuleId = imagePackCollectionResp.ruleId
            saleGoods.imagePackRuleVersion = imagePackCollectionResp.ruleVersion
        }

        // 处理SKC图
        var skcImages = ImageUtils.filterImagesBySkc(imageAniVos, skcCodes)
        if (skcImages.isNotEmpty()) {
            if (ctx.spu.contains(SEP)) {
                val flowers = ctx.spu.split(SEP)[1]
                // 过滤掉花色图，但好像是老逻辑，后续看业务是否还需要，因为可能文件名会用SPU拼接
                skcImages = skcImages.filterNot { it.orgImgName!!.startsWith(flowers) }
            }
        }

        // 转换为内部DTO进行处理
        val imageCollection = ImagePackCollectionDTO().apply {
            // 复制字段
            this.ruleId = imagePackCollectionResp.ruleId
            this.ruleVersion = imagePackCollectionResp.ruleVersion
            this.ruleName = imagePackCollectionResp.ruleName
            this.shops = imagePackCollectionResp.shops
            this.mainUrlList = imagePackCollectionResp.mainUrlList
            this.detailImageList = imagePackCollectionResp.detailImageList
            this.detailAppImageList = imagePackCollectionResp.detailAppImageList
            this.marketingImages = imagePackCollectionResp.marketingImages
            this.skcImages = skcImages
        }

        // 使用新的图包规则助手上传图片
        val aeImgList = productPublishAeHelper.checkImageAndUpdateToAe(
            imageCollection,
            ctx.requiredImageRepository,
            ctx.shop,
            picture.flowerUrl
        )
        if (aeImgList.isEmpty()) {
            throw BusinessException("图库上传至平台失败，请检查图片格式及像素大小，或联系管理员")
        }

        return imagePackCollectionHelper.updateProductImagesAndBuildCollection(
            platformId = AE.platformId,
            shopId = ctx.shop.shopId!!,
            spuCode = ctx.spu,
            images = aeImgList,
            picture = picture,
            skcCodes = skcCodes,
        )
    }

    /**
     * 获取图片资料
     *
     * @param ctx 同步上下文
     * @return 产品图片信息
     */
    private fun getPicture(ctx: ProductSyncContext): ProductPicture {
        val productId = ctx.product.productId!!
        val spuCode = ctx.spu
        val imageRepository = ctx.requiredImageRepository

        val picture = productPictureRepository.getOneByProductId(productId)
        if (picture == null) {
            val productPicture = ProductPicture()
            productPicture.productId = productId
            productPicture.spuCode = spuCode
            productPicture.sourceImageUrl = imageRepository.mainUrl
            productPictureRepository.save(productPicture)
            return productPicture
        } else {
            picture.sourceImageUrl = imageRepository.imageUrls
            productPictureRepository.updateById(picture)
        }
        return picture
    }

    /**
     * 校验图片资料
     */
    private fun validateImageData(imageRepository: ImageRepository) {
        val imgList = imageRepository.imageUrls!!.parseJsonList(ImageAniVo::class.java)
        val imgOrgList = imgList.mapNotNull { it.orgImgName }
        val imageNoList = ImageUtils.extractTypeCodesFromNames(imgOrgList)
        if (CollectionUtils.isEmpty(imageNoList)) {
            throw BusinessException("无上传图片资料!")
        }
        val loseImage = ImageUtils.missingImageTypeCodes(imageNoList)
        if (StringUtils.isNotBlank(loseImage)) {
            throw BusinessException("缺少" + loseImage + "图片资源")
        }
    }

    /**
     * 校验SKU资料
     */
    private fun validateSaleSku(saleSkcList: List<AeSaleSkc>, saleSkuList: List<AeSaleSku>) {
        // 收集所有错误信息
        val errorMessages = mutableListOf<String>()

        // 检查SKC颜色是否有重复
        saleSkcList.groupBy { it.platformColor }
            .filter { (platformColor, skcs) -> platformColor != null && skcs.size > 1 }
            .forEach { (platformColor, _) ->
                errorMessages.add("平台颜色 [$platformColor] 在多个SKC中重复使用")
            }
        // 检查SKC颜色是否为空
        saleSkcList.filter { it.platformColor.isNullOrBlank() }
            .forEach { skc ->
                errorMessages.add("SKC ID [${skc.saleSkcId}] 未设置平台颜色")
            }

        // 创建SKC映射，用于后续验证
        val saleSkcMap = saleSkcList.associateBy { it.saleSkcId }

        // 按SKC分组SKU，用于校验尺码重复
        val skusBySkc = saleSkuList.groupBy { it.saleSkcId }

        // 检查每个颜色组中尺码是否重复
        skusBySkc.forEach { (skcId, skusWithSameColor) ->
            val colorName = saleSkcMap[skcId]?.platformColor!!
            val skc = saleSkcMap[skcId]?.skc

            // 检查尺码重复
            skusWithSameColor.groupBy { Pair(it.sizeName, it.shipsFromAttributeValueId) }
                .filter { (_, skus) -> skus.size > 1 }
                .forEach { (size, _) ->
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的尺码 [$size] 重复使用")
                }

            // 检查每个SKU
            skusWithSameColor.forEach { sku ->
                val sizeName = sku.sizeName

                // 检查尺码是否为空
                if (sizeName.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的SKU缺少尺码名称")
                }
                if (sku.retailPrice == null) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 未设置划线价格")
                } else if (sku.retailPrice!! <= BigDecimal.ZERO) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 划线价格必须大于 0")
                }
                if (sku.stockQuantity?.let { it <= 0 } == true) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置库存数量")
                }
                if (sku.sellerSku.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置sellerSku")
                }
            }

        }

        // 如果有错误，抛出异常
        if (errorMessages.isNotEmpty()) {
            throw BusinessException(errorMessages.joinToString("\n"))
        }
    }

    /**
     * 校验平台商品状态
     *
     * @param ctx 同步上下文
     */
    private fun validatePlatformProductStatus(ctx: ProductSyncContext) {
        val saleGoods = ctx.saleGoods
        val platformProductId = saleGoods.platformProductId

        if (platformProductId != null) {
            // 查询速卖通商品信息状态
            val queryResponse = aliexpressService.queryProductStatus(ctx.shopToken, platformProductId)

            if (!queryResponse.isSuccess() || queryResponse.result == null) {
                throw BusinessException("调用速卖通查询商品异常:${queryResponse.getJoinErrorMsg()}")
            }

            if (ProductAeAuditStatusEnum.AUDITING.value.equals(queryResponse.result.status, true)) {
                throw BusinessException("商品正在审核中，请稍后再试")
            }
        }
    }

    /**
     * 校验SKC图片
     */
    private fun validateSkcPictures(skcList: List<AeSaleSkc>) {
        val missingPictureColorCodes = skcList.filter { it.pictures.isNullOrBlank() }.map { it.colorCode }
        if (missingPictureColorCodes.isNotEmpty()) {
            val errorMessages = missingPictureColorCodes.joinToString("\n") { "颜色 [$it] 缺少图片" }
            requireBusiness(false) { errorMessages }
        }
    }

    /**
     * 检查必填属性是否都已处理
     *
     * @param categoryAttributeMap 类目属性映射
     * @param processedAttributeIds 已处理的属性ID集合
     * @param ctx 同步上下文
     */
    private fun checkRequiredAttributes(
        categoryAttributeMap: Map<String, Attribute>,
        processedAttributeIds: Set<Long>,
        ctx: ProductSyncContext,
    ) {
        // 查找所有必填但未处理的属性
        val missingRequiredAttributes = categoryAttributeMap.values
            .filter { it.isNotNull() && it.required == true && it.id !in processedAttributeIds }
            .map { it.getName() ?: it.id.toString() }

        if (missingRequiredAttributes.isNotEmpty()) {
            val errorMsg = StringBuilder("${ctx.logPrefix}以下必填属性未提供值：\n")
            missingRequiredAttributes.forEachIndexed { index, attrName ->
                errorMsg.append("${index + 1}. $attrName\n")
            }

            log.error { errorMsg.toString() }
            throw BusinessException(errorMsg.toString())
        }
    }

    /**
     * 更新商品状态
     *
     * @param ctx 同步上下文
     */
    private fun updateProductState(ctx: ProductSyncContext) {
        val product = ctx.product
        val saleGoods = ctx.saleGoods
        val imageCollection = ctx.requiredImageCollection

        Product().apply {
            productId = product.productId

            if (product.getIsSyncPlatform().isNull() || product.getIsSyncPlatform() == Bool.NO.code) {
                platformSyncState = Bool.YES.code
            }

            // 有上架成功的商品则设置产品为上架状态
            if (saleGoods.publishState == ProductPublishStateEnum.ACTIVE.code) {
                publishState = ProductPublishStateEnum.ACTIVE.code

                if (product.getIsSyncPlatform().isNull() || product.getIsSyncPlatform() == Bool.NO.code) {
                    setIsSyncPlatform(Bool.YES.code)
                }
            }

            product.publishTime ?: run {
                val currentUser = CurrentUserHolder.get()
                publishUserId = currentUser.id
                publishUserName = currentUser.name
                publishTime = LocalDateTime.now()
            }

            platformSyncState = PlatformSyncStateEnum.SUCCESS.code

            // 直接在 apply 块结束后执行更新
            product.mainImgUrl = imageCollection.limitedAeMainImages(1).firstOrNull()?.ossImageUrl
            this.mainImgUrl = product.mainImgUrl
            productRepository.updateById(this)
            log.info { "${ctx.logPrefix}更新商品状态，updateProduct: ${product.toJson()}" }
        }
    }

    /**
     * 添加成功同步日志
     */
    private fun saveSyncLog(platformName: String, shop: String?, saleGoods: AeSaleGoods, platformRequestParams: String?, resp: String, opType: Int?) {
        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.opType = opType
            this.logType = Bool.YES.code
            this.platformName = platformName
            this.shopName = shop
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
        }
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 添加错误同步日志
     */
    private fun addErrorSyncLog(platformName: String, shop: String?, saleGoods: AeSaleGoods, error: String?, platformRequestParams: String?, resp: String?, opType: Int?, aeSpuId: Long? = null) {
        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.templateSpuId = aeSpuId
            this.opType = opType
            this.errorMsg = error
            this.platformName = platformName
            this.shopName = shop
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
        }
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 处理失败的同步日志
     */
    private fun handleFailure(
        product: Product,
        saleGoods: AeSaleGoods,
        requestParams: String?,
        respParams: String?,
        opType: Int?,
        errorMessage: String?,
        logPrefix: String,
        aeSpuId: Long? = null,
    ) {
        log.error { "${logPrefix}发布商品到AE速卖通失败, error=${errorMessage}" }
        productRepository.updateById(Product().apply {
            this.productId = product.productId
            this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
        })

        if (saleGoods.platformSyncState != PlatformSyncStateEnum.SUCCESS.code) {
            aeSaleGoodsRepository.updateById(AeSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
            })
        }

        addErrorSyncLog(AE.platformName, saleGoods.shopName, saleGoods, errorMessage, requestParams, respParams, opType, aeSpuId)
    }

    /**
     * 处理成功的同步日志
     *
     * @param ctx 同步上下文
     * @param requestParams 请求参数
     * @param respParams 响应参数
     * @param opType 操作类型
     */
    private fun handleSuccessSyncLog(
        ctx: ProductSyncContext,
        requestParams: String?,
        respParams: String,
        opType: Int?,
    ) {
        val saleGoods = ctx.saleGoods

        //记录操作日志
        saveSyncLog(AE.platformName, saleGoods.shopName, saleGoods, requestParams, respParams, opType)

        // 删除错误日志
        val errorLogList = KtQueryWrapper(ProductSyncLog::class.java)
            .eq(ProductSyncLog::saleGoodId, saleGoods.saleGoodsId)
            .eq(ProductSyncLog::logType, Bool.NO.code)
            .`in`(
                ProductSyncLog::opType,
                listOf(PlatformOperatorTypeEnum.ACTIVE.code, PlatformOperatorTypeEnum.UPDATE_PRODUCT.code)
            )

        productSyncLogRepository.remove(errorLogList)
    }

    /**
     * 推送到AIDC
     *
     * @param ctx 同步上下文
     */
    private fun pushToAIDC(ctx: ProductSyncContext) {
        val product = ctx.product
        val saleGoods = ctx.saleGoods

        if (product.inspiraSourceId != null) {
            val onlineNoticeReq = ProductOnlineNoticeReq().apply {
                this.inspireSourceId = product.inspiraSourceId!!
                this.onlineSaleItemId = saleGoods.platformProductId!!
            }
            inspirationClientExternal.productOnlineNotice(onlineNoticeReq)
        }
    }

    /**
     * 推送图片到款式平台
     *
     * @param ctx 同步上下文
     * @param saleSkcList SKC列表
     */
    private fun pushPublishImage(ctx: ProductSyncContext, saleSkcList: List<AeSaleSkc>) {
        val product = ctx.product
        ctx.requiredImageRepository

        // 不处理数码印花类型的商品
        if (!ProductPublishHelper.isLoginNum(product)) {
            val imageCollection = ctx.requiredImageCollection

            //商品详情
            val dataList = mutableListOf<ProductImageChangeStateDto.Data>()
            val pushSkcList = mutableListOf<ProductImageChangeStateDto.Skc>()
            for (saleSkc in saleSkcList) {
                val skcImages = saleSkc.pictures?.split(Url.DELIM)?.map { it.trim() }?.filter { it.isNotBlank() }?.toList()
                val skc = ProductImageChangeStateDto.Skc().apply {
                    this.skcImageList = skcImages
                    this.skc = saleSkc.skc
                }
                pushSkcList.add(skc)
            }

            val data = ProductImageChangeStateDto.Data().apply {
                this.spuCode = product.spuCode
                this.productDetailsImageList = imageCollection.detailAppImageList.map { it.ossImageUrl!! }
                this.skcList = pushSkcList
            }
            dataList.add(data)

            val productImageChangeStateDto = ProductImageChangeStateDto()
            productImageChangeStateDto.dataList = dataList

            val record = MessageRecord().apply {
                this.businessId = product.productId.toString()
                this.exchange = MqConstants.EXCHANGE_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.routingKey = MqConstants.KEY_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.content = productImageChangeStateDto.toJson()
            }
            val msgId = messageRecordService.preCommitAndGetId(record)
            messageRecordService.commit(msgId, true)
        }
    }

    /**
     * 更新速卖通SKU关联信息
     * 查询商品信息并更新本地SKU的平台SKU ID
     *
     * @param ctx 同步上下文
     * @param saleSkus 销售SKU列表
     * @return AliexpressProductQueryResponse 商品详情响应
     */
    private fun updateSkuId(
        ctx: ProductSyncContext,
        saleSkus: List<AeSaleSku>,
    ): AliexpressProductQueryResponse {
        val saleGoods = ctx.saleGoods

        if (saleGoods.platformProductId == null) {
            throw BusinessException("平台商品ID不能为空")
        }

        // 查询速卖通商品详情
        val queryResponse = aliexpressService.queryProduct(saleGoods.platformProductId!!, ctx.shopToken)

        if (!queryResponse.isSuccess()) {
            log.error { "${ctx.logPrefix}查询速卖通商品详情异常, error: ${queryResponse.getJoinErrorMsg()} " }
            throw BusinessException("查询速卖通商品详情异常: ${queryResponse.getJoinErrorMsg()}")
        }

        val productDetail = queryResponse.result!!
        val platformSkus = productDetail.productSkus

        if (platformSkus.isNullOrEmpty()) {
            log.warn { "${ctx.logPrefix}商品${saleGoods.platformProductId}没有SKU信息" }
            return queryResponse
        }

        // 使用skuCode匹配并更新SKU ID
        val updatedSkuList = mutableListOf<AeSaleSku>()
        val productAePublishStateEnum = ProductAePublishStateEnum.getByPlatformStateValue(productDetail.productStatusType!!)

        platformSkus.forEach { platformSku ->
            val skuCode = platformSku.skuCode
            if (skuCode.isNotBlank()) {
                // 通过skuCode匹配本地SKU

                val shipsFromAttributeValueId = platformSku.skuProperties
                    ?.filter { it.skuPropertyId == aliexpressProperties.aePlatform.shipsFromPropertyId.toInt() }
                    ?.firstOrNull { it.propertyValueId.isNotNull() }
                    ?.propertyValueId
                // 匹配本地SKU, 需要增加发货地匹配
                val matchedSku = saleSkus.filter { it.sellerSku == skuCode }
                    .find {
                        // 正常发货地匹配
                        it.shipsFromAttributeValueId == shipsFromAttributeValueId
                                // 发货地为中国时匹配, 中国(pop)==null(平台)
                                || (shipsFromAttributeValueId == null && it.shipsFromAttributeValueId == aliexpressProperties.aePlatform.shipsFromPropertyValueId)
                    }
                if (matchedSku != null) {
                    val updateSku = AeSaleSku().apply {
                        this.saleSkuId = matchedSku.saleSkuId
                        if (matchedSku.shopSku != platformSku.id) {
                            this.shopSku = platformSku.id
                        }
                        if (matchedSku.platformSkuId != platformSku.skuId?.toString()) {
                            this.platformSkuId = platformSku.skuId?.toString()
                        }
                        if (matchedSku.platformProductId == null) {
                            this.platformProductId = saleGoods.platformProductId
                        }
                        this.publishState = productAePublishStateEnum?.code
                    }

                    updatedSkuList.add(updateSku)
                } else {
                    log.warn { "${ctx.logPrefix}未找到匹配的本地SKU: skuCode=${skuCode}" }
                }
            } else {
                log.warn { "${ctx.logPrefix}平台SKU没有skuCode, 无法匹配: id=${platformSku.id}" }
            }
        }

        // 批量更新SKU信息
        if (updatedSkuList.isNotEmpty()) {
            aeSaleSkuRepository.updateBatchById(updatedSkuList)
            log.info { "${ctx.logPrefix}成功更新${updatedSkuList.size}个SKU的平台ID信息" }
        } else {
            log.warn { "${ctx.logPrefix}没有找到可以更新的SKU匹配项" }
        }

        return queryResponse
    }

    /**
     * 清理属性值名称，移除特殊字符并限制长度
     * subCode: isv.7011013, subMsg: null:011013
     * The SKU name cannot include special characters, legal characters such as English letters, numbers, spaces, etc. The length cannot exceed 20 characters, and you have entered Purple Blue+Red
     * 2025年6月11日: 因为发现有正常颜色超过20, 方案自动截取改为异常抛出, 让业务自己买平台颜色
     */
    private fun sanitizePropertyValueName(name: String): String {
//        // 使用预编译的正则表达式
//        val sanitized = name.replace("+", " ")
//            .replace(INVALID_CHARS_REGEX, " ")
//            .replace(MULTIPLE_SPACES_REGEX, " ")
//            .trim()
//
//        return if (sanitized.length > 20) sanitized.substring(0, 20) else sanitized
        return name
    }

    /**
     * 构建必填属性
     *
     * @param ctx 同步上下文
     * @param categoryAttributeMap 类目属性映射
     * @param processedAttributeIds 已处理的属性ID集合，用于跟踪
     * @return 构建好的必填属性列表
     */
    private fun buildRequiredProductProperties(
        ctx: ProductSyncContext,
        categoryAttributeMap: Map<String, Attribute>,
        processedAttributeIds: MutableSet<Long>,
    ): MutableList<ProductProperty> {
        val shop = ctx.shop
        val saleGoods = ctx.saleGoods

        // 从配置中获取必填属性配置项
        val requiredAttributesConfig = aliexpressProperties.aePlatform.requiredProductAttributes
        val result = mutableListOf<ProductProperty>()

        // 处理品牌属性
        val brandPropertyId = aliexpressProperties.aePlatform.brandPropertyId
        val shopBrandName = shop.brandName

        // 检查品牌属性是否必填
        val brandAttribute = categoryAttributeMap[brandPropertyId.toString()]
        val isBrandRequired = brandAttribute?.required ?: false
        // 尝试使用实际的品牌值
        val realBrandValue = shopBrandName?.takeIf { it.isNotBlank() }?.let { name ->
            brandAttribute?.values?.firstOrNull { v ->
                // 包含匹配或反向包含匹配
                (v.getName()?.contains(name, true) == true
                        || v.getName()?.let { name.contains(it, true) } == true)
            }?.let { brandValue ->
                result.add(
                    ProductProperty(
                        attrNameId = brandPropertyId.toInt(),
                        attrValueId = brandValue.id,
                        attrName = requiredAttributesConfig.brandAttribute.attributeName,
                        attrValue = name
                    )
                )
                processedAttributeIds.add(brandPropertyId)
                true
            }
        } ?: false

        // 如果没有找到实际品牌值，且属性是必填的，才使用默认值
        if (!realBrandValue && isBrandRequired) {
            requiredAttributesConfig.brandAttribute.let { brandAttr ->
                result.add(brandAttr.toProductProperty())
                processedAttributeIds.add(brandPropertyId)
            }
        }

        // 处理原产地属性
        val originItems = saleGoods.getParsedOriginPropertyValueItems()
        val originPropertyId = requiredAttributesConfig.originAttribute.attributeId!!

        // 检查原产地是否必填
        val isOriginRequired = categoryAttributeMap[originPropertyId.toString()]?.required ?: false

        if (originItems.isNotEmpty()) {
            // 使用实际的原产地属性
            originItems!!.forEach { item ->
                result.add(item.toProductProperty())
                processedAttributeIds.add(item.attributeId!!)
            }
        } else {
            // 仅当属性为必填时才设置默认值
            with(requiredAttributesConfig) {
                // 检查原产地是否必填，是则添加
                if (isOriginRequired) {
                    originAttribute.let { attr ->
                        result.add(attr.toProductProperty())
                        processedAttributeIds.add(attr.attributeId!!)
                    }
                    provinceAttribute.let { attr ->
                        result.add(attr.toProductProperty())
                        processedAttributeIds.add(attr.attributeId!!)
                    }
                }
            }
        }

        return result
    }

    /**
     * 专门用于查找尺码属性值的方法
     * 优先使用标准尺码属性值（value_tags中有标记的）
     * 全托管&半托管&POP-女装尺码升级-ISV技术对接文档
     * [参考链接](https://open.aliexpress.com/announcement/index.htm#/announcement/detail?id=1618&entitle=All%20Announcements&cntitle=%E6%89%80%E6%9C%89%E5%85%AC%E5%91%8A&_k=88he9n)
     */
    private fun findBestSizeAttributeMatch(
        attribute: Attribute?,
        sizeToMatch: String,
        usedValueIds: Set<Long> = emptySet(),
    ): AttributeValueResult? {
        if (attribute == null || sizeToMatch.isBlank()) return null

        // 标准化尺码表示，优先处理映射
        val normalizedSize = AE_SIZE_MAP[sizeToMatch] ?: sizeToMatch
        val sizeLower = normalizedSize.lowercase()


        // 1. 首先尝试匹配带有标准尺码标记的属性值（value_tags中有size_model_value=standard或onesize）
        attribute.values?.firstOrNull { value ->
            val name = value.getName()?.lowercase()
            val valueTagsMap = value.valueTags ?: emptyMap()
            val isStandardSize =
                valueTagsMap["size_model_value"] == "standard" || valueTagsMap["size_model_value"] == "onesize"
            // 名称匹配且是标准尺码且ID未被使用
            isStandardSize && value.id !in usedValueIds
                    && (name == sizeLower || name == normalizedSize.lowercase() || name == sizeToMatch.lowercase())
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 2. 如果没有找到标准尺码，尝试精确匹配非标准尺码
        attribute.values?.find { value ->
            val name = value.getName()?.lowercase()
            value.id !in usedValueIds && (name == sizeLower || name == sizeToMatch.lowercase())
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 3. 尝试反向映射（例如：2XL → XXL）匹配
        val reverseMappedSize = AE_REVERSE_SIZE_MAP[normalizedSize]
        if (reverseMappedSize != null) {
            attribute.values?.find { value ->
                val name = value.getName()?.lowercase()
                value.id !in usedValueIds && name == reverseMappedSize.lowercase()
            }?.let {
                return AttributeValueResult(it.id!!)
            }
        }

        // 4. 尝试包含匹配
        attribute.values?.find { value ->
            val name = value.getName()?.lowercase()
            value.id !in usedValueIds && name != null && (name.contains(sizeLower) || sizeLower.contains(name))
        }?.let {
            return AttributeValueResult(it.id!!)
        }

        // 5. 如果所有匹配方式都失败但还有可用属性值，选择一个未使用的
        attribute.values?.firstOrNull { it.id !in usedValueIds }?.let {
            return AttributeValueResult(it.id!!, isCustomValue = true)
        }

        return null
    }

    private fun getExchangeRate(currencyType: String, countryCode: String): BigDecimal? {
        return currencyExchangeRateService.getExchangeRate(currencyType, countryCode)
    }

    /**
     * 添加必填输入框属性
     */
    private fun addInputTypeRequiredAttributesIfConfigured(
        ctx: ProductSyncContext,
        categoryAttributeMap: Map<String, Attribute>,
        processedAttributeIds: MutableSet<Long>,
        aeProductProperties: MutableList<ProductProperty>,
    ) {
        aliexpressProperties.aePlatform.inputTypePropertyDefaultValues.forEach { (attrIdStr, defaultValue) ->
            val attribute = categoryAttributeMap[attrIdStr] ?: return@forEach
            val isInputType = attribute.attributeShowTypeValue?.let {
                aliexpressProperties.aePlatform.attributeInputTypeValue.contains(it)
            } ?: false
            if (attribute.required == true && isInputType && processedAttributeIds.add(attribute.id ?: return@forEach)) {
                aeProductProperties.add(
                    ProductProperty(
                        attrNameId = attribute.id.toInt(),
                        attrName = attribute.getName() ?: attribute.id.toString(),
                        attrValue = defaultValue
                    )
                )
                log.info { "${ctx.logPrefix}添加必填输入框属性: [${attribute.getName()}], 默认值: $defaultValue" }
            }
        }
    }

    /**
     * 内部方法：用 ProductAeGroupDTO 列表设置产品分组
     */
    private fun setProductGroups(
        ctx: ProductSyncContext,
        platformProductId: Long,
        groups: List<ProductAeGroupDTO>,
    ) {
        // 提取有效分组ID（child > parent）
        val groupIds = groups.mapNotNull { it.childGroupId ?: it.parentGroupId }
            .distinct()

        if (groupIds.isEmpty()) {
            log.info { "${ctx.logPrefix} No valid groupId in group list." }
            return
        }

        var respParams: String? = null
        var requestParams: String? = null
        try {
            val request = AliexpressSetProductGroupsRequest(
                platformProductId = platformProductId,
                groupIds = groupIds
            )
            requestParams = request.toJson()

            val response = aliexpressService.setProductGroups(ctx.product.productId!!, request, ctx.shopToken)
            respParams = response.toJson()
            if (!response.isSuccess()) {
                throw BusinessException("设置商品分组失败: ${response.getJoinErrorMsg()}")
            }
        } catch (e: Exception) {
            addErrorSyncLog(AE.platformName, ctx.saleGoods.shopName, ctx.saleGoods, e.getRootMessage(), requestParams, respParams, PlatformOperatorTypeEnum.SET_PRODUCT_GROUPS.code)
        }
    }

    /**
     * 更新图包规则相关字段
     *
     * @param saleGoods 销售商品信息
     */
    private fun updateImagePackRuleFields(
        ctx: ProductSyncContext,
    ) {
        val saleGoods = ctx.saleGoods
        val saleGoodsId = saleGoods.saleGoodsId ?: return
        if (saleGoods.imagePackRuleId != null) return

        // 使用 KtUpdateWrapper 来处理 null 值的设置
        aeSaleGoodsRepository.ktUpdate()
            .eq(AeSaleGoods::saleGoodsId, saleGoodsId)
            .set(AeSaleGoods::imagePackRuleId, null)
            .set(AeSaleGoods::imagePackRuleVersion, null)
            .update()

        log.info { "${ctx.logPrefix} 更新图包规则字段, ruleId=${saleGoods.imagePackRuleId}, version=${saleGoods.imagePackRuleVersion}" }
    }

}

/**
 * 属性值查找结果
 */
private data class AttributeValueResult(
    /**
     * 属性值ID
     */
    val id: Long,
    val isCustomValue: Boolean = false,
)

/**
 * 聚合一次上/更新商品所需的上下文信息。
 * 用于在多个步骤之间共享核心参数、图片资料、商品信息等，避免重复透传。
 */
private data class ProductSyncContext(
    /** 日志前缀，统一标识 spuCode/productId/saleGoodsId，便于日志检索 */
    val logPrefix: String,
    /** 商品主信息（Product 表），包含 SPU、品类ID、同步状态等 */
    val product: Product,
    /** 销售维度商品（SaleGoods 表），包含商品标题、税号、平台状态等 */
    val saleGoods: AeSaleGoods,
    /** 所属店铺信息，包含店铺 ID、token、品牌名等 */
    val shop: Shop,
    /** 商品的图片资源（ImageRepository 表），包括主图、详情图等 */
    var imageRepository: ImageRepository? = null,
    /** 上传后的图片集合，用于构建平台商品请求（延迟设置） */
    var imageCollection: ImagePackCollectionDTO? = null,
    /** 当前商品对应的图片记录（ProductPicture 表），用于上传与回写（延迟设置） */
    var picture: ProductPicture? = null,
    /**
     * 速卖通卖家关系响应，包含卖家信息等
     */
    var aliexpressSellerRelationResponse: AliexpressSellerRelationResponse? = null,
    /**
     * 店铺渠道的商品的货币类型，默认CNY 或者 USD
     */
    var aliexpressCurrencyType: String? = null,
) {
    /** 商品 SPU 编码，供统一访问使用 */
    val spu get() = product.spuCode!!

    /** 店铺 access token，供平台接口调用 */
    val shopToken get() = shop.token ?: throw BusinessException("$logPrefix ${shop.shopName}店铺没有token")

    /** 强制获取 picture，如未初始化则抛出异常 */
    val requiredPicture: ProductPicture
        get() = picture ?: throw BusinessException("$logPrefix picture 未初始化")

    /** 强制获取 imageCollection，如未初始化则抛出异常 */
    val requiredImageCollection: ImagePackCollectionDTO
        get() = imageCollection ?: throw BusinessException("$logPrefix imageCollection 未初始化")

    /** 强制获取 imageRepository，如未初始化则抛出异常 */
    val requiredImageRepository: ImageRepository
        get() = imageRepository ?: throw BusinessException("$logPrefix imageRepository 未初始化")

    /** 强制获取 aliexpressSellerRelationResponse，如未初始化则抛出异常 */
    val requiredSellerRelationResponse: AliexpressSellerRelationResponse
        get() = aliexpressSellerRelationResponse ?: throw BusinessException("$logPrefix aliexpressSellerRelationResponse 未初始化")

    /** 强制获取 aliexpressCurrencyType，如未初始化则抛出异常 */
    val requiredAliexpressCurrencyType: String
        get() = aliexpressCurrencyType ?: throw BusinessException("$logPrefix aliexpressCurrencyType 未初始化")
}
