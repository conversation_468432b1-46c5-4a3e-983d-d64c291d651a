package tech.tiangong.pop.component.export

import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.repository.AeSelectedProductSpuRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.AeSelectedProductExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.selected.AeSelectedProductPageReq
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.FileExportUtils
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * AE精选商品-导出-商品数据
 * <AUTHOR>
 * @date 2025-7-18 17:55:53
 */
@Component
@Slf4j
class AeSelectedProductExportComponent(
    private val aeSelectedProductSpuRepository: AeSelectedProductSpuRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val downloadTaskService: DownloadTaskService,
) : DownloadTaskInterface() {

    /**
     * 创建下载任务
     */
    fun createExportTask(req: AeSelectedProductPageReq) {
        val dateStr = LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault())
        )
        downloadTaskService.createTask("AE精品商品导出商品数据_$dateStr", DownloadTaskTypeEnum.AE_SELECTED_PRODUCT_DATA, req.toJson())
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson(AeSelectedProductPageReq::class.java) ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 组装excel dto集合
        val dataDtoList = mutableListOf<AeSelectedProductExportDto>()

        // 获取数据
        var pageNum = 1L
        val pageSize = 1000L
        while (true) {


            val resultPage = aeSelectedProductSpuRepository.pageAe(Page(pageNum, pageSize), req)
            if (resultPage.records.isEmpty()) {
                break
            }

            val respList = resultPage.records.map {
                AeSelectedProductExportDto().apply {
                    this.aePid = it.aeProductId?.toString()
                    this.alibabaId = it.alibabaId
                    this.brandName = it.brandName
                    this.mainPush = if (it.isMainPush == Bool.YES.code) "是" else "否"
                    this.marketName = it.marketName
                    this.tryOnRule = it.tryOnRuleName
                }
            }
            pageNum += 1
            if (respList.isNotEmpty()) {
                dataDtoList.addAll(respList)
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, AeSelectedProductExportDto::class.java, dataDtoList)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
