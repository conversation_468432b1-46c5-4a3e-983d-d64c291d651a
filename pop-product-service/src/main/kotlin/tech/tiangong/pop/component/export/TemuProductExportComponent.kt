package tech.tiangong.pop.component.export

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.support.ExcelTypeEnum
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.enums.TemuCurrencyTypeEnum
import tech.tiangong.pop.common.enums.*
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.ImportBaseProductDTO
import tech.tiangong.pop.dto.product.ImportTemuProductDTO
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.enums.settings.ProductTitleRuleFieldEnum
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.AttributePairReq
import tech.tiangong.pop.req.product.AutoCalPriceReq
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.product.AutoCalPriceResp
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.ExcelUtils
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 导出-待上架-TEMU商品
 * <AUTHOR>
 * @date 2025-6-16 17:57:52
 */
@Component
@Slf4j
class TemuProductExportComponent(
    private val uploaderOssHelper: UploaderOssHelper,
    private val productRepository: ProductRepository,
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val productAttributesRepository: ProductAttributesRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository,
    private val productPriceManagementService: ProductPriceManagementService,
    private val listingTemplateRepository: ListingTemplateRepository,
    private val listingTemplateSkcRepository: ListingTemplateSkcRepository,
    private val listingTemplateSkuRepository: ListingTemplateSkuRepository,
    private val downloadTaskService: DownloadTaskService,
    private val shopRepository: ShopRepository,
) : DownloadTaskInterface() {

    /**
     * 固定平台枚举-Temu
     */
    private val platformEnum = PlatformEnum.TEMU
    private val channelEnum = ChannelEnum.OTHER

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductPendingTemuPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productTemplateTemuSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出待上架Temu商品_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PENDING_LISTING_TEMU_TASK,
                req.toJson()
            );
            log.info { "导出上架失败日志-Temu待上架-创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出上架失败日志-Temu待上架-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("提交导出待上架发布失败任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ProductPendingTemuPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 分页所有数据
        var pageNum: Long = 1
        val pageSize: Long = 1000
        // key=sheet(品类), value=sheetData
        val sheetDataMap = mutableMapOf<String, MutableList<ImportTemuProductDTO>>()

        while (true) {
            val pageVo = productTemplateTemuSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (pageVo.records.isEmpty()) {
                break
            }
            var productIdMap: Map<Long?, Product?> = mutableMapOf()
            val productIds = pageVo.records.mapNotNull { it.productId }.distinct()
            if (productIds.isNotEmpty()) {
                productIdMap = productRepository.listByIds(productIds).associateBy { it.productId }
            }
            pageVo.records.forEach { templateSpu ->
                val dataList = exportProductByTemu(templateSpu, productIdMap)
                if (dataList.isNotEmpty()) {
                    dataList.forEach { data ->
                        sheetDataMap.getOrPut(data.categoryName ?: "") { mutableListOf() }.add(data)
                    }
                }
            }
            pageNum++
        }
        if (sheetDataMap.isEmpty()) {
            throw BusinessException("导出数据为空")
        }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {
                val excelWriter = EasyExcel.write(excelFile).excelType(ExcelTypeEnum.XLSX).build()
                // 将 MutableMap 的 entries 转换为 List 后使用 forEachIndexed
                sheetDataMap.entries.toList().forEachIndexed { index, entry ->
                    val sheetName = entry.key
                    val dataList = entry.value
                    // Ae属性转为动态列
                    val resultAeMap = ImportBaseProductDTO.convertToMapList(dataList)
                    val totalAe = resultAeMap.map { map -> map.values.toList() }

                    val writeSheet = EasyExcel.writerSheet(index, ExcelUtils.replaceSheetName(sheetName))
                        .head(ExcelUtils.extractHeaders(resultAeMap))
                        .build()
                    excelWriter.write(totalAe, writeSheet)
                }
                excelWriter.finish()
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败，使用 EasyExcel 写入文件时出错", e)
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            throw BusinessException("导出失败", e)
        }
    }

    /**
     * 处理TEMU数据导出
     */
    private fun exportProductByTemu(
        templateSpu: ProductTemplateTemuSpu,
        productIdMap: Map<Long?, Product?>,
    ): MutableList<ImportTemuProductDTO> {
        val allTemuData: MutableList<ImportTemuProductDTO> = mutableListOf()
        // 获取product
        val product = productIdMap[templateSpu.productId]
        if (product == null) {
            return allTemuData
        }
        // 获取上架shop
        val listingShop = shopRepository.getById(templateSpu.shopId) ?: return allTemuData

        // 获取Temu模板SPU
        val spuTemu = productTemplateTemuSpuRepository.getByProductId(templateSpu.productId!!)
        val productTemplateTemuSpu = spuTemu.find { it.businessType == ProductShopBusinessType.FULLY_MANAGED.value }
        if (productTemplateTemuSpu == null) {
            return allTemuData
        }
        // 获取Temu模板SKC
        val skcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(productTemplateTemuSpu.temuSpuId!!)
            .filter { it.state == Bool.YES.code }
            .filter { it.combo == Bool.NO.code }
        if (skcTemuList.isEmpty()) {
            return allTemuData
        }
        // 获取Temu模板SKU
        val skuTemuList = productTemplateTemuSkuRepository.listByTemuSkcIdList(skcTemuList.mapNotNull { it.temuSkcId })
            .filter { it.enableState == Bool.YES.code }
        if (skuTemuList.isEmpty()) {
            return allTemuData
        }
        if (productTemplateTemuSpu.productNameEn.isBlank() || productTemplateTemuSpu.productName.isBlank()) {
            val resp = productTitleGenerateService.previewTitle(
                ProductTitleGenerateReq().apply {
                    productId = product.productId
                    this.product = product
                }
            )
            resp.productTitleRuleId?.let {
                fun assignTitleIfValid(newTitle: String?, oldTitle: String?) =
                    if (!newTitle.isNullOrBlank() && newTitle.length < ProductInnerConstant.getTitleMaxLength(PlatformEnum.TEMU)) newTitle else oldTitle

                productTemplateTemuSpu.productNameEn = assignTitleIfValid(resp.title, productTemplateTemuSpu.productNameEn)
                productTemplateTemuSpu.productName = assignTitleIfValid(resp.title, productTemplateTemuSpu.productName)

                productTemplateTemuSpu.generatedTitleOverLengthFlag =
                    if (resp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                productTemplateTemuSpu.generatedTitleMissingFieldsJson = resp.missingFields.toJson()
            }
        }

        val imageCollection = getImages(product.spuCode!!)
        val detailImageList = imageCollection.sellingPointImages

        //获取SKC的轮播图
        val productDetailImages: List<ImageAniVo> = imageCollection.productDetailImages
        val skcList = skcTemuList.map { it.skc!! }
        val skcImages = imageCollectionHelper.fetchSkcImages(
            productDetailImages,
            skcList,
            ImageTypeEnum.PRODUCT_DETAIL.code
        )

        val calPriceReq = AutoCalPriceReq().apply {
            this.productId = product.productId!!
            this.shopId = product.shopId
            this.platform = PlatformEnum.TEMU
            this.shopBusinessType = ProductShopBusinessType.FULLY_MANAGED.value
        }
        val autoPriceList: List<AutoCalPriceResp> = productPriceManagementService.autoCalPrice(calPriceReq)
        val priceMap = autoPriceList.associateBy { autoPrice -> autoPrice.skc }

        // 上架模板SPU
        val listingTemplate = product.prototypeNum?.let { pn -> listingTemplateRepository.listByPatternCodeAndPlatformId(platformEnum.platformId, pn, Bool.YES.code) }
        var colorCodeLtSkcMap: Map<String?, ListingTemplateSkc> = emptyMap()
        var skcIdLtSkuMap: Map<Long?, List<ListingTemplateSku>> = emptyMap()
        if (listingTemplate != null) {
            colorCodeLtSkcMap = listingTemplateSkcRepository.getByListingTemplateId(listingTemplate.listingTemplateId!!).associateBy { it.colorCode }
            skcIdLtSkuMap = listingTemplateSkuRepository.getByListingTemplateId(listingTemplate.listingTemplateId!!).groupBy { it.templateSkcId }
        }

        skcTemuList.forEach { skcInfo ->

            val firstSku = skuTemuList.find { it.temuSkcId == skcInfo.temuSkcId }

            val importDto = ImportTemuProductDTO().apply {
                this.spuCode = product.spuCode                  // *SPU
                this.productName = productTemplateTemuSpu.productName                 // 商品名称(中文)
                this.productNameEn = productTemplateTemuSpu.productName               // 商品名称(英文)
                this.listingShopName = listingShop.shopName
                this.materialLanguages = productTemplateTemuSpu.materialLanguages?.parseJsonList(MaterialLanguage::class.java)?.mapNotNull { ml -> ml.languageName }?.sorted()?.joinToString(",")
                if (this.materialLanguages.isBlank() && listingTemplate != null) {
                    // 如果为空, 拿模版
                    this.materialLanguages = listingTemplate.materialLanguages?.parseJsonList(MaterialLanguage::class.java)?.mapNotNull { ml -> ml.languageName }?.sorted()?.joinToString(",")
                }

                this.generatedTitleMissingFields = productTemplateTemuSpu.getParsedGeneratedTitleMissingFields().joinToString(", ") { ProductTitleRuleFieldEnum.getByCode(it)?.desc ?: it }
                this.generatedTitleOverLengthFlag = productTemplateTemuSpu.generatedTitleOverLengthFlag?.let { if (it == YesOrNoEnum.YES.code) "是" else "否" }
                this.categoryName = product.categoryName               // (内部)商品类目
                val originJson = productTemplateTemuSpu.countryOriginPlace?.parseJson(ProductTemuCountryOriginPlaceReq::class.java)
                if (originJson != null) {
                    var originPlaceNameTmp = "${originJson.originName}"
                    if (originJson.secondaryOriginName.isNotBlank()) {
                        originPlaceNameTmp = originPlaceNameTmp + ",${originJson.secondaryOriginName}"
                    }
                    this.originPlaceName = originPlaceNameTmp
                }
                this.skcCode = skcInfo.skc
                this.mainColor = skcInfo.color
                this.sizeGroup = "字母码"
                this.specificationType = "尺码"
                this.size = skuTemuList.mapNotNull { it.sizeName }.distinct().sorted().joinToString(",")

                val price = priceMap[skcInfo.skc]
                val cnyPrice = price?.countryPriceList?.find { it.country == CountryEnum.CN.code }?.salePrice
                val usdPrice = price?.countryPriceList?.find { it.country == CountryEnum.US.code }?.salePrice

                if (firstSku?.declaredPrice.isNotBlank()) {
                    val declaredPriceJson = firstSku?.declaredPrice!!.parseJsonList(FullyManagedPrice::class.java)
                    if (declaredPriceJson.isNotEmpty()) {
                        this.declaredPriceUsd = declaredPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.USD.value }?.price
                        this.declaredPriceCny = declaredPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.CNY.value }?.price
                    }
                }
                if (firstSku?.recommendedPrice.isNotBlank()) {
                    val recommendedPriceJson = firstSku?.recommendedPrice!!.parseJsonList(FullyManagedPrice::class.java)
                    if (recommendedPriceJson.isNotEmpty()) {
                        this.retailPriceUsd = recommendedPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.USD.value }?.price
                        this.retailPriceCny = recommendedPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.CNY.value }?.price
                    }
                }

                // 价格为空, 则使用计算价格
                if (this.declaredPriceUsd == null) {
                    this.declaredPriceUsd = usdPrice
                }
                if (this.declaredPriceCny == null) {
                    this.declaredPriceCny = cnyPrice
                }
                // 2025年6月19日, 产品说不用计算建议零售价
//                if (this.retailPriceCny == null) {
//                    this.retailPriceCny = cnyPrice
//                }
//                if (this.retailPriceUsd == null) {
//                    this.retailPriceUsd = usdPrice
//                }


                if (firstSku?.classifiedAttrs.isNotBlank()) {
                    val classifiedAttrsJson = firstSku?.classifiedAttrs?.parseJson<ClassifiedAttr>()
                    if (classifiedAttrsJson != null) {
                        this.skuClassification = ClassifiedAttrTypeCodeEnum.getByCode(classifiedAttrsJson.typeCode)?.desc
                        this.quantity = classifiedAttrsJson.quantity
                        this.unit = ClassifiedAttrUnitEnum.getByCode(classifiedAttrsJson.unitCode)?.desc
                    }
                }
                this.stockQuantity = firstSku?.stockQuantity
                this.maxLength = firstSku?.longestEdge
                this.middleLength = firstSku?.secondEdge
                this.minLength = firstSku?.shortestEdge
                this.weight = firstSku?.weight

                // 为空, 找上架模版的
                val ltSkc = colorCodeLtSkcMap[skcInfo.colorCode]
                var ltSku: ListingTemplateSku? = null
                if (ltSkc != null) {
                    ltSku = skcIdLtSkuMap[ltSkc.templateSkcId]?.firstOrNull()
                }
                if (ltSku != null) {
                    // SKU分类为空时
                    if (this.skuClassification == null && this.quantity == null && this.unit == null) {
                        if (ltSku.classifiedAttrs.isNotBlank()) {
                            val classifiedAttrsJson = ltSku.classifiedAttrs?.parseJson<ClassifiedAttr>()
                            if (classifiedAttrsJson != null) {
                                this.skuClassification = ClassifiedAttrTypeCodeEnum.getByCode(classifiedAttrsJson.typeCode)?.desc
                                this.quantity = classifiedAttrsJson.quantity
                                this.unit = ClassifiedAttrUnitEnum.getByCode(classifiedAttrsJson.unitCode)?.desc
                            }
                        }
                    }
                    // 其他为空时
                    if (this.stockQuantity == null) {
                        this.stockQuantity = ltSku.stockQuantity
                    }
                    if (this.maxLength == null) {
                        this.maxLength = ltSku.longestEdge
                    }
                    if (this.middleLength == null) {
                        this.middleLength = ltSku.secondEdge
                    }
                    if (this.minLength == null) {
                        this.minLength = ltSku.shortestEdge
                    }
                    if (this.weight == null) {
                        this.weight = ltSku.weight
                    }
                }

                val skcImageTmpList = skcImages[skcInfo.skc]
                if (skcImageTmpList.isNotEmpty()) {
                    this.productImage1 = skcImageTmpList?.getOrNull(0)?.ossImageUrl
                    this.productImage2 = skcImageTmpList?.getOrNull(1)?.ossImageUrl
                    this.productImage3 = skcImageTmpList?.getOrNull(2)?.ossImageUrl
                    this.productImage4 = skcImageTmpList?.getOrNull(3)?.ossImageUrl
                    this.productImage5 = skcImageTmpList?.getOrNull(4)?.ossImageUrl

                    // 详情图是70+勾选的skc
                    var detailSkcImageTmpList = listOf<ImageAniVo>()
                    // 先找出勾选的skc
                    val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)
                    if (detailedImageSetting != null) {
                        // 有勾选, 则使用勾选的skc图
                        val tmpList = skcImages[detailedImageSetting.skc]
                        if (tmpList.isNotEmpty()) {
                            detailSkcImageTmpList = tmpList!!
                        }
                    }
                    if (detailSkcImageTmpList.isNotEmpty()) {
                        // 空, 则使用第一个skc图
                        val firstSkcImage = skcImages.values.firstOrNull()
                        if (firstSkcImage.isNotEmpty()) {
                            detailSkcImageTmpList = firstSkcImage!!
                        }
                    }

                    this.detailImages = (detailImageList + detailSkcImageTmpList).mapNotNull { it.ossImageUrl }.joinToString(",")
                }
            }
            // 设置属性
            var attributes = productAttributesRepository.listByProductId(templateSpu.productId!!, PlatformEnum.TEMU.platformId)
            if (attributes.isEmpty() && listingTemplate != null) {
                // 如果商品是空, 则使用上架模板属性(特殊规则: 上架模板属性也在商品属性表, 只是id不同)
                attributes = productAttributesRepository.listByProductId(listingTemplate.listingTemplateId!!, PlatformEnum.TEMU.platformId)
            }
            if (attributes.isNotEmpty()) {
                val attributePairResps = productRepository.selectAttributesById(
                    attributes.map { att ->
                        AttributePairReq().apply {
                            this.attributeId = att.attributeId
                            this.attributeValueId = att.attributeValueId
                        }
                    }
                )
                if (attributePairResps.isNotEmpty()) {
                    importDto.attributePairResps = attributePairResps
                }
            }
            allTemuData.add(importDto)
        }
        return allTemuData
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }

}