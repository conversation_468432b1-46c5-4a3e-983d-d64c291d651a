package tech.tiangong.pop.component.export

import cn.hutool.core.date.DatePattern
import com.alibaba.excel.EasyExcel
import com.alibaba.excel.support.ExcelTypeEnum
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.*
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.*
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ImportProductProcessStateEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.ExportAwaitProductReq
import tech.tiangong.pop.resp.product.AttributePairResp
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.ExcelUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.time.LocalDateTime
import java.util.*

/**
 * 导出"导入失败"的商品数据
 * <AUTHOR>
 * @date 2025-4-27 11:11:39
 */
@Component
@Slf4j
class ImportFailProductExportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val downloadTaskService: DownloadTaskService,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ExportAwaitProductReq) {
        if (req.createdTimeStart == null || req.createdTimeEnd == null) {
            throw IllegalArgumentException("请选择日期")
        }
        val count = importProductRecordRepository.count(
            KtQueryWrapper(ImportProductRecord::class.java)
                .eq(ImportProductRecord::processState, ImportProductProcessStateEnum.FAILURE.code)
                .eq(ImportProductRecord::platformId, req.platform!!.platformId)
                .between(ImportProductRecord::createdTime, req.createdTimeStart, req.createdTimeEnd)
        )
        if (count == 0L) {
            throw BusinessException("没有数据可导出")
        }

        val taskName = when (req.platform) {
            LAZADA, AE, TEMU -> {
                "导入${req.platform!!.platformName}待上架失败商品_" + LocalDateTime.now().format(DatePattern.PURE_DATETIME_FORMATTER)
            }

            else -> throw IllegalArgumentException("导出\"导入失败\"商品数据-请选择平台")
        }

        // 创建导出任务
        try {
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.FAILED_IMPORT_PRODUCT_TASK_EXPORT_TASK,
                req.toJson()
            )
            log.info { "提交导入待上架失败商品导出任务成功，任务名称：${taskName}，请求参数：${req.toJson()}" }
        } catch (e: Exception) {
            log.error(e) { "提交导入待上架失败商品导出任务失败，任务名称：${taskName}" }
            throw BusinessException("提交待上架失败商品导出任务失败：${e.message}")
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ExportAwaitProductReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        if (req.createdTimeStart == null || req.createdTimeEnd == null) {
            throw IllegalArgumentException("请选择日期")
        }

        return when (req.platform) {
            PlatformEnum.LAZADA -> {
                // 处理Lazada平台导出逻辑
                exportLazada(req, tempDir, task)
            }

            PlatformEnum.AE -> {
                // 处理AE平台导出逻辑
                exportAe(req, tempDir, task)
            }

            PlatformEnum.TEMU -> {
                // 处理Temu平台导出逻辑
                exportTemu(req, tempDir, task)
            }

            else -> {
                throw IllegalArgumentException("不支持的平台: ${req.platform}")
            }
        }
    }

    /**
     * 导出Lazada平台的商品数据
     */
    private fun exportLazada(req: ExportAwaitProductReq, tempDir: File, task: DownloadTask): FileUploadDTO {

        // 获取数据
        val importProductRecords = importProductRecordRepository.list(
            KtQueryWrapper(ImportProductRecord::class.java)
                .eq(ImportProductRecord::processState, ImportProductProcessStateEnum.FAILURE.code)
                .eq(ImportProductRecord::platformId, req.platform!!.platformId)
                .eq(ImportProductRecord::importSource, ImportSourceEnum.PENDING_UPLOAD.code)
                .between(ImportProductRecord::createdTime, req.createdTimeStart, req.createdTimeEnd)
                .orderByDesc(ImportProductRecord::createdTime)
        )
        val result = importProductRecords.map { it ->
            val importProductDTO = it.importData!!.parseJson(ImportProductFailDTO::class.java)
            importProductDTO.errorMsg = it.errorMsg
            val attributePairResps = mutableListOf<AttributePairResp>()
            if (Objects.nonNull(importProductDTO.dynamicAttributes)) {
                importProductDTO.dynamicAttributes.forEach { (key, value) ->
                    val attributePairResp = AttributePairResp()
                        .apply {
                            this.attributeName = key
                            this.attributeValue = value
                        }
                    attributePairResps.add(attributePairResp)
                }
                importProductDTO.attributePairResps = attributePairResps
            }
            importProductDTO
        }

        val resultMap = ImportBaseProductDTO.convertToMapList(result)

        val total = mutableListOf<List<String>>()
        for (map in resultMap) {
            val info = mutableListOf<String>()
            map.forEach { (key, value) -> info.add(value) }
            total.add(info)
        }
        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            val fos = FileOutputStream(excelFile)
            EasyExcel.write(fos)
                .head(ExcelUtils.extractHeaders(resultMap))
                .sheet("sheet1")
                .doWrite(total)

            // 创建Excel文件
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }


    /**
     * 导出Ae平台的商品数据
     */
    private fun exportAe(req: ExportAwaitProductReq, tempDir: File, task: DownloadTask): FileUploadDTO {

        // 获取数据
        val importProductRecords = importProductRecordRepository.list(
            KtQueryWrapper(ImportProductRecord::class.java)
                .eq(ImportProductRecord::processState, ImportProductProcessStateEnum.FAILURE.code)
                .eq(ImportProductRecord::platformId, req.platform!!.platformId)
                .eq(ImportProductRecord::importSource, ImportSourceEnum.PENDING_UPLOAD.code)
                .between(ImportProductRecord::createdTime, req.createdTimeStart, req.createdTimeEnd)
                .orderByDesc(ImportProductRecord::createdTime)
        )

        val result = importProductRecords.map { it ->
            val importProductDTO = it.importData!!.parseJson(ImportAeProductFailDTO::class.java)
            importProductDTO.errorMsg = it.errorMsg
            val attributePairResps = mutableListOf<AttributePairResp>()
            if (Objects.nonNull(importProductDTO.dynamicAttributes)) {
                importProductDTO.dynamicAttributes.forEach { (key, value) ->
                    val attributePairResp = AttributePairResp()
                        .apply {
                            this.attributeName = key
                            this.attributeValue = value
                        }
                    attributePairResps.add(attributePairResp)
                }
                importProductDTO.attributePairResps = attributePairResps
            }
            importProductDTO
        }

        // key=sheet(品类), value=sheetData
        val sheetDataMap = mutableMapOf<String, MutableList<ImportAeProductDTO>>()
        result.forEach { data ->
            sheetDataMap.getOrPut(data.categoryName ?: "") { mutableListOf() }.add(data)
        }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {
                val excelWriter = EasyExcel.write(excelFile).excelType(ExcelTypeEnum.XLSX).build()
                // 将 MutableMap 的 entries 转换为 List 后使用 forEachIndexed
                sheetDataMap.entries.toList().forEachIndexed { index, entry ->
                    val sheetName = entry.key
                    val dataList = entry.value
                    // Ae属性转为动态列
                    val resultAeMap = ImportBaseProductDTO.convertToMapList(dataList)
                    val totalAe = resultAeMap.map { map -> map.values.toList() }

                    val writeSheet = EasyExcel.writerSheet(index, ExcelUtils.replaceSheetName(sheetName))
                        .head(ExcelUtils.extractHeaders(resultAeMap))
                        .build()
                    excelWriter.write(totalAe, writeSheet)
                }
                excelWriter.finish()
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败")
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }


    /**
     * 导出TEMU平台的商品数据
     */
    private fun exportTemu(req: ExportAwaitProductReq, tempDir: File, task: DownloadTask): FileUploadDTO {

        // 获取数据
        val importProductRecords = importProductRecordRepository.list(
            KtQueryWrapper(ImportProductRecord::class.java)
                .eq(ImportProductRecord::processState, ImportProductProcessStateEnum.FAILURE.code)
                .eq(ImportProductRecord::platformId, req.platform!!.platformId)
                .eq(ImportProductRecord::importSource, ImportSourceEnum.PENDING_UPLOAD.code)
                .between(ImportProductRecord::createdTime, req.createdTimeStart, req.createdTimeEnd)
                .orderByDesc(ImportProductRecord::createdTime)
        )

        val result = importProductRecords.map { it ->
            val importProductDTO = it.importData!!.parseJson(ImportTemuProductFailDTO::class.java)
            importProductDTO.errorMsg = it.errorMsg
            val attributePairResps = mutableListOf<AttributePairResp>()
            if (Objects.nonNull(importProductDTO.dynamicAttributes)) {
                importProductDTO.dynamicAttributes.forEach { (key, value) ->
                    val attributePairResp = AttributePairResp()
                        .apply {
                            this.attributeName = key
                            this.attributeValue = value
                        }
                    attributePairResps.add(attributePairResp)
                }
                importProductDTO.attributePairResps = attributePairResps
            }
            importProductDTO
        }

        // key=sheet(品类), value=sheetData
        val sheetDataMap = mutableMapOf<String, MutableList<ImportTemuProductFailDTO>>()
        result.forEach { data ->
            sheetDataMap.getOrPut(data.categoryName ?: "") { mutableListOf() }.add(data)
        }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {
                val excelWriter = EasyExcel.write(excelFile).excelType(ExcelTypeEnum.XLSX).build()
                // 将 MutableMap 的 entries 转换为 List 后使用 forEachIndexed
                sheetDataMap.entries.toList().forEachIndexed { index, entry ->
                    val sheetName = entry.key
                    val dataList = entry.value
                    // Ae属性转为动态列
                    val resultAeMap = ImportBaseProductDTO.convertToMapList(dataList)
                    val totalAe = resultAeMap.map { map -> map.values.toList() }

                    val writeSheet = EasyExcel.writerSheet(index, ExcelUtils.replaceSheetName(sheetName))
                        .head(ExcelUtils.extractHeaders(resultAeMap))
                        .build()
                    excelWriter.write(totalAe, writeSheet)
                }
                excelWriter.finish()
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败")
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
