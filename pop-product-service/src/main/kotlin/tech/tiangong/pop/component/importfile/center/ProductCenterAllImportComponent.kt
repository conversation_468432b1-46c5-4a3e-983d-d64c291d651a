package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.ExcelReader
import com.alibaba.excel.read.listener.PageReadListener
import com.alibaba.excel.read.metadata.ReadSheet
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.dto.export.ProductCenterAttrExportDto
import tech.tiangong.pop.dto.export.ProductCenterBasicInfoExportDto
import tech.tiangong.pop.dto.export.ProductCenterLogisticsPackageExportDto
import tech.tiangong.pop.dto.export.ProductCenterPlatformAttrExportDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum

/**
 * 商品中心-全部信息导入组件
 * 负责导入商品中心的全部信息数据
 */
@Component
@Slf4j
class ProductCenterAllImportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {

        // 1. 初始化结果集合
        val basicInfoList = mutableListOf<ProductCenterBasicInfoExportDto>()
        val logisticsList = mutableListOf<ProductCenterLogisticsPackageExportDto>()
        val platformAttList = mutableListOf<ProductCenterPlatformAttrExportDto>()
        val productAttList = mutableListOf<ProductCenterAttrExportDto>()

        // 2. 创建ExcelReader对象
        val excelReader: ExcelReader = EasyExcel.read(file.inputStream).build()

        try {
            // 3. 获取所有sheet信息
            val sheets: List<ReadSheet> = excelReader.excelExecutor().sheetList()

            // 4. 循环处理每个sheet
            sheets.forEach { sheet ->
                val sheetName = sheet.sheetName
                log.info { "开始读取sheet: $sheetName (索引: ${sheet.sheetNo})" }

                when (sheetName) {
                    // 匹配"商品基本信息"sheet，使用对应的DTO
                    "商品基本信息" -> {
                        val sheetReader = EasyExcel.readSheet(sheet.sheetNo)
                            .head(ProductCenterBasicInfoExportDto::class.java)
                            .registerReadListener(PageReadListener { dataList ->
                                log.info { "sheet[$sheetName]读取到${dataList.size}条数据" }
                                basicInfoList.addAll(dataList)
                            })
                            .build()
                        excelReader.read(sheetReader)
                    }

                    // 匹配"物流包装"sheet，使用对应的DTO
                    "物流包装" -> {
                        val sheetReader = EasyExcel.readSheet(sheet.sheetNo)
                            .head(ProductCenterLogisticsPackageExportDto::class.java)
                            .registerReadListener(PageReadListener { dataList ->
                                log.info { "sheet[$sheetName]读取到${dataList.size}条数据" }
                                logisticsList.addAll(dataList)
                            })
                            .build()
                        excelReader.read(sheetReader)
                    }
                    // 匹配"平台属性"sheet，使用对应的DTO
                    "平台属性" -> {
                        val sheetReader = EasyExcel.readSheet(sheet.sheetNo)
                            .head(ProductCenterPlatformAttrExportDto::class.java)
                            .registerReadListener(PageReadListener { dataList ->
                                log.info { "sheet[$sheetName]读取到${dataList.size}条数据" }
                                platformAttList.addAll(dataList)
                            })
                            .build()
                        excelReader.read(sheetReader)
                    }

                    // 其他sheet视为"商品属性"(品类分sheet)
                    else -> {
                        val sheetReader = EasyExcel.readSheet(sheet.sheetNo)
                            .head(ProductCenterAttrExportDto::class.java)
                            .registerReadListener(PageReadListener { dataList ->
                                log.info { "sheet[$sheetName]读取到${dataList.size}条数据" }
                                productAttList.addAll(dataList)
                            })
                            .build()
                        excelReader.read(sheetReader)
                    }
                }
            }
        } finally {
            // 5. 关闭读取器释放资源
            excelReader.finish()
        }

        // 6. 入库
        /*
           先读取excel
           1. 商品基本信息
           2. 物流包装
           3. 平台属性
           4. 商品属性

           按照不同的sheet insert到不同的importDataType导入任务(必须按照顺序保存, 因为要先执行1生成SPU, 再执行4生成skc)
           通过各自importDataType的导入逻辑各自处理
        */

        importProductRecordRepository.saveBatch(basicInfoList.map {
            ImportProductRecord().apply {
                this.supplyMode = it.supplyMode
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.PRODUCT_BASIC_INFO.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }, 100)

        importProductRecordRepository.saveBatch(logisticsList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.LOGISTICS_PACKAGE.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }, 100)

        importProductRecordRepository.saveBatch(platformAttList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.PLATFORM_ATTR.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }, 100)

        importProductRecordRepository.saveBatch(productAttList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.skc = it.skc
                this.importDataType = ImportDataTypeEnum.PRODUCT_ATTR.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        log.error { "商品中心-全部导入-无导入逻辑: $importData" }
    }
}