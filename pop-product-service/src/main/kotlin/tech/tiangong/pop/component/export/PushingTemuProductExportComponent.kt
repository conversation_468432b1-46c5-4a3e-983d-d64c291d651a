package tech.tiangong.pop.component.export

import com.alibaba.excel.EasyExcel
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType.MICROSOFT_EXCEL
import jakarta.annotation.Resource
import org.apache.commons.io.FileUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.constant.DatePatternConstants
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.callAsync
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.dto.product.ImportTemuPushingFlowerProductDTO
import tech.tiangong.pop.dto.product.TemuPushingProductDTO
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.image.Image
import tech.tiangong.pop.req.product.temu.ProductTemuPageQueryReq
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.service.product.strategy.AbstractProductPublishPlatformExportTaskStrategy.SkcImageInfo
import java.io.File
import java.io.IOException
import java.time.format.DateTimeFormatter
import java.util.concurrent.ExecutorService

/**
 * 导出-已上架-TEMU商品
 * <AUTHOR>
 * @date 2025-6-16 17:57:52
 */
@Component
@Slf4j
class PushingTemuProductExportComponent(
    private val uploaderOssHelper: UploaderOssHelper,
    private val productRepository: ProductRepository,
    private val temuSaleSkcRepository: TemuSaleSkcRepository,
    private val temuSaleSkuRepository: TemuSaleSkuRepository,
    private val pictureRepository: ProductPictureRepository,
    private val temuSaleGoodsRepository: TemuSaleGoodsRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val imageCollectionHelper: ImageCollectionHelper,
) : DownloadTaskInterface() {


    @Resource(name = "imageDownloadExecutor")
    private lateinit var imageDownloadExecutor: ExecutorService

    companion object {
        private val PURE_DATE_FORMAT = DateTimeFormatter.ofPattern(DatePatternConstants.NORM_DATETIME_PATTERN)
        private const val FLOWER_IMAGE_FOLDER = "花型图片"
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ProductTemuPageQueryReq>()
            ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")


        val allTemuSaleGoods = mutableListOf<TemuSaleGoods>()
        try {
            // 分页所有数据
            req.pageNum = 1
            req.pageSize = 500

            while (true) {
                // 获取上架商品数据
                val saleGoodsList = temuSaleGoodsRepository.getTemuPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
                if (saleGoodsList.records.isEmpty()) {
                    break
                }

                val saleGoodsIds = saleGoodsList.records.mapNotNull { it.saleGoodsId }.distinct()
                if (saleGoodsIds.isNotEmpty()) {
                    temuSaleGoodsRepository.listByIds(saleGoodsIds)?.let {
                        allTemuSaleGoods.addAll(it)
                    }
                }
                req.pageNum++
            }


            if (req.exportTypeMode == Bool.YES.code) {
                // 创建花型图片目录
                val flowerImageDir = createFlowerImageDir(tempDir, FLOWER_IMAGE_FOLDER)

                // 并行处理商品数据并生成Excel数据
                val excelDTOList = processProductsParallel(allTemuSaleGoods, tempDir, flowerImageDir)
                if (excelDTOList.isEmpty()) {
                    log.warn { "没有生成有效的已上架花型Excel数据" }
                }

                // 生成Excel文件
                val excelFile = generateSkuImageExcelFile(tempDir, task.taskName!!, excelDTOList)
                // 直接上传Excel文件（不再导出花型原图后，不需要压缩）
                return uploaderOssHelper.createFileUploadDTO(excelFile, MICROSOFT_EXCEL.type())
            } else {

                val processProductsParallelPublish = processProductsParallelPublish(allTemuSaleGoods, tempDir)

                if (processProductsParallelPublish.isEmpty()) {
                    log.warn { "没有生成有效已上架的Excel数据" }
                }

                // 生成Excel文件
                val excelFile =
                    generateSkuImageExcelFilePublish(tempDir, task.taskName!!, processProductsParallelPublish)
                // 直接上传Excel文件（不再导出花型原图后，不需要压缩）
                return uploaderOssHelper.createFileUploadDTO(excelFile, MICROSOFT_EXCEL.type())
            }
        } catch (e: Exception) {
            log.error(e) { "处理花型图导出失败: ${e.message}" }
            throw BusinessException("处理花型图导出失败：${e.message}")
        }
    }


    /**
     * 创建SKU图片Excel数据对象
     */
    private fun createSkuImageDTO(
        sku: TemuSaleSku,
        skc: TemuSaleSkc,
        product: Product,
        saleGoods: TemuSaleGoods,
        imageInfo: SkcImageInfo,
    ): ImportTemuPushingFlowerProductDTO = ImportTemuPushingFlowerProductDTO().apply {
        sellerSku = sku.sellerSku
        spuCode = product.spuCode
        skcCode = skc.skc
        color = skc.color
        size = sku.sizeName
        prototype = product.prototypeNum
        supplyPrice = skc.localPrice?.toPlainString() ?: ""
        shopName = saleGoods.shopName
        skuImageUrlString = imageInfo.ossUrl
        country = sku.countryName
        platformProductId = saleGoods.platformProductId?.toString() ?: ""

        // 处理条形码
        barcode = if (sku.barcodes.isNotBlank()) {
            sku.barcodes!!.parseJsonList(ComboBarcodeInfoDto::class.java)
                .mapNotNull { it.barcode }
                .joinToString(",")
        } else {
            sku.barcode ?: ""
        }

        platformSkuId = sku.platformSkuId
    }


    /**
     * 处理SKU列表数据
     */
    private fun processSkus(
        skuList: List<TemuSaleSku>,
        product: Product,
        tempDir: File,
        picture: ProductPicture?,
        saleGoods: TemuSaleGoods,
    ): List<ImportTemuPushingFlowerProductDTO> {
        if (skuList.isEmpty()) return emptyList()

        // 收集所有需要的 saleSkcId
        val saleSkcIds = skuList.mapNotNull { it.saleSkcId }.toSet()

        // 批量查询 SKC 信息
        val skcMap = temuSaleSkcRepository.listByIds(saleSkcIds).associateBy { it.saleSkcId }

        // 存储已处理的 SKC 图片信息
        val skcImageInfoMap = mutableMapOf<Long, SkcImageInfo>()

        val materialImageUrlString =
            picture?.materialImages?.parseJsonList(Image::class.java)?.mapNotNull { it.ossImageUrl }
                ?.joinToString(",")
                ?: ""

        // 格式化发布日期
        val publishDate = saleGoods.publishTime?.format(PURE_DATE_FORMAT) ?: ""

        // 处理每个SKU
        return skuList.mapNotNull { sku ->
            val skc = skcMap[sku.saleSkcId] ?: run {
                log.warn { "SKC不存在, skcId: ${sku.saleSkcId}" }
                return@mapNotNull null
            }

            // 处理SKC图片（每个SKC只处理一次）
            val imageInfo = skcImageInfoMap.getOrPut(skc.saleSkcId!!) {
                processSkcImage(skc, tempDir)
            }

            // 创建并返回DTO
            createSkuImageDTO(sku, skc, product, saleGoods, imageInfo).apply {
                this.materialImagesUrlString = materialImageUrlString
                this.publishDate = publishDate
            }
        }
    }


    /**
     * 处理SKC图片
     */
    private fun processSkcImage(skc: TemuSaleSkc, tempDir: File): SkcImageInfo {
        return try {
            if (!skc.pictures.isNullOrBlank()) {
                SkcImageInfo(skc.pictures!!, null)
            } else {
                SkcImageInfo("", null)
            }
        } catch (e: Exception) {
            log.error(e) { "处理SKC图片失败, skcId: ${skc.saleSkcId}" }
            SkcImageInfo("", null)
        }
    }


    /**
     * 处理单个产品数据
     */
    private fun processProduct(
        saleGoods: TemuSaleGoods,
        tempDir: File,
        flowerImageDir: File,
    ): List<ImportTemuPushingFlowerProductDTO> {
        try {
            // 获取商品基础信息
            val product = productRepository.getById(saleGoods.productId) ?: run {
                log.warn { "商品不存在, productId: ${saleGoods.productId}" }
                return emptyList()
            }

            // 获取商品图片信息
            val picture = getPicture(product.productId!!)

            // 获取SKU列表
            val skuList = temuSaleSkuRepository.ktQuery()
                .eq(TemuSaleSku::saleGoodsId, saleGoods.saleGoodsId)
//                .`in`(TemuSaleSku::publishState, ProductConstant.PREVIOUSLY_PUBLISHED_STATES)
                .isNotNull(TemuSaleSku::saleGoodsId)
                .isNotNull(TemuSaleSku::platformSkuId)
                .list()

            if (skuList.isEmpty()) {
                log.warn { "商品没有有效的SKU数据, productId: ${saleGoods.productId}" }
                return emptyList()
            }

            // 处理SKU数据
            return processSkus(skuList, product, tempDir, picture, saleGoods)
        } catch (e: Exception) {
            log.error(e) { "处理商品数据失败, productId: ${saleGoods.productId}" }
            return emptyList()
        }
    }


    /**
     * 获取商品图片
     */
    private fun getPicture(productId: Long): ProductPicture? {
        return pictureRepository.getOneByProductId(productId)
    }


    /**
     * 并行处理商品数据
     */
    private fun processProductsParallel(
        saleGoodsList: List<TemuSaleGoods>,
        tempDir: File,
        flowerImageDir: File,
    ): List<ImportTemuPushingFlowerProductDTO> {
        return processItemsParallel(
            saleGoodsList,
            { saleGoods -> processProduct(saleGoods, tempDir, flowerImageDir) },
            imageDownloadExecutor
        ).flatten()
    }


    /**
     * 并行处理列表中的每个元素
     */
    private fun <T, R> processItemsParallel(
        items: List<T>,
        processor: (T) -> R,
        executor: ExecutorService,
    ): List<R> {
        val futures = items.map { item ->
            callAsync(executor) {
                processor(item)
            }
        }

        return futures.mapNotNull { it.join() }
    }


    /**
     * 创建花型图片目录
     */
    private fun createFlowerImageDir(tempDir: File, folderName: String): File {
        val flowerImageDir = File(tempDir, folderName)
        try {
            FileUtils.forceMkdir(flowerImageDir)
        } catch (e: IOException) {
            log.error(e) { "创建花型图片目录失败" }
            throw BusinessException("创建花型图片目录失败：${e.message}")
        }
        return flowerImageDir
    }


    /**
     * 生成Excel文件
     */
    private fun generateSkuImageExcelFile(
        tempDir: File,
        taskName: String,
        dataList: List<ImportTemuPushingFlowerProductDTO>,
    ): File {
        val excelFile = File(tempDir, "$taskName.xlsx")
        EasyExcel.write(excelFile, ImportTemuPushingFlowerProductDTO::class.java)
            .sheet("已上架花型图片")
            .doWrite(dataList)
        return excelFile
    }


    /**
     * 生成Excel文件
     */
    private fun generateSkuImageExcelFilePublish(
        tempDir: File,
        taskName: String,
        dataList: List<TemuPushingProductDTO>,
    ): File {
        val excelFile = File(tempDir, "$taskName.xlsx")
        EasyExcel.write(excelFile, TemuPushingProductDTO::class.java)
            .sheet("已上架商品")
            .doWrite(dataList)
        return excelFile
    }

    /**
     * 并行处理商品数据 已上架
     */
    private fun processProductsParallelPublish(
        saleGoodsList: List<TemuSaleGoods>,
        tempDir: File,
    ): List<TemuPushingProductDTO> {
        return processItemsParallel(
            saleGoodsList,
            { saleGoods -> processPushingProduct(saleGoods, tempDir) },
            imageDownloadExecutor
        ).flatten()
    }


    /**
     * 处理单个产品数据
     */
    private fun processPushingProduct(
        saleGoods: TemuSaleGoods,
        tempDir: File,
    ): List<TemuPushingProductDTO> {
        try {
            // 获取商品基础信息
            val product = productRepository.getById(saleGoods.productId) ?: run {
                log.warn { "商品不存在, productId: ${saleGoods.productId}" }
                return emptyList()
            }


            // 获取SKU列表
            val skuList = temuSaleSkuRepository.ktQuery()
                .eq(TemuSaleSku::saleGoodsId, saleGoods.saleGoodsId)
//                .`in`(TemuSaleSku::publishState, ProductConstant.PREVIOUSLY_PUBLISHED_STATES)
                .isNotNull(TemuSaleSku::saleGoodsId)
                .isNotNull(TemuSaleSku::platformSkuId)
                .list()

            if (skuList.isEmpty()) {
                log.warn { "商品没有有效的SKU数据, productId: ${saleGoods.productId}" }
                return emptyList()
            }

            val resultList = skuList.associateBy { it.country + it.saleSkcId }.values.toList()

            val imageCollection = getImages(product.spuCode!!)


            // 收集所有需要的 saleSkcId
            val saleSkcIds = skuList.mapNotNull { it.saleSkcId }.toSet()

            // 批量查询 SKC 信息
            val skcList = temuSaleSkcRepository.listByIds(saleSkcIds).map { it.skc!! }

            //获取SKC的轮播图
            val productDetailImages: List<ImageAniVo> = imageCollection.productDetailImages
            val skcImages = imageCollectionHelper.fetchSkcImages(
                productDetailImages,
                skcList,
                ImageTypeEnum.PRODUCT_DETAIL.code
            )

            // 处理SKU数据
            return processSkusPublish(resultList, product, saleGoods, skcImages)
        } catch (e: Exception) {
            log.error(e) { "处理商品数据失败, productId: ${saleGoods.productId}" }
            return emptyList()
        }
    }


    /**
     * 处理SKU列表数据
     */
    private fun processSkusPublish(
        skuList: List<TemuSaleSku>,
        product: Product,
        saleGoods: TemuSaleGoods,
        skcImages: Map<String, List<ImageAniVo>>,
    ): List<TemuPushingProductDTO> {
        if (skuList.isEmpty()) return emptyList()

        // 收集所有需要的 saleSkcId
        val saleSkcIds = skuList.mapNotNull { it.saleSkcId }.toSet()

        // 批量查询 SKC 信息
        val skcMap = temuSaleSkcRepository.listByIds(saleSkcIds).associateBy { it.saleSkcId }

        // 处理每个SKU
        return skuList.mapNotNull { sku ->
            val skc = skcMap[sku.saleSkcId] ?: run {
                log.warn { "SKC不存在, skcId: ${sku.saleSkcId}" }
                return@mapNotNull null
            }

            // 创建并返回DTO
            createDTO(sku, skc, product, saleGoods, skcImages)
        }
    }


    /**
     * 创建SKU图片Excel数据对象
     */
    private fun createDTO(
        sku: TemuSaleSku,
        saleSkc: TemuSaleSkc,
        product: Product,
        saleGoods: TemuSaleGoods,
        skcImages: Map<String, List<ImageAniVo>>,
    ): TemuPushingProductDTO = TemuPushingProductDTO().apply {
        spuCode = saleGoods.spuCode
        platformProductId = saleGoods.platformProductId.toString()
        skc = saleSkc.skc
        productSkcId = saleSkc.platformSkcId.toString()
        shopName = saleGoods.shopName
        businessType = ProductShopBusinessType.fromValue(saleGoods.businessType).description
        supplyMode = SupplyModeEnum.getByCode(product.supplyMode)?.desc
        goodsType = product.goodsType
        waves = product.waves
        categoryName = saleGoods.platformCategoryName
        productName = saleGoods.productName
        productNameEn = saleGoods.productNameEn
        supplyPrice = saleSkc.localPrice
        supplyCurrencyType = "CNY"
        declaredPrice = sku.declaredPrice
        declaredCurrencyType = saleGoods.currencyType
        country = sku.countryName
        retailPrice = sku.recommendedPrice
        retailPriceCurrencyType = saleGoods.currencyType
        publishTime = saleGoods.publishTime
        publishUserName = saleGoods.publishUserName
        val skcImageTmpList = skcImages[saleSkc.skc]
        if (skcImageTmpList.isNotEmpty()) {
            this.productImage1 = skcImageTmpList?.getOrNull(0)?.ossImageUrl
            this.productImage2 = skcImageTmpList?.getOrNull(1)?.ossImageUrl
            this.productImage3 = skcImageTmpList?.getOrNull(2)?.ossImageUrl
            this.productImage4 = skcImageTmpList?.getOrNull(3)?.ossImageUrl
            this.productImage5 = skcImageTmpList?.getOrNull(4)?.ossImageUrl
        }
    }


    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }
}