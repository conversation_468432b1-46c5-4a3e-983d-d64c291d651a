package tech.tiangong.pop.component

import org.redisson.api.RedissonClient
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import java.time.Duration

/**
 * 缓存组件
 * <AUTHOR>
 * @date 2025-2-14 15:51:33
 */
@Slf4j
@Component
class CacheComponent(
    private val redissonClient: RedissonClient,
) {

    /**
     * 执行并缓存
     * 如果key有缓存, 就直接返回, 如果没有, 就执行函数, 并且缓存结果, 并且返回结果
     *
     * @param key
     * @param ttl
     * @param function
     * @return
     */
    fun getAndCache(key: String, ttl: Duration, function: () -> Any?): Any? {
        val cache = redissonClient.getBucket<Any>(key)
        val result = cache.get()
        if (result != null) {
            return result
        }
        val value = function()
        if (value != null) {
            cache.set(value)
            cache.expire(ttl)
        }
        return value
    }
}