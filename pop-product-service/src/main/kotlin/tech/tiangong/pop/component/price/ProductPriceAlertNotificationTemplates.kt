package tech.tiangong.pop.component.price

import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.enums.PriceAlertTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.enums.TagPopPriceInterceptEnum
import tech.tiangong.pop.resp.product.ProductPriceAlertCheckResp
import tech.tiangong.pop.utils.format2
import java.math.BigDecimal

/**
 * 价格兜底通知模板工具类
 */
object ProductPriceAlertNotificationTemplates {
    // 标题模板
    private const val COST_PRICE_LOW_TITLE = "【价格兜底-定价成本低于区间】"
    private const val COST_PRICE_HIGH_TITLE = "【价格兜底-定价成本超过区间】"
    private const val RETAIL_PRICE_LOW_TITLE = "【价格兜底-划线价低于区间】"
    private const val RETAIL_PRICE_HIGH_TITLE = "【价格兜底-划线价超过区间】"
    private const val SALE_PRICE_LOW_TITLE = "【价格兜底-售价低于区间】"
    private const val SALE_PRICE_HIGH_TITLE = "【价格兜底-售价超过区间】"
    private const val DEFAULT_PRICE_TITLE = "【价格兜底-价格异常】"

    // 内容模板
    private val COST_PRICE_CONTENT_TEMPLATE = """
        **SPU：** %s        
        **SKC：** %s        
        **品类：** %s     
        **供给方式：** %s        
        **供货价（元）：** %.2f        
        **定价成本（元）：** %.2f        
        **原因：** SKC供货价异常
    """.trimIndent()

    private val SALE_PRICE_CONTENT_TEMPLATE = """
        **SPU：** %s        
        **SKC：** %s        
        **品类：** %s        
        **上架平台：** %s        
        **上架站点：** %s        
        **供给方式：** %s        
        **SPU定价成本：** %.2f        
        **ISP：** %.2f        
        **SP：** %.2f        
        **价格区间：** %s
    """.trimIndent()

    private val RETAIL_PRICE_CONTENT_TEMPLATE = """
        **SPU：** %s        
        **SKC：** %s        
        **品类：** %s        
        **上架平台：** %s        
        **上架站点：** %s        
        **供给方式：** %s        
        **SPU定价成本：** %.2f        
        **ISP：** %.2f        
        **SP：** %.2f        
        **价格区间：** %s
     """.trimIndent()

    private val DEFAULT_CONTENT_TEMPLATE = """
        **SPU：** %s        
        **SKC：** %s        
        **品类：** %s        
        **上架平台：** %s        
        **上架站点：** %s        
        **供给方式：** %s        
        **SPU定价成本：** %.2f        
        **价格异常，请检查价格设置**
    """.trimIndent()

    /**
     * 判断价格异常类型
     */
    fun determinePriceAlertType(checkResult: ProductPriceAlertCheckResp): PriceAlertTypeEnum {
        // 获取第一个异常的SKC
        val abnormalSkc = checkResult.skcResults.firstOrNull { !it.passed }
            ?: return PriceAlertTypeEnum.OTHER

        // 获取第一个异常的SKU（如果有）
        val abnormalSku = abnormalSkc.skuResults.firstOrNull { !it.passed }

        return when {
            // 定价成本过低
            !abnormalSkc.interceptPassed &&
                    abnormalSkc.interceptReasonCodes.contains(TagPopPriceInterceptEnum.COST_PRICE_LOW.code) -> {
                PriceAlertTypeEnum.COST_PRICE_LOW
            }

            // 定价成本过高
            !abnormalSkc.interceptPassed &&
                    abnormalSkc.interceptReasonCodes.contains(TagPopPriceInterceptEnum.COST_PRICE_HIGH.code) -> {
                PriceAlertTypeEnum.COST_PRICE_HIGH
            }

            // 划线价过低 (SKU级别优先)
            abnormalSku != null &&
                    !abnormalSku.interceptPassed &&
                    abnormalSku.interceptReasonCodes.contains(TagPopPriceInterceptEnum.RETAIL_PRICE_LOW.code) -> {
                PriceAlertTypeEnum.RETAIL_PRICE_LOW
            }

            // 划线价过高 (SKU级别优先)
            abnormalSku != null &&
                    !abnormalSku.interceptPassed &&
                    abnormalSku.interceptReasonCodes.contains(TagPopPriceInterceptEnum.RETAIL_PRICE_HIGH.code) -> {
                PriceAlertTypeEnum.RETAIL_PRICE_HIGH
            }

            // 售价过低 (SKU级别优先)
            abnormalSku != null &&
                    !abnormalSku.interceptPassed &&
                    abnormalSku.interceptReasonCodes.contains(TagPopPriceInterceptEnum.SALE_PRICE_LOW.code) -> {
                PriceAlertTypeEnum.SALE_PRICE_LOW
            }

            // 售价过高 (SKU级别优先)
            abnormalSku != null &&
                    !abnormalSku.interceptPassed &&
                    abnormalSku.interceptReasonCodes.contains(TagPopPriceInterceptEnum.SALE_PRICE_HIGH.code) -> {
                PriceAlertTypeEnum.SALE_PRICE_HIGH
            }

            // 默认情况
            else -> PriceAlertTypeEnum.OTHER
        }
    }

    /**
     * 根据异常类型获取通知标题
     */
    fun getNotificationTitle(alertType: PriceAlertTypeEnum): String {
        return when (alertType) {
            PriceAlertTypeEnum.COST_PRICE_LOW -> COST_PRICE_LOW_TITLE
            PriceAlertTypeEnum.COST_PRICE_HIGH -> COST_PRICE_HIGH_TITLE
            PriceAlertTypeEnum.RETAIL_PRICE_LOW -> RETAIL_PRICE_LOW_TITLE
            PriceAlertTypeEnum.RETAIL_PRICE_HIGH -> RETAIL_PRICE_HIGH_TITLE
            PriceAlertTypeEnum.SALE_PRICE_LOW -> SALE_PRICE_LOW_TITLE
            PriceAlertTypeEnum.SALE_PRICE_HIGH -> SALE_PRICE_HIGH_TITLE
            PriceAlertTypeEnum.OTHER -> DEFAULT_PRICE_TITLE
        }
    }

    /**
     * 根据异常类型生成通知内容
     */
    fun getNotificationContent(
        alertType: PriceAlertTypeEnum,
        product: Product,
        checkResult: ProductPriceAlertCheckResp
    ): String {
        // 获取第一个异常的SKC
        val abnormalSkc = checkResult.skcResults.firstOrNull { !it.passed }
            ?: return "没有发现价格异常"

        // 获取第一个异常的SKU（如果有）
        val abnormalSku = abnormalSkc.skuResults.firstOrNull { !it.passed }

        return when (alertType) {
            PriceAlertTypeEnum.COST_PRICE_LOW, PriceAlertTypeEnum.COST_PRICE_HIGH -> {
                String.format(
                    COST_PRICE_CONTENT_TEMPLATE,
                    product.spuCode,
                    abnormalSkc.skc,
                    product.categoryName,
                    SupplyModeEnum.getByCode(product.supplyMode)?.desc ?: "未知",
                    abnormalSkc.supplyPrice,
                    abnormalSkc.costPrice
                )
            }
            PriceAlertTypeEnum.RETAIL_PRICE_LOW, PriceAlertTypeEnum.RETAIL_PRICE_HIGH -> {
                val priceRange = formatPriceRange(checkResult.retailPriceMin, checkResult.retailPriceMax)

                String.format(
                    RETAIL_PRICE_CONTENT_TEMPLATE,
                    product.spuCode,
                    abnormalSkc.skc,
                    product.categoryName,
                    checkResult.platformName,
                    checkResult.countryCode,
                    SupplyModeEnum.getByCode(product.supplyMode)?.desc ?: "未知",
                    abnormalSkc.costPrice,
                    (abnormalSku?.retailPrice ?: abnormalSkc.retailPrice),
                    (abnormalSku?.salePrice ?: abnormalSkc.salePrice),
                    priceRange
                )
            }
            PriceAlertTypeEnum.SALE_PRICE_LOW, PriceAlertTypeEnum.SALE_PRICE_HIGH -> {
                val priceRange = formatPriceRange(checkResult.salePriceMin, checkResult.salePriceMax)

                String.format(
                    SALE_PRICE_CONTENT_TEMPLATE,
                    product.spuCode,
                    abnormalSkc.skc,
                    product.categoryName,
                    checkResult.platformName,
                    checkResult.countryCode,
                    SupplyModeEnum.getByCode(product.supplyMode)?.desc ?: "未知",
                    abnormalSkc.costPrice,
                    (abnormalSku?.retailPrice ?: abnormalSkc.retailPrice),
                    (abnormalSku?.salePrice ?: abnormalSkc.salePrice),
                    priceRange
                )
            }
            PriceAlertTypeEnum.OTHER -> {
                String.format(
                    DEFAULT_CONTENT_TEMPLATE,
                    product.spuCode,
                    abnormalSkc.skc,
                    product.categoryName,
                    checkResult.platformName,
                    checkResult.countryCode,
                    SupplyModeEnum.getByCode(product.supplyMode)?.desc ?: "未知",
                    abnormalSkc.costPrice
                )
            }
        }
    }

    /**
     * 格式化价格区间
     */
    private fun formatPriceRange(minPrice: BigDecimal?, maxPrice: BigDecimal?): String {
        val min = minPrice?.format2() ?: "无下限"
        val max = maxPrice?.format2() ?: "无上限"
        return "$min ~ $max"
    }
}