package tech.tiangong.pop.component.temu

import com.alibaba.fastjson2.JSONArray
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.req.*
import tech.tiangong.eis.temu.req.TemuGoodsAddReq.*
import tech.tiangong.eis.temu.resp.TemuAttrsGetResp
import tech.tiangong.eis.temu.resp.TemuGoodsAddResp
import tech.tiangong.eis.temu.resp.TemuVideoUploadResultGetResp
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto
import tech.tiangong.pop.common.enums.*
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.config.TemuProperties
import tech.tiangong.pop.constant.ImageConstants.FileName.SEP
import tech.tiangong.pop.constant.ImageConstants.Url
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.constant.RedisConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.mq.MessageRecord
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.CategoryItemDTO
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.PublishPlatformAttributeDTO
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.external.EisCenterClientExternal
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.ProductPublishHelper
import tech.tiangong.pop.helper.ProductPublishTemuHelper
import tech.tiangong.pop.req.product.CountryShippingWarehouse
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.service.TemuService
import tech.tiangong.pop.service.mq.MessageRecordService
import tech.tiangong.pop.utils.ImageUtils
import tech.tiangong.sdp.common.req.ProductOnlineNoticeReq
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ExecutorService


/**
 * Lazada 创建/更新商品
 */
@Component
@Slf4j
class TemuUpdateProductComponent(
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val temuSaleGoodsRepository: TemuSaleGoodsRepository,
    private val temuSaleSkcRepository: TemuSaleSkcRepository,
    private val temuSaleSkuRepository: TemuSaleSkuRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val inspirationClientExternal: InspirationClientExternal,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val eisCenterClientExternal: EisCenterClientExternal,
    private val temuCategoryRepository: TemuCategoryRepository,
    private val temuService: TemuService,
    private val temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository,
    private val lockComponent: LockComponent,
    private val productPublishTemuHelper: ProductPublishTemuHelper,
    private var productSaleAttributesV2Repository: ProductSaleAttributesV2Repository,
) {

    @Autowired
    private lateinit var temuProperties: TemuProperties

    @Autowired
    private lateinit var messageRecordService: MessageRecordService

    /**
     * 固定平台枚举-Temu
     */
    private val platformEnum = PlatformEnum.TEMU
    private val channelEnum = ChannelEnum.OTHER


    @Resource(name = "asyncExecutor")
    private lateinit var asyncExecutor: ExecutorService

    /**
     * 创建/更新商品
     */
    fun updateProduct(shopId: Long, saleGoods: TemuSaleGoods, temuSpuId: Long? = null) {
        val logPrefix =
            "[TemuProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        log.info { "${logPrefix}开始创建/更新商品" }

        temuSaleGoodsRepository.updateSyncState(saleGoods.saleGoodsId!!, PlatformSyncStateEnum.PROCESSING)

        val productId = saleGoods.productId!!
        val product = productRepository.getById(productId) ?: throw BusinessException("未设置商品Product!")
        val spuCode = saleGoods.spuCode!!
        val saleGoodsId = saleGoods.saleGoodsId!!
        var requestParams: String? = null
        var respParams: String? = null
        var opType: Int? = null

        val shop = shopRepository.getById(shopId) ?: throw BusinessException("店铺不存在 shopId=$shopId")
        val saleSkcList = temuSaleSkcRepository.findBySaleGoodsId(saleGoodsId)
            .takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKC!")

        saleGoods.productName.takeIf { it.isNotBlank() } ?: throw BusinessException("商品标题不能为空")

        val saleSkuList = temuSaleSkuRepository.ktQuery()
            .`in`(TemuSaleSku::saleSkcId, saleSkcList.map { it.saleSkcId }.toSet())
            .eq(TemuSaleSku::enableState, Bool.YES.code)
            .list().takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKU!")

        val saleSkuListMap: Map<Long?, List<TemuSaleSku>> = saleSkuList.groupBy { it.saleSkcId }

//        //查询商品属性
//        var platformAttributes = saleGoodsRepository.findPlatformAttributes(
//            product.categoryId!!,
//            platformEnum.platformId,
//            saleGoods.productId!!
//        )
//        if (CollectionUtils.isEmpty(platformAttributes)) {
//            throw BaseBizException("属性面料成分不能为空，请在品类管理关联面料属性")
//        }
//        platformAttributes = platformAttributes.filter { k ->
//            StringUtils.isNotBlank(k.platformAttributeKeyName) && StringUtils.isNotBlank(k.platformAttributeValue)
//        }

        val platformAttributes = productSaleAttributesV2Repository
            .findPlatformAttributes(product.categoryId!!, platformEnum.platformId, saleGoods.saleGoodsId!!)
            .filter { k -> StringUtils.isNoneBlank(k.platformAttributeCode, k.platformAttributeKeyName) }
            .takeIf { it.isNotEmpty() }
            ?: throw BusinessException("属性不能为空，请在品类管理关联属性，或检查平台属性是否正确设置")

        //校验品类
        val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
            product.categoryId!!,
            platformEnum.platformId,
            ChannelEnum.OTHER.channelId
        ) ?: throw PublishAscBizException("找不到品类映射信息")

        if (categoryMapping.platformCategoryId.isNullOrBlank()) {
            throw PublishAscBizException("找不到平台品类ID")
        }

        val temuCategory = temuCategoryRepository.getByCategoryId(categoryMapping.platformCategoryId!!.toLong())
            ?: throw PublishAscBizException("找不到平台品类")


        val categoryItemDTOS = temuCategory.categoryPathList?.parseJsonList(CategoryItemDTO::class.java)

        if (categoryItemDTOS.isNullOrEmpty()) {
            throw PublishAscBizException("解析不到 平台品类的叶子类")
        }
        //平台属性原始数据 用来映射
        val attrsByCategoryWithCache =
            temuService.getAttrsByCategoryWithCache(categoryMapping.platformCategoryId!!.toInt())
                ?: throw PublishAscBizException("解析不到 平台属性的原始数据")


        // 检查包装信息
        val packageSpecs = saleGoods.packageSpecs?.parseJson(TemuProductOuterPackage::class.java)
        if (packageSpecs == null || packageSpecs.packageShapeId == null || packageSpecs.packageTypeId == null) {
            throw PublishAscBizException("缺少货品外包装信息")
        }


        validateSaleSku(saleSkcList, saleSkuList, saleGoods)
        //校验图库资料是否存在
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
            ?: throw PublishAscBizException("图库不存在，请先上传图片")
        // 校验是否缺少101-106以及规格图的图片资料
//            validateImageData(imageRepository)


        try {

//            temuSaleSkcRepository.updateBatchById(
//                newSkcList.map { SaleSkc().apply { saleSkcId = it.saleSkcId; pictures = it.pictures } }
//            )

            saleGoods.apply {
                if (this.platformCategoryId != categoryMapping.platformCategoryId) {
                    this.platformCategoryId = categoryMapping.platformCategoryId
                }
                if (this.platformCategoryName != categoryMapping.platformCategoryName) {
                    this.platformCategoryName = categoryMapping.platformCategoryName
                }
            }


            val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)

            //获取图包
            val imageCollectionDTO = getImages(product.spuCode!!)

            // 组装Temu商品参数, 并调用API
            if (saleGoods.platformProductId == null) {
                // 创建商品
                opType = PlatformOperatorTypeEnum.ACTIVE.code

                val buildSingleProduct = buildSingleProduct(
                    product,
                    logPrefix,
                    saleGoods,
                    saleSkcList,
                    saleSkuListMap,
                    categoryItemDTOS,
                    platformAttributes,
                    imageCollectionDTO,
                    attrsByCategoryWithCache,
                    shop,
                    detailedImageSetting
                )

                requestParams = buildSingleProduct.toJson()
                val temuGoodsAdd = eisCenterClientExternal.temuGoodsAdd(buildSingleProduct)
                respParams = temuGoodsAdd?.toJson()

                //更新标识
                val updateProduct = Product()
                updateProduct.productId = product.productId
                updateProduct.isUpdate = Bool.NO.code
                product.setIsUpdate(Bool.NO.code)
                productRepository.updateById(updateProduct)

                // 创建成功, 信息修改到sale
                updateByTemuAddResp(temuGoodsAdd, saleGoods)
                runAsync(asyncExecutor) { pushToAIDC(product, saleGoods) }
            }

            temuSaleGoodsRepository.updateSyncState(saleGoods.saleGoodsId!!, PlatformSyncStateEnum.SUCCESS)

            // 更新product状态
            updateProductState(product, saleGoods, imageCollectionDTO, logPrefix)


            //发布后记录发布时使用的图包版本
            updateImageVersionNumAfterPublish(saleGoods.productId!!)

            runAsync(asyncExecutor) { handleSuccessSyncLog(saleGoods, requestParams, respParams, opType) }
            runAsync(asyncExecutor) { pushPublishImage(product, imageRepository, saleSkcList) }
        } catch (e: Exception) {
            runAsync(asyncExecutor) {
                var errorMessage = ExceptionUtils.getRootCauseMessage(e) ?: e::class.simpleName ?: "未知错误"
                try {
                    val businessTypeName = ProductShopBusinessType.fromValue(saleGoods.businessType).description
                    val siteNames = saleGoods.site?.parseJsonList(TemuSiteVo::class.java)?.mapNotNull { it.countryName }?.distinct()?.joinToString(",")
                    errorMessage = "店铺运营:$businessTypeName 店铺:${saleGoods.shopName} 站点:$siteNames 错误:$errorMessage"
                } catch (e: Exception) {
                    log.error(e) { "Temu上架失败-组装异常信息失败" }
                }
                handleFailure(product, saleGoods, requestParams, respParams, opType, errorMessage, logPrefix, temuSpuId)
            }
            throw e
        }
    }

    fun updateImageVersionNumAfterPublish(productId: Long) {
        val product = productRepository.getById(productId)
        if (product.spuCode != null && product.spuCode.isNotBlank()) {
            val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
            if (imageRepository != null) {
                val updateProduct = Product()
                updateProduct.productId = product.productId
                updateProduct.imageVersionNum = imageRepository.versionNum
                if (product.imagePackageState == ProductImagePackageStateEnum.UPDATED.code) {
                    updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                }
                productRepository.updateById(updateProduct)
            }
        }

    }

    /**
     * 处理失败的同步日志
     */
    private fun handleFailure(
        product: Product,
        saleGoods: TemuSaleGoods,
        requestParams: String?,
        respParams: String?,
        opType: Int?,
        errorMessage: String?,
        logPrefix: String,
        temuSpuId: Long? = null
    ) {
        log.error { "${logPrefix}发布商品到TEMU失败, error=${errorMessage}" }
        productRepository.updateById(Product().apply {
            this.productId = product.productId
            this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
        })

        if (saleGoods.platformSyncState != PlatformSyncStateEnum.SUCCESS.code) {
            temuSaleGoodsRepository.updateById(TemuSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
            })
        }

        addErrorSyncLog(
            platformEnum.platformName,
            saleGoods.shopName,
            saleGoods,
            errorMessage,
            requestParams,
            respParams,
            opType,
            temuSpuId
        )
    }

    /**
     * 构建上传商品对象
     */
    private fun buildSingleProduct(
        product: Product,
        logPrefix: String,
        saleGoods: TemuSaleGoods,
        saleSkcList: List<TemuSaleSkc>,
        saleSkuListMap: Map<Long?, List<TemuSaleSku>>,
        categoryItemDTOS: List<CategoryItemDTO>,
        platformAttributes: List<PublishPlatformAttributeDTO>,
        imageCollectionDTO: ImageCollectionDTO,
        temuAttrsGetResp: TemuAttrsGetResp,
        shop: Shop,
        detailedImageSetting: TemuSkcDetailedImage?,
    ): TemuGoodsAddReq {

        //平台数据属性数据整理
//        val propertyItemMap: Map<String, TemuAttrsGetResp.PropertyItem>? =
//            temuAttrsGetResp.properties?.associateBy { it.pid!!.toString() }

        val propertyItemMap = temuAttrsGetResp.properties
            ?.groupBy { it.pid.toString() + "-" + it.templatePid }

        //商品属性
        val buildProductProperties = buildProductProperties(platformAttributes, logPrefix, propertyItemMap)

        // 校验平台必填属性
        val notFoundRequiredPlatformAttrName = mutableListOf<TemuAttrsGetResp.PropertyItem>()
        val requiredPlatformAttributes = temuAttrsGetResp.properties
            ?.filter {
                // 必填属性
                it.required != null && it.required == true
            }
            ?.filter {
                // 过滤销售属性(如: 颜色,尺码)
                it.isSale == null || it.isSale == false
            }
        if (requiredPlatformAttributes.isNotEmpty()) {
            requiredPlatformAttributes?.forEach { requiredPlatform ->
                val find =
                    buildProductProperties.find { Objects.equals(requiredPlatform.pid?.toString() + "-" + requiredPlatform.templatePid, it.pid.toString() + "-" + it.templatePid) }
                if (find == null) {
                    notFoundRequiredPlatformAttrName.add(requiredPlatform)
                }
            }
        }


        val refPids = buildProductProperties.groupBy { it.refPid.toString() }
        val vidMap = buildProductProperties.map { it.vid }.toSet()

        val productPropertyReqs = notFoundRequiredPlatformAttrName
            .filter { it.controlType == 0 }
            .mapNotNull { attr ->
                //属性值如果存在showCondition是子类属性 如果找到父类值了说明是必填  如果说当前是成分需要填写 需要选择光面才填写
                val shouldProcess = attr.showCondition?.find { condition ->
                    condition.parentRefPid?.toString() in refPids.keys
                }

                val hasValidParentVid = shouldProcess?.parentVids
                    ?.map { it.toString() }
                    ?.any { it in vidMap.map { v -> v.toString() } }
                    ?: false

                if (shouldProcess != null && hasValidParentVid) {
                    // 条件成立时的处理逻辑
                    ProductPropertyReq(
                        templatePid = attr.templatePid ?: 0,
                        pid = attr.pid ?: 0,
                        refPid = attr.refPid ?: 0,
                        propName = attr.name ?: "",
                        vid = 0,
                        propValue = "90",
                        valueUnit = attr.valueUnit?.firstOrNull() ?: "",
                        numberInputValue = "",
                        valueExtendInfo = ""
                    )
                } else {
                    // 条件不成立时的默认处理
                    null
                }
            }

        val allProperties = buildProductProperties + productPropertyReqs
        //清空未填的必填属性
        notFoundRequiredPlatformAttrName.clear()

        if (requiredPlatformAttributes.isNotEmpty()) {
            requiredPlatformAttributes?.forEach { requiredPlatform ->
                val find = allProperties.find { Objects.equals(requiredPlatform.pid?.toString() + "-" + requiredPlatform.templatePid, it.pid.toString() + "-" + it.templatePid) }
                //必填属性是没有父类属性的 就当作  平台必填属性  showCondition存在说明 有上级关联属性
                if (find == null && requiredPlatform.showCondition.isNullOrEmpty() && requiredPlatform.parentTemplatePid == 0) {
                    notFoundRequiredPlatformAttrName.add(requiredPlatform)
                }
            }
        }

        if (notFoundRequiredPlatformAttrName.isNotEmpty()) {
            throw BusinessException(
                "平台必填属性需补充: ${
                    notFoundRequiredPlatformAttrName.mapNotNull { it.name }.joinToString(";")
                }"
            )
        }

        //平台数据
        val itemMap = temuAttrsGetResp.properties?.associateBy { it.name } ?: emptyMap()

        //颜色 平台数据
        val colorPropertyItem = itemMap["颜色"] ?: throw PublishAscBizException("不存在颜色映射")
        //当前属性值数组
        val colorValueMap = colorPropertyItem.values?.associateBy { it.value } ?: emptyMap()

        //尺码 平台数据
        val platformSizeItem = itemMap["尺码"] ?: throw PublishAscBizException("平台尺码映射关系不存在")
        val platformSizeItemMap = platformSizeItem.values?.associateBy { it.value } ?: emptyMap()


        //获取SKC的轮播图
        val productDetailImages: List<ImageAniVo> = imageCollectionDTO.productDetailImages
        val skcList = saleSkcList.map { it.skc!! }
        val skcImages = imageCollectionHelper.fetchSkcImages(
            productDetailImages,
            skcList,
            ImageTypeEnum.PRODUCT_DETAIL.code
        )

        //上传TEMU返回的图片 轮播图格式
        val skcImageMap: Map<String, List<String>> = skcList.associate { skc ->
            val imageAniVos = skcImages[skc].takeIf { !it.isNullOrEmpty() }
                ?: throw PublishAscBizException("当前SKC没有有效轮播图: $skc")
            val carouselImages = uploadImages(imageAniVos, shop, 0, 2, 1)
            skc to carouselImages
        }

        // 如果有视频的话 处理视频
        val videoResult: MutableList<TemuVideoUploadResultGetResp> = mutableListOf()
        if (imageCollectionDTO.videos.isNotEmpty()) {

            imageCollectionDTO.videos.forEach { video ->
                try {
                    handleVideoAsync(video.ossImageUrl!!, shop, product.spuCode!!, videoResult)
                } catch (e: Exception) {
                    log.error(e) { "[TemuProductSync] 启动视频异步处理失败，视频地址: ${video.ossImageUrl}, 错误: ${e.message}" }
                    // 这里不抛出异常，避免影响主流程
                }
            }
        }


        //素材图
        val mainSkc = detailedImageSetting?.skc ?: skcList.firstOrNull()
        ?: throw PublishAscBizException("无效SKC详情配置")

        // 2. 获取素材图（空安全处理）
        val mainImage = skcImages[mainSkc]?.firstOrNull()

        // 3. 构建素材图（命名参数）
        val carouselUrls = uploadImages(listOf(mainImage!!), shop, 0, 1, 1)


        //创建尺码表临时模板
        val sizeChartsTemplateCreateResp = eisCenterClientExternal.goodsSizeChartsTemplateCreate(
            TemuSizeChartsTemplateCreateReq(saleGoods.sizeTemplateCode?.toLong()).apply {
                region = shop.country
                shortCode = shop.shortCode
            })


        //*********下面构建数据*******************//
        var productWarehouseRouteReq: ProductWarehouseRouteReq? = null
        //库存仓库配置（半托管必传）
        if (saleGoods.businessType == 2) {

            val countryShippingWarehouses =
                saleGoods.countryShippingWarehouse?.parseJsonList(CountryShippingWarehouse::class.java)

            // 创建仓库到站点的映射表
            val warehouseToSites = mutableMapOf<String, MutableList<Int>>()

            countryShippingWarehouses?.forEach { country ->
                val countryCode = country.countryCode?.toIntOrNull() ?: 0
                country.shippingWarehouses?.forEach { warehouse ->
                    warehouseToSites
                        .computeIfAbsent(warehouse.shippingWarehouseCode!!) { mutableListOf() }
                        .add(countryCode)
                }
            }

            // 构建目标数据结构
            val targetRoutes = warehouseToSites.map { (warehouseId, siteIds) ->
                ProductWarehouseRouteReq.TargetRoute(
                    siteIdList = siteIds.distinct(),
                    warehouseId = warehouseId
                )
            }

            productWarehouseRouteReq = ProductWarehouseRouteReq(targetRoutes)
        }

        val carouseVideoReq = videoResult.map {
            CarouseVideoReq(it.vid, it.coverUrl, it.videoUrl, it.width, it.height)
        }


        val materialLanguages = saleGoods.materialLanguages?.parseJsonList(MaterialLanguage::class.java)
        if (materialLanguages.isNullOrEmpty()) {
            throw BusinessException("多素材语言不能为空至少要一个en")
        }

        val any = materialLanguages.any { it -> it.languageCode!! == "en" }
        if (!any) {
            throw BusinessException("多素材语言要一个en")
        }

        val productI18nReqs = materialLanguages.map { lang ->
            requireNotNull(lang.languageCode) { "Language code cannot be null" }

            ProductI18nReq(
                language = lang.languageCode!!,
                productName = when {
                    lang.languageCode == "en" ->
                        saleGoods.productNameEn ?: throw IllegalArgumentException("English product name required")

                    else ->
                        saleGoods.productName ?: throw IllegalArgumentException("Default product name required")
                }
            )
        }


        //商品销售规格
        val buildSpecProperties =
            buildSpecProperties(
                platformAttributes,
                logPrefix,
                colorPropertyItem,
                colorValueMap,
                saleSkcList,
                shop,
                saleSkuListMap,
                platformSizeItem,
                platformSizeItemMap
            )

        //SKC 和 SKU
        val buildSkcAndSku = buildSkcAndSku(
            product,
            saleGoods,
            saleSkcList,
            saleSkuListMap,
            imageCollectionDTO,
            colorPropertyItem,
            buildSpecProperties,
            platformSizeItem,
            platformSizeItemMap,
            skcImageMap
        )

        /**
         * 货品外包装信息
         */
        val temuProductOuterPackage = saleGoods.packageSpecs?.parseJson(TemuProductOuterPackage::class.java)
            ?: throw BusinessException("外包装信息不存在")

        temuProductOuterPackage.imageUrls

        val productOuterPackageReq = buildProductOuterPackageReq(temuProductOuterPackage)
        //外包装图片
        val buildProductOuterPackageImageReq =
            buildProductOuterPackageImageReq(temuProductOuterPackage, shop)

        /**
         * 半托管货品配送信息请求
         */
        val buildProductShipmentReq = buildProductShipmentReq(saleGoods)

        /**
         * 商品模特列表请求
         */
        val buildGoodsModelReqs = buildGoodsModelReqs(saleGoods, platformSizeItemMap)


        //半托管相关信息 站点
        var semiManagedRequest: ProductSemiManagedReq? = null
        if (saleGoods.businessType == 2) {
            semiManagedRequest = buildSkcAndSku.firstOrNull()?.let { buildProductSemiManagedReq(saleGoods, it) }
        }

        /**
         * 货品仓配供应链侧扩展属性请求
         */
        val buildProductWhExtAttrReq = buildProductWhExtAttrReq(saleGoods)

        /**
         * 商品详情图
         */
        val buildGoodsLayerDecorationReq = buildGoodsLayerDecorationReq(saleGoods, shop, imageCollectionDTO, skcImageMap, detailedImageSetting, skcList)

        return TemuGoodsAddReq(
            cat1Id = categoryItemDTOS.getOrNull(0)?.categoryId?.toInt() ?: 0,
            cat2Id = categoryItemDTOS.getOrNull(1)?.categoryId?.toInt() ?: 0,
            cat3Id = categoryItemDTOS.getOrNull(2)?.categoryId?.toInt() ?: 0,
            cat4Id = categoryItemDTOS.getOrNull(3)?.categoryId?.toInt() ?: 0,
            cat5Id = categoryItemDTOS.getOrNull(4)?.categoryId?.toInt() ?: 0,
            cat6Id = categoryItemDTOS.getOrNull(5)?.categoryId?.toInt() ?: 0,
            cat7Id = categoryItemDTOS.getOrNull(6)?.categoryId?.toInt() ?: 0,
            cat8Id = categoryItemDTOS.getOrNull(7)?.categoryId?.toInt() ?: 0,
            cat9Id = categoryItemDTOS.getOrNull(8)?.categoryId?.toInt() ?: 0,
            cat10Id = categoryItemDTOS.getOrNull(9)?.categoryId?.toInt() ?: 0,
            productCarouseVideoReqList = carouseVideoReq,

            productWarehouseRouteReq = productWarehouseRouteReq,
            productI18nReqs = productI18nReqs,
            productName = saleGoods.productName!!,
            carouselImageUrls = skcImageMap.getOrDefault(
                detailedImageSetting?.skc ?: skcList.firstOrNull(),
                emptyList()
            ),
            sizeTemplateIds = listOf(sizeChartsTemplateCreateResp?.tempBusinessId!!),
            materialImgUrl = carouselUrls.firstOrNull() ?: "",
            productPropertyReqs = allProperties,
            productSpecPropertyReqs = buildSpecProperties,
            productSkcReqs = buildSkcAndSku,
            goodsModelReqs = buildGoodsModelReqs,
            showSizeTemplateIds = listOf(sizeChartsTemplateCreateResp.tempBusinessId),
            productOuterPackageReq = productOuterPackageReq,
            personalizationSwitch = saleGoods.isCustomized,
            productSemiManagedReq = semiManagedRequest,
            productShipmentReq = buildProductShipmentReq,
            productWhExtAttrReq = buildProductWhExtAttrReq,
            productOuterPackageImageReqs = buildProductOuterPackageImageReq,
            goodsLayerDecorationReqs = buildGoodsLayerDecorationReq,
            productSaleExtAttrReq = buildProductSaleExtAttrReq(saleGoods)
        ).apply {
            this.shortCode = shop.shortCode
            this.region = shop.country
        }
    }


    /**
     * 商品属性
     */
    private fun buildProductProperties(
        platformAttributes: List<PublishPlatformAttributeDTO>,
        logPrefix: String,
        propertyMap: Map<String, List<TemuAttrsGetResp.PropertyItem>>?,
    ): List<ProductPropertyReq> {
        log.info { "${logPrefix}开始转换平台属性产品属性，属性数量: ${platformAttributes.size}" }
        return platformAttributes.map { it: PublishPlatformAttributeDTO ->
            //当前属性名称信息 和 属性值数组
            val propertyItems: List<TemuAttrsGetResp.PropertyItem>? = propertyMap?.get(it.platformAttributeKeyName)
            if (propertyItems.isNullOrEmpty()) {
                throw PublishAscBizException("映射不到平台属性数据组")
            }

            // 匹配平台属性(有可能会重复, 若重复则使用必选的, 单个则直接用)
            val items = if (propertyItems.size > 1) {
                propertyItems.firstOrNull { it.required == true }
            } else {
                propertyItems.firstOrNull()
            }


            //当前属性值数组
            val valueItemMap = items?.values?.associateBy { it.vid.toString() } ?: emptyMap()
            val valueItem = valueItemMap[it.platformAttributeCode]


            ProductPropertyReq(
                templatePid = items?.templatePid!!,
                pid = items.pid!!,
                refPid = items.refPid!!,
                propName = items.name ?: "",
                vid = valueItem?.vid ?: 0,
                propValue = when (items.controlType) {
                    0 -> "90"
                    else -> valueItem?.value ?: ""
                },
                valueUnit = items.valueUnit?.first().toString(),
                numberInputValue = when (items.controlType) {
                    2 -> it.attributeValue?.takeIf { str -> str.contains(SEP) }?.substringAfterLast(SEP) ?: ""
                    3 -> it.attributeValue?.takeIf { str -> str.contains(SEP) }?.substringAfterLast(SEP) ?: ""
                    16 -> it.attributeValue?.takeIf { str -> str.contains(SEP) }?.substringAfterLast(SEP) ?: ""
//                    0 -> it.attributeValue ?: ""
                    else -> ""
                },
                valueExtendInfo = ""
            )
        }
    }


    private fun buildSpecProperties(
        platformAttributes: List<PublishPlatformAttributeDTO>,
        logPrefix: String,
        propertyItem: TemuAttrsGetResp.PropertyItem,
        valueItemMap: Map<String, TemuAttrsGetResp.PropertyItem.ValueItem>,
        saleSkcList: List<TemuSaleSkc>,
        shop: Shop,
        saleSkuListMap: Map<Long?, List<TemuSaleSku>>,
        sizePropertyItem: TemuAttrsGetResp.PropertyItem,
        platformSizeItemMap: Map<String, TemuAttrsGetResp.PropertyItem.ValueItem>,

        ): List<ProductSpecPropertyReq> {
        log.info { "${logPrefix}开始转换平台规格属性产品属性，属性数量: ${platformAttributes.size}" }

        val propertyReqs = saleSkcList.map { skc ->
            val valueItem = valueItemMap[skc.color]
            val specId = if (valueItem == null) {
                eisCenterClientExternal.goodsSpecCreate(TemuGoodsSpecCreateReq().apply {
                    shortCode = shop.shortCode
                    region = shop.country
                    parentSpecId = propertyItem.parentSpecId
                    specName = skc.color
                })?.specId ?: 0
            } else {
                valueItem.specId ?: 0
            }

            ProductSpecPropertyReq(
                templatePid = propertyItem.templatePid!!,
                pid = propertyItem.pid!!,
                parentSpecId = propertyItem.parentSpecId ?: 0,
                specId = specId,
                valueGroupId = valueItem?.group?.id ?: 0,
                numberInputValue = "",
                refPid = propertyItem.refPid!!,
                vid = valueItem?.vid ?: 0,
                propName = propertyItem.name!!,
                propValue = valueItem?.value ?: skc.color!!,
                parentSpecName = propertyItem.name!!,
                specName = valueItem?.value ?: skc.color!!,
                valueGroupName = valueItem?.group?.name ?: "",
                valueUnit = propertyItem.valueUnit?.firstOrNull() ?: ""
            )
        }

        //尺码规格
        val sizes = saleSkuListMap[saleSkcList.first().saleSkcId]?.map { it.sizeName }?.toSet()
        val productSpecPropertyReqs = sizes?.map { s ->
            //先获取尺码和平台的尺码关系再获取平台的尺码属性
            val valueItem = s
                ?.let { temuProperties.sizeMapping[it] }
                ?.let { platformSizeItemMap[it] }
                ?: throw IllegalArgumentException("无效尺码: $s")


            ProductSpecPropertyReq(
                templatePid = sizePropertyItem.templatePid!!,
                pid = sizePropertyItem.pid!!,
                parentSpecId = sizePropertyItem.parentSpecId ?: 0,
                specId = valueItem.specId ?: 0,
                valueGroupId = valueItem.group?.id ?: 0,
                numberInputValue = "",
                refPid = sizePropertyItem.refPid!!,
                vid = valueItem.vid,
                propName = sizePropertyItem.name!!,
                propValue = valueItem.value,
                parentSpecName = sizePropertyItem.name!!,
                specName = valueItem.value,
                valueGroupName = valueItem.group?.name ?: "",
                valueUnit = sizePropertyItem.valueUnit?.firstOrNull() ?: ""
            )
        } ?: emptyList()

        return propertyReqs + productSpecPropertyReqs
    }


    //skc和sku
    fun buildSkcAndSku(
        product: Product,
        saleGoods: TemuSaleGoods,
        saleSkcList: List<TemuSaleSkc>,
        saleSkuListMap: Map<Long?, List<TemuSaleSku>>,
        imageCollectionDTO: ImageCollectionDTO,
        colorPropertyItem: TemuAttrsGetResp.PropertyItem,
        productSpecPropertyReqs: List<ProductSpecPropertyReq>,
        sizePropertyItem: TemuAttrsGetResp.PropertyItem,
        platformSizeItemMap: Map<String, TemuAttrsGetResp.PropertyItem.ValueItem>,
        skcImageMap: Map<String, List<String>>,
    ): List<ProductSkcReq> {


        return saleSkcList.map { skc ->
            //获取SKC轮播图
            val imageAniVos = skcImageMap[skc.skc]

            val temuSaleSkus = saleSkuListMap[skc.saleSkcId]


            val associateBy = productSpecPropertyReqs.associateBy { it.propValue }
            //先用平台颜色去映射
            val valueItem = associateBy[skc.color]

            //货品sku规格列表 服饰类目只有颜色 和尺码
            val skuSpecReq = ProductSkuSpecReq(
                specId = valueItem!!.specId,
                parentSpecName = colorPropertyItem.name!!,
                parentSpecId = colorPropertyItem.parentSpecId ?: 0,
                specName = valueItem.specName!!
            )


            //站点和仓库的关系
            val warehouseMap = saleGoods.countryShippingWarehouse
                ?.takeIf { it.isNotBlank() }
                ?.let { jsonStr ->
                    jsonStr.parseJsonList(CountryShippingWarehouse::class.java)
                        .filterNot {
                            it.countryCode.isNullOrBlank() || it.shippingWarehouses.isNullOrEmpty()
                        }
                        .associate { it.countryCode!! to it.shippingWarehouses!! }
                } ?: emptyMap()


            val groupedSkus: Map<String, List<TemuSaleSku>>? = temuSaleSkus?.groupBy {
                it.sizeName!!
            }


            //sku数据构建
            val productSkuReqs = groupedSkus!!.map { sku ->
                //半托站点价格
                var siteSupplierPrices: List<SiteSupplierPrice>? = null

                val temuSaleSku = sku.value.first()


                //半托处理站点价格
                if (saleGoods.businessType == 2) {
                    siteSupplierPrices = sku.value.map { it ->
                        SiteSupplierPrice(
                            it.country!!.toInt(),
                            it.declaredPrice
                                ?.multiply(BigDecimal(100))
                                ?.toInt() ?: 0
                        )
                    }
                }

                //先获取尺码和平台的尺码关系再获取平台的尺码属性
                val platformSize = temuSaleSku.sizeName
                    ?.let { temuProperties.sizeMapping[it] }
                    ?.let { platformSizeItemMap[it] }
                    ?: throw IllegalArgumentException("无效尺码: ${temuSaleSku.sizeName}")

                val skuSpecSizeReq = ProductSkuSpecReq(
                    specId = platformSize.specId ?: 0,
                    parentSpecName = sizePropertyItem.name!!,
                    parentSpecId = sizePropertyItem.parentSpecId ?: 0,
                    specName = platformSize.value
                )


                /**
                 * 货品多包规请求
                 */
                /**
                 * 构建可空的多包规请求对象
                 */
                val classifiedAttr = temuSaleSku.classifiedAttrs?.parseJson<ClassifiedAttr>()

                val productSkuMultiPackReq = classifiedAttr?.let { attr ->
                    ProductSkuMultiPackReq(
                        numberOfPieces = attr.quantity!!,          // 允许为null
                        skuClassification = attr.typeCode!!,       // 允许为null
                        pieceUnitCode = attr.unitCode!!,           // 允许为null
                        individuallyPacked = if (attr.typeCode == 1) null else attr.isPacking       // 注意typeCode复用逻辑
                    )
                }

                //货品sku扩展属性
                val productSkuWhExtAttrReq = ProductSkuWhExtAttrReq(
                    productSkuWeightReq = ProductSkuWeightReq(temuSaleSku.weight?.multiply(BigDecimal("1000"))?.toInt() ?: 0),
                    productSkuSameReferPriceReq = ProductSkuSameReferPriceReq(temuSaleSku.referenceUrl),
                    productSkuVolumeReq = ProductSkuVolumeReq(
                        temuSaleSku.longestEdge?.multiply(BigDecimal("10"))?.toInt() ?: 0,
                        temuSaleSku.secondEdge?.multiply(BigDecimal("10"))?.toInt() ?: 0,
                        temuSaleSku.shortestEdge?.multiply(BigDecimal("10"))?.toInt() ?: 0,
                    ),

                    productSkuSensitiveAttrReq = ProductSkuSensitiveAttrReq(
                        0,
                        emptyList()
                    )
                )


                //半托的仓库
                var productSkuStockQuantityReq: ProductSkuStockQuantityReq? = null
                if (saleGoods.businessType == 2) {
                    val countryShippingWarehouses =
                        temuSaleSku.skuWarehouseStockQuantity?.parseJsonList(WarehouseStockQuantity::class.java)

                    val warehouseStockQuantityReqs = countryShippingWarehouses?.map { warehouse ->
                        WarehouseStockQuantityReq(
                            targetStockAvailable = warehouse.stockQuantity?.toInt() ?: 0,
                            warehouseId = warehouse.shippingWarehouseCode!!
                        )
                    } ?: emptyList()
                    productSkuStockQuantityReq = ProductSkuStockQuantityReq(warehouseStockQuantityReqs)
                }


                ProductSkuReq(
                    thumbUrl = imageAniVos?.getOrNull(0) ?: "",
                    currencyType = saleGoods.currencyType!!,
                    productSkuSpecReqs = listOf(skuSpecReq, skuSpecSizeReq),
                    supplierPrice = if (saleGoods.businessType == ProductShopBusinessType.FULLY_MANAGED.value) {
                        temuSaleSku.declaredPrice?.multiply(BigDecimal(100))?.toInt()
                    } else null,
                    siteSupplierPrices = siteSupplierPrices,
                    productSkuStockQuantityReq = productSkuStockQuantityReq,
                    productSkuMultiPackReq = productSkuMultiPackReq,
                    productSkuWhExtAttrReq = productSkuWhExtAttrReq,
                    extCode = temuSaleSku.sellerSku ?: "",
                    productSkuSuggestedPriceReq = ProductSkuSuggestedPriceReq(specialSuggestedPrice = "NA")
                )
            }

            //主销售规格列表   就是颜色
            val mainProductSkuSpecReq = MainProductSkuSpecReq(
                specId = skuSpecReq.specId,
                parentSpecName = skuSpecReq.parentSpecName,
                parentSpecId = skuSpecReq.parentSpecId,
                specName = skuSpecReq.specName
            )
            ProductSkcReq(
                previewImgUrls = imageAniVos!!,
                mainProductSkuSpecReqs = listOf(mainProductSkuSpecReq),
                productSkuReqs = productSkuReqs,
                productSkcCarouselImageI18nReqs = null,
                colorImageUrl = null,
                isBasePlate = null,
                extCode = skc.skc ?: ""
            )
        }
    }


    /**
     * 货品外包装信息
     */
    fun buildProductOuterPackageReq(temuProductOuterPackage: TemuProductOuterPackage): ProductOuterPackageReq {
        return ProductOuterPackageReq(
            packageShape = temuProductOuterPackage.packageShapeId!!,
            packageType = temuProductOuterPackage.packageTypeId!!
        )
    }


    /**
     * 货品图片信息
     */
    fun buildProductOuterPackageImageReq(
        temuProductOuterPackage: TemuProductOuterPackage,
        shop: Shop,
    ): List<ProductOuterPackageImageReq>? {
        //随机取个图片名字
        val imageUrls = temuProductOuterPackage.imageUrls?.map { url ->
            ImageAniVo(
                "img_${System.currentTimeMillis()}_${(1000..9999).random()}",
                url
            )
        }

        // 空列表检查
        if (imageUrls.isNullOrEmpty()) return emptyList()

        val uploadImages = uploadImages(imageUrls, shop, 1, 1, 1)

        if (uploadImages.isEmpty()) return emptyList()

        return uploadImages.map { ProductOuterPackageImageReq(it) }

    }


//    /**
//     * 上传图片到Temu平台
//     * @param imageList 图片URL字符串列表
//     * @param shop 店铺
//     * @param imageBizType 图片业务类型（默认0） 外包装图片用1
//     * @param sizeMode 尺寸模式 返回尺寸大小，0-原图大小，1-800800（1:1），2-13501800（3:4）
//     * @return 上传成功的图片URL列表
//     */
//    fun uploadImages(
//        imageList: List<String>?,
//        shop: Shop,
//        imageBizType: Int = 0,
//        sizeMode: Int,
//    ): List<String> {
//        if (imageList.isNullOrEmpty()) return emptyList()
//
//        return imageList.mapNotNull { imageUrl ->
//            runCatching {
//                ImageUtils.urlToBase64(imageUrl, 2048, 5000)?.let { base64Str ->
//                    TemuGoodsImageUploadReq("data:image/png;base64,$base64Str").apply {
//                        this.shortCode = shop.shortCode
//                        this.imageBizType = imageBizType
//                        this.region = shop.country
//                        this.options = TemuGoodsImageUploadReq.Options(
//                            true,
//                            1,
//                            false,
//                            sizeMode
//                        )
//                    }.let { uploadReq ->
//                        eisCenterClientExternal.goodsImageUpload(uploadReq)?.url
//                    }
//                }
//            }.getOrNull()
//        }
//    }


    /**
     * 半托管货品配送信息请求
     */
    fun buildProductShipmentReq(saleGoods: TemuSaleGoods): ProductShipmentReq? {
        if (saleGoods.businessType == 2) {
            val freightTemplate = saleGoods.freightTemplate?.parseJson(FreightTemplate::class.java)
            val shipmentLimitValidity = saleGoods.deliveryPromise?.parseJson(ShipmentLimitValidity::class.java)
            return ProductShipmentReq(
                freightTemplateId = freightTemplate?.freightTemplateId!!,
                shipmentLimitSecond = shipmentLimitValidity!!.shipmentLimitSecond!!
            )
        }
        return null
    }


    /**
     * 货品仓配供应链侧扩展属性请求
     */
    fun buildProductWhExtAttrReq(saleGoods: TemuSaleGoods): ProductWhExtAttrReq {
        val countryOriginPlaces =
            saleGoods.countryOriginPlace?.parseJson(ProductTemuCountryOriginPlaceReq::class.java)

        return ProductWhExtAttrReq(
            productOrigin = ProductOrigin(
                countryOriginPlaces?.originCode ?: throw IllegalArgumentException("主产地编码不能为空"),
                countryOriginPlaces.secondaryOriginCode?.takeUnless { it.isEmpty() }?.toLongOrNull()
            ),
            outerGoodsUrl = ""
        )
    }


    /**
     * 商品详情图
     */
    fun buildGoodsLayerDecorationReq(
        saleGoods: TemuSaleGoods,
        shop: Shop,
        imageCollectionDTO: ImageCollectionDTO,
        skcImageMap: Map<String, List<String>>,
        detailedImageSetting: TemuSkcDetailedImage?,
        skcList: List<String>?,
    ): List<GoodsLayerDecorationReq>? {
        val sellingPointImages = imageCollectionDTO.sellingPointImages

        val carouselImages = uploadImages(sellingPointImages, shop, 0, 2, 2)

        //设为详情图的轮播图
        val carouselImageUrls = skcImageMap.getOrDefault(
            detailedImageSetting?.skc ?: skcList!!.firstOrNull(),
            emptyList()
        )

        val allGoodsLayerDecoration = carouselImageUrls + carouselImages
        if (allGoodsLayerDecoration.isEmpty()) {
            return null
        }

        val contentItems = allGoodsLayerDecoration.mapIndexed { index, img ->
            GoodsLayerDecorationReq(
                lang = "zh",
                type = "image",
                priority = index, // 改为从0开始
                key = "DecImage",
                contentList = listOf(ContentItem().apply {
                    imgUrl = img
                    height = 1800
                    width = 1350
                })
            )

        }
        return contentItems
    }


    /**
     * 商品模特列表请求
     */
    fun buildGoodsModelReqs(
        saleGoods: TemuSaleGoods,
        valueItemMap: Map<String, TemuAttrsGetResp.PropertyItem.ValueItem>,
    ): List<GoodsModelReq>? {
        val temuModel = saleGoods.modelProfile?.parseJson(TemuModel::class.java)
        if (temuModel.isNull()) {
            return emptyList()
        }
        if (temuModel?.clothesModel != null) {
            //先获取尺码和平台的尺码关系再获取平台的尺码属性
            val valueItem = temuModel.sizeSpecName
                ?.let { temuProperties.sizeMapping[it] }
                ?.let { valueItemMap[it] }
                ?: throw IllegalArgumentException("模特列表试用尺码找不到平台对应属性: ${temuModel.sizeSpecName}")

            val goodsModelReq = GoodsModelReq(
                modelProfileUrl = temuModel.headPortrait,
                sizeSpecName = valueItem.value,
                modelId = temuModel.id!!,
                sizeSpecId = valueItem.specId!!,
                modelWaist = temuModel.clothesModel.waist?.split(".")?.firstOrNull() ?: "",
                modelType = 1,
                modelName = temuModel.modelName ?: "",
                modelHeight = temuModel.clothesModel.height?.split(".")?.firstOrNull() ?: "",
                modelFeature = 1,
                modelFootWidth = "",
                modelBust = temuModel.clothesModel.bust?.split(".")?.firstOrNull() ?: "",
                modelFootLength = "",
                tryOnResult = temuModel.tryOnResult!!,
                modelHip = temuModel.clothesModel.hipline?.split(".")?.firstOrNull() ?: ""
            )
            return listOf(goodsModelReq)
        }
        return emptyList()
    }


    /**
     * 半托管相关信息
     */
    fun buildProductSemiManagedReq(saleGoods: TemuSaleGoods, productSkcReqs: ProductSkcReq): ProductSemiManagedReq {
//        val countryShippingWarehouses =
//            saleGoods.countryShippingWarehouse?.parseJsonList(CountryShippingWarehouse::class.java)
//        val bindSiteIds = countryShippingWarehouses?.map { it.countryCode!!.toInt() }
        //从sku里获取 站点供货价列表，半托必传
        val bindSiteIds = productSkcReqs
            .productSkuReqs
            .firstOrNull()
            ?.siteSupplierPrices
            ?.map { it.siteId }  // 过滤掉可能为null的siteId
            .orEmpty()  // 确保返回不可变空集合

        return ProductSemiManagedReq(
            bindSiteIds = bindSiteIds,
        )
    }


    /**
     * 货品销售类扩展属性请求
     */
    fun buildProductSaleExtAttrReq(saleGoods: TemuSaleGoods): ProductSaleExtAttrReq? {
        val regionId = saleGoods.stockingArea
            ?.takeIf { it.isNotBlank() }
            ?.parseJson(InventoryRegion::class.java)
            ?.inventoryRegionId
            ?: return null

        return ProductSaleExtAttrReq().apply {
            inventoryRegion = regionId
        }
    }


    /**
     * 上传图片
     */
    fun buildSkcCarouselImage(
        imageList: List<ImageAniVo>?,
        shortCode: String,
        sizeMode: Int,
        imageBizType: Int? = null,
    ): List<String> {
        // 空列表检查
        if (imageList.isNullOrEmpty()) return emptyList()

        // 使用mapNotNull和runCatching进行异常处理与转换
        return imageList.mapNotNull { image ->
            runCatching {
                // 安全调用ossImageUrl并转换为Base64
                image.ossImageUrl?.let { url ->
                    ImageUtils.urlToBase64(url, 2048, 5000)?.let { base64Str ->
                        // 构建并上传图片请求
                        val uploadReq = TemuGoodsImageUploadReq("data:image/png;base64,$base64Str").apply {
                            this.shortCode = shortCode
                            this.imageBizType = 0
                            this.region = "CN"
                            this.options = TemuGoodsImageUploadReq.Options(true, 1, false, sizeMode)
                            this.imageBizType = imageBizType
                        }
                        // 调用上传方法并返回图片URL
                        eisCenterClientExternal.goodsImageUpload(uploadReq)?.url
                    }
                }
            }.getOrNull() // 返回非空结果或null（在mapNotNull中过滤掉）
        }
    }


    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            val buildImageCollection = imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
            // 有视频的话返回视频
            if (imageRepository.videos != null && imageRepository.videos.isNotBlank()) {
                buildImageCollection.videos = JSONArray.parseArray(imageRepository.videos, ImageAniVo::class.java)
            }
            buildImageCollection
        } else {
            ImageCollectionDTO()
        }
    }


    /**
     * 校验SKU资料
     */
    private fun validateSaleSku(
        saleSkcList: List<TemuSaleSkc>,
        saleSkuList: MutableList<TemuSaleSku>,
        saleGoods: TemuSaleGoods,
    ) {
        // 收集所有错误信息
        val errorMessages = mutableListOf<String>()

        // 检查SKC颜色是否有重复
        saleSkcList.groupBy { it.platformColor }
            .filter { (platformColor, skcs) -> platformColor != null && skcs.size > 1 }
            .forEach { (platformColor, _) ->
                errorMessages.add("平台颜色 [$platformColor] 在多个SKC中重复使用")
            }
        // 检查SKC颜色是否为空
        saleSkcList.filter { it.platformColor.isNullOrBlank() }
            .forEach { skc ->
                errorMessages.add("SKC ID [${skc.saleSkcId}] 未设置平台颜色")
            }

        // 创建SKC映射，用于后续验证
        val saleSkcMap = saleSkcList.associateBy { it.saleSkcId }

        // 按SKC分组SKU，用于校验尺码重复
        val skusBySkc = saleSkuList.groupBy { it.saleSkcId }

        // 检查每个颜色组中尺码是否重复
        skusBySkc.forEach { (skcId, skusWithSameColor) ->
            val colorName = saleSkcMap[skcId]?.platformColor!!
            val skc = saleSkcMap[skcId]?.skc

            // 检查尺码重复
            skusWithSameColor.groupBy { it.sizeName }
                .filter { (size, skus) -> size != null && skus.groupBy { it.sizeName }.size > 1 }
                .forEach { (size, _) ->
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的尺码 [$size] 重复使用")
                }

            // 检查每个SKU
            skusWithSameColor.forEach { sku ->
                val sizeName = sku.sizeName

                // 检查尺码是否为空
                if (sizeName.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的SKU缺少尺码名称")
                }
                if (sku.declaredPrice == null) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 未设置申报价格")
                } else if (sku.declaredPrice!! <= BigDecimal.ZERO) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 申报价格必须大于 0")
                }
                if (saleGoods.businessType == 2 && sku.stockQuantity?.let { sq -> sq <= 0 } == true) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置库存数量")
                }
                if (sku.sellerSku.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置sellerSku")
                }
            }

        }

        // 如果有错误，抛出异常
        if (errorMessages.isNotEmpty()) {
            throw BusinessException(errorMessages.joinToString("\n"))
        }
    }

    /**
     * 添加错误同步日志
     */
    private fun addErrorSyncLog(
        platformName: String,
        shop: String?,
        saleGoods: TemuSaleGoods,
        error: String?,
        platformRequestParams: String?,
        resp: String?,
        opType: Int?,
        temuSpuId: Long?,
    ) {
        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.templateSpuId = temuSpuId
            this.opType = opType
            this.errorMsg = error
            this.platformName = platformName
            this.shopName = shop
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
        }
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 推送到AIDC
     */
    private fun pushToAIDC(product: Product, saleGoods: TemuSaleGoods) {

        if (product.inspiraSourceId != null) {
            val onlineNoticeReq = ProductOnlineNoticeReq().apply {
                this.inspireSourceId = product.inspiraSourceId!!
                this.onlineSaleItemId = saleGoods.platformProductId!!
            }
            inspirationClientExternal.productOnlineNotice(onlineNoticeReq)
        }
    }

    /**
     * 更新商品状态
     */
    private fun updateProductState(
        product: Product,
        saleGoods: TemuSaleGoods,
        imageCollection: ImageCollectionDTO,
        logPrefix: String,
    ) {

        Product().apply {
            productId = product.productId

            if (product.getIsSyncPlatform().isNull() || product.getIsSyncPlatform() == Bool.NO.code) {
                platformSyncState = Bool.YES.code
            }

            // 有上架成功的商品则设置产品为上架状态
            if (saleGoods.publishState == ProductPublishStateEnum.ACTIVE.code) {
                publishState = ProductPublishStateEnum.ACTIVE.code

                if (product.getIsSyncPlatform().isNull() || product.getIsSyncPlatform() == Bool.NO.code) {
                    setIsSyncPlatform(Bool.YES.code)
                }
            }

            product.publishTime ?: run {
                val currentUser = CurrentUserHolder.get()
                publishUserId = currentUser.id
                publishUserName = currentUser.name
                publishTime = LocalDateTime.now()
            }

            platformSyncState = PlatformSyncStateEnum.SUCCESS.code

            // 直接在 apply 块结束后执行更新
            product.mainImgUrl = imageCollection.limitedAeMainImages(1).firstOrNull()?.ossImageUrl
            this.mainImgUrl = product.mainImgUrl
            productRepository.updateById(this)
            log.info { "${logPrefix}更新商品状态，updateProduct: ${product.toJson()}" }
        }
    }

    /**
     * 处理成功的同步日志
     *
     * @param saleGoods TemuSaleGoods
     * @param requestParams 请求参数
     * @param respParams 响应参数
     * @param opType 操作类型
     */
    private fun handleSuccessSyncLog(
        saleGoods: TemuSaleGoods,
        requestParams: String?,
        respParams: String?,
        opType: Int?,
    ) {
        //记录操作日志
        saveSyncLog(PlatformEnum.TEMU.platformName, saleGoods.shopName, saleGoods, requestParams, respParams, opType)

        // 删除错误日志
        val errorLogList = KtQueryWrapper(ProductSyncLog::class.java)
            .eq(ProductSyncLog::saleGoodId, saleGoods.saleGoodsId)
            .eq(ProductSyncLog::logType, Bool.NO.code)
            .`in`(
                ProductSyncLog::opType,
                listOf(PlatformOperatorTypeEnum.ACTIVE.code, PlatformOperatorTypeEnum.UPDATE_PRODUCT.code)
            )

        productSyncLogRepository.remove(errorLogList)
    }

    /**
     * 添加成功同步日志
     */
    private fun saveSyncLog(
        platformName: String,
        shop: String?,
        saleGoods: TemuSaleGoods,
        platformRequestParams: String?,
        resp: String?,
        opType: Int?,
    ) {
        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.opType = opType
            this.logType = Bool.YES.code
            this.platformName = platformName
            this.shopName = shop
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
        }
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 推送图片到款式平台
     *
     * @param saleSkcList SKC列表
     */
    private fun pushPublishImage(product: Product, imageRepository: ImageRepository, saleSkcList: List<TemuSaleSkc>) {
        // 不处理数码印花类型的商品
        if (!ProductPublishHelper.isLoginNum(product)) {
            val imageCollection = imageCollectionHelper.buildImageCollection(product.spuCode!!, imageRepository)

            //商品详情
            val dataList = mutableListOf<ProductImageChangeStateDto.Data>()
            val pushSkcList = mutableListOf<ProductImageChangeStateDto.Skc>()
            for (saleSkc in saleSkcList) {
                val skcImages =
                    saleSkc.pictures?.split(Url.DELIM)?.map { it.trim() }?.filter { it.isNotBlank() }?.toList()
                val skc = ProductImageChangeStateDto.Skc().apply {
                    this.skcImageList = skcImages
                    this.skc = saleSkc.skc
                }
                pushSkcList.add(skc)
            }

            val data = ProductImageChangeStateDto.Data().apply {
                this.spuCode = product.spuCode
                this.productDetailsImageList = imageCollection.detailImages.map { it.ossImageUrl!! }
                this.skcList = pushSkcList
            }
            dataList.add(data)

            val productImageChangeStateDto = ProductImageChangeStateDto()
            productImageChangeStateDto.dataList = dataList

            val record = MessageRecord().apply {
                this.businessId = product.productId.toString()
                this.exchange = MqConstants.EXCHANGE_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.routingKey = MqConstants.KEY_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.content = productImageChangeStateDto.toJson()
            }
            val msgId = messageRecordService.preCommitAndGetId(record)
            messageRecordService.commit(msgId, true)
        }
    }

    /**
     * Temu创建返回信息更新到sale
     */
    private fun updateByTemuAddResp(resp: TemuGoodsAddResp?, saleGoods: TemuSaleGoods) {
        if (resp == null) {
            return
        }
        // 更新PID
        if (saleGoods.platformProductId == null) {
            val updateSaleGoods = TemuSaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.platformProductId = resp.productId
            }
            temuSaleGoodsRepository.updateById(updateSaleGoods)
        }

        // Map<SellerSku, resp>
        val sellerSkuMap = resp.productSkuList?.associateBy { it.extCode }
        if (sellerSkuMap.isNullOrEmpty()) {
            return
        }

        // 获取数据库的skc
        val dbSkcList = temuSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        val saleSkcIdMap = dbSkcList.associateBy { it.saleSkcId }
        // 获取数据库的sku
        val dbSkuList = temuSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        // 匹配出需要更新的skc
        val updateSkcList = mutableListOf<TemuSaleSkc>()
        // 匹配出需要更新的sku
        val updateSkuList = mutableListOf<TemuSaleSku>()
        dbSkuList.forEach { sdSku ->
            val sellerSku = sellerSkuMap[sdSku.sellerSku]
            if (sellerSku != null) {
                // 收集需要更新的sku
                val sku = TemuSaleSku().apply {
                    this.saleSkuId = sdSku.saleSkuId
                    this.platformSkuId = sellerSku.productSkuId!!.toString()
                    this.platformProductId = resp.productId
                }
                updateSkuList.add(sku)

                // 收集需要更新的sku
                val dbSkc = saleSkcIdMap[sdSku.saleSkcId]
                if (dbSkc != null) {
                    val skc = TemuSaleSkc().apply {
                        this.saleSkcId = sdSku.saleSkcId
                        this.platformSkcId = sellerSku.productSkcId
                    }
                    updateSkcList.add(skc)
                }
            }
        }
        if (updateSkuList.isNotEmpty()) {
            temuSaleSkuRepository.updateBatchById(updateSkuList)
        }
        if (updateSkcList.isNotEmpty()) {
            temuSaleSkcRepository.updateBatchById(updateSkcList)
        }
    }


    fun updateProductImage(shopId: Long, saleGoods: TemuSaleGoods) {
        val logPrefix =
            "[TemuProductImageSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        log.info { "${logPrefix}开始更新图片" }

        val productId = saleGoods.productId!!
        val product = productRepository.getById(productId) ?: throw BusinessException("未设置商品Product!")
        val spuCode = saleGoods.spuCode!!
        val saleGoodsId = saleGoods.saleGoodsId!!

        val shop = shopRepository.getById(shopId) ?: throw BusinessException("店铺不存在 shopId=$shopId")
        val saleSkcList = temuSaleSkcRepository.findBySaleGoodsId(saleGoodsId)
            .takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKC!")


        val saleSkuList = temuSaleSkuRepository.ktQuery()
            .`in`(TemuSaleSku::saleSkcId, saleSkcList.map { it.saleSkcId }.toSet())
            .eq(TemuSaleSku::enableState, Bool.YES.code)
            .list().takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKU!")


        try {
            val saleSkuListMap: Map<Long?, List<TemuSaleSku>> = saleSkuList.groupBy { it.saleSkcId }

            //校验图库资料是否存在
            val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
                ?: throw PublishAscBizException("图库不存在，请先上传图片")


            val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)

            //获取图包
            val imageCollectionDTO = getImages(product.spuCode!!)

            val productDetailImages = imageCollectionDTO.productDetailImages

            val skcList = saleSkcList.map { it.skc!! }
            val skcImages = imageCollectionHelper.fetchSkcImages(
                productDetailImages,
                skcList,
                ImageTypeEnum.PRODUCT_DETAIL.code
            )

            //上传TEMU返回的图片 轮播图格式
            val skcImageMap: Map<String, List<String>> = skcList.associate { skc ->
                val imageAniVos = skcImages[skc] ?: emptyList()
                val carouselImages = uploadImages(imageAniVos, shop, 0, 2, 1)
                skc to carouselImages
            } ?: emptyMap()


            //素材图
            val mainSkc = detailedImageSetting?.skc ?: skcList.firstOrNull()
            ?: throw PublishAscBizException("无效SKC详情配置")

            // 2. 获取素材图（空安全处理）
            val mainImage = listOfNotNull(
                skcImages[mainSkc]?.firstOrNull()
            ) // 自动过滤null并包装为List<String>

            // 3. 构建素材图（命名参数）
            val carouselUrls = uploadImages(mainImage, shop, 0, 1, 1)


            val skcReqList = saleSkcList.mapNotNull { skc ->
                skc.platformSkcId?.let { skcId ->
                    TemuGoodsEditPicturesSubmitReq.SkcReq(
                        skcId = skcId,
                        previewImgUrls = skcImageMap[skc.skc].orEmpty(),
                        skuCommonReqList = saleSkuListMap[skc.saleSkcId]
                            ?.mapNotNull { sku ->
                                sku.platformSkuId?.toLongOrNull()?.let { skuId ->
                                    TemuGoodsEditPicturesSubmitReq.SkuReq(
                                        skuId = skuId,
                                        thumbUrl = skcImageMap[skc.skc]?.firstOrNull() ?: ""
                                    )
                                }
                            } ?: emptyList()
                    )
                }
            }

            //skc信息
            val temuGoodsEditPicturesSubmitReq = TemuGoodsEditPicturesSubmitReq(
                productId = saleGoods.platformProductId!!,
                materialImgUrl = carouselUrls.firstOrNull() ?: "",
                skcList = skcReqList,
            ).apply {
                shortCode = shop.shortCode
                region = shop.country
            }

            eisCenterClientExternal.goodsEditPicturesSubmit(temuGoodsEditPicturesSubmitReq)

            saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            temuSaleGoodsRepository.updateById(saleGoods)

            //发布后记录发布时使用的图包版本
            updateImageVersionNumAfterPublish(saleGoods.productId!!)
        } catch (e: Exception) {
            saleGoods.platformSyncState = PlatformSyncStateEnum.FAILURE.code
            temuSaleGoodsRepository.updateById(saleGoods)
            throw PublishGlobalBizException("更新图片失败: ${e.message}")
        }
    }


    /**
     * 创建/更新商品
     */
    fun updateProductPicture(shopId: Long, saleGoods: TemuSaleGoods) {
        val logPrefix =
            "[TEMUProductSyncImage] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId}"

        log.info { "$logPrefix - 开始更新图片" }

        // 构造唯一锁 key
        val lockKey = RedisConstants.TEMU_UPDATE_IMAGE_LOCK + saleGoods.spuCode + saleGoods.shopId

        lockComponent.doInLock(
            key = lockKey,
            inLockAction = {
                log.info { "$logPrefix - 获取锁成功" }
                updateProductImage(shopId, saleGoods)
            },
            unLockAction = {
                log.warn { "$logPrefix - 获取锁失败，可能存在并发操作" }
                // 你可以抛异常，也可以直接 return（建议业务语义明确，抛出更合理）
                throw BusinessException("并发操作冲突，请勿重复提交。$logPrefix")
            }
        )
    }


    /**
     * 上传图片到Temu平台
     * @param imageList 图片URL对象
     * @param shop 店铺
     * @param imageBizType 图片业务类型（默认0） 外包装图片用1
     * @param sizeMode 尺寸模式 返回尺寸大小，0-原图大小，1-800800（1:1），2-13501800（3:4）
     * @param maxSize 图片大小 M 兆
     * @return 上传成功的图片URL列表
     */
    fun uploadImages(
        imageList: List<ImageAniVo>?,
        shop: Shop,
        imageBizType: Int = 0,
        sizeMode: Int,
        maxSize: Int,
    ): List<String> {
        if (imageList.isNullOrEmpty()) return emptyList()

        return imageList.mapNotNull { imageUrl ->
            runCatching {
                productPublishTemuHelper.downloadAndCompressToBase64(imageUrl, maxSize)?.let { base64Str ->
                    TemuGoodsImageUploadReq(base64Str).apply {
                        this.shortCode = shop.shortCode
                        this.imageBizType = imageBizType
                        this.region = shop.country
                        this.options = TemuGoodsImageUploadReq.Options(
                            true, 1, false, sizeMode
                        )
                    }.let { uploadReq ->
                        eisCenterClientExternal.goodsImageUpload(uploadReq)?.url
                    }
                }
            }.getOrNull()
        }
    }

    private fun handleVideoAsync(videoUrl: String, shop: Shop, spuCode: String, resultList: MutableList<TemuVideoUploadResultGetResp>) {
        runAsync(asyncExecutor) {
            val logPrefix = "[TemuVideoUploadAsync] spuCode=$spuCode"
            try {
                log.info { "$logPrefix 开始异步上传视频: $videoUrl" }

                // 上传视频
                val videoId = eisCenterClientExternal.videoUpload(videoUrl, shop.shortCode!!, shop.country!!)
                    ?: throw PublishAscBizException("视频上传失败，未返回videoId")

                log.info { "$logPrefix 视频上传成功，videoId: $videoId" }

                // 轮询获取结果，最多重试5次
                var retryCount = 0
                val maxRetries = 5
                var waitTime = 1000L // 初始等待时间1秒

                while (retryCount < maxRetries) {
                    try {
                        Thread.sleep(waitTime)

                        val req = TemuGoodsVideoUploadResultGetReq(videoId).apply {
                            this.shortCode = shop.shortCode
                            this.region = shop.country
                        }

                        val result = eisCenterClientExternal.getVideoUploadResult(req)

                        if (!result.successful && result.message.contains("120000012")) {
                            log.warn { "$logPrefix 获取视频上传结果失败，视频未完成上传，等待中..." }
                        } else {
                            // 失败了 直接退出
                            log.error { "获取视频上传结果失败，messae:${result.message} " }
                            return@runAsync
                        }

                        if (result.successful && result.data != null) {
                            log.info { "$logPrefix 成功获取视频上传结果: ${result.data!!.toJson()}" }
                            resultList.add(result.data!!)
                            return@runAsync
                        }
                        log.warn { "$logPrefix 第${retryCount + 1}次获取视频结果为空" }
                    } catch (e: Exception) {
                        log.error(e) { "$logPrefix 第${retryCount + 1}次获取视频结果失败: ${e.message}" }
                    }

                    retryCount++
                    // 等待时间递增：1s, 3s, 5s, 7s, 9s
                    waitTime = 1000L + (retryCount * 2000L)

                }
                log.error { "$logPrefix 获取视频上传结果失败，已重试$maxRetries 次" }

            } catch (e: Exception) {
                log.error(e) { "$logPrefix 视频上传处理失败: ${e.message}" }
            }
        }
    }

}