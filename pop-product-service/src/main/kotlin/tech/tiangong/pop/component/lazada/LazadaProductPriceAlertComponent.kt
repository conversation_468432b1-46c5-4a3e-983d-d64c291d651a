package tech.tiangong.pop.component.lazada

import org.apache.commons.lang3.BooleanUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.bo.LazadaProductPriceAlertSkcAdapter
import tech.tiangong.pop.bo.LazadaProductPriceAlertSkuAdapter
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.component.price.PriceAlertValidationParam
import tech.tiangong.pop.component.price.PriceAlertValidationResult
import tech.tiangong.pop.component.price.ProductPriceAlertBaseComponent
import tech.tiangong.pop.config.PigeonMessageProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.enums.ProductTagTargetTypeEnum
import tech.tiangong.pop.external.PigeonMessageClientExternal
import tech.tiangong.pop.resp.product.ProductPriceAlertCheckResp
import tech.tiangong.pop.service.settings.PriceAlertConfigurationService
import tech.tiangong.pop.utils.TransactionUtils

/**
 * Lazada价格兜底组件
 */
@Slf4j
@Component
class LazadaProductPriceAlertComponent(
    priceAlertConfigurationService: PriceAlertConfigurationService,
    pigeonMessageClientExternal: PigeonMessageClientExternal,
    productTagRepository: ProductTagRepository,
    pigeonMessageProperties: PigeonMessageProperties,
    imageRepositoryRepository: ImageRepositoryRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    ) : ProductPriceAlertBaseComponent(
    priceAlertConfigurationService,
    pigeonMessageClientExternal,
    productTagRepository,
    pigeonMessageProperties,
    imageRepositoryRepository,
) {
    /**
     * 上架前校验方法
     */
    fun validateBeforePublish(
        product: Product,
        saleGoods: SaleGoods,
        saleSkcList: List<SaleSkc>?,
        saleSkuList: List<SaleSku>?,
        skipValidation: Int = Bool.NO.code,
    ): PriceAlertValidationResult {
        // 检查基本条件
        val spuCode = product.spuCode.takeIf { it.isNotBlank() } ?: throw BusinessException("productId: ${product.productId}-商品SPU编码不能为空")
        val countryCode = saleGoods.country ?: throw BusinessException("saleGoodsId: ${saleGoods.saleGoodsId}-上架国家代码不能为空")
        val categoryId = product.categoryId ?: throw BusinessException("productId: ${product.productId}-商品分类ID不能为空")
        val platformId = PlatformEnum.LAZADA.platformId

        val lazadaSpu = productTemplateLazadaSpuRepository.ktQuery()
            .select(ProductTemplateLazadaSpu::lazadaSpuId, ProductTemplateLazadaSpu::spuCode)
            .eq(ProductTemplateLazadaSpu::spuCode, spuCode)
            .orderByDesc(ProductTemplateLazadaSpu::createdTime)
            .last("Limit 1").one()

        TransactionUtils.runWithoutTransaction { removeLazadaSpuTags(lazadaSpu) }

        val effectiveSaleSkcList = getOrFetchSaleSkcList(saleSkcList, saleGoods.saleGoodsId!!)

        // 过滤非组合SKC并构建数据模型
        val skcDataList = effectiveSaleSkcList
            .filter { it.combo != Bool.YES.code }
            .mapNotNull { saleSkc ->
                if (saleSkc.skc == null) return@mapNotNull null
                LazadaProductPriceAlertSkcAdapter(saleSkc, product.productId!!)
            }

        if (skcDataList.isEmpty()) {
            return PriceAlertValidationResult(valid = true, message = "没有有效的非组合SKC")
        }

        val effectiveSaleSkuList = getOrFetchSaleSkuList(saleSkuList, effectiveSaleSkcList)
        // 构建SKU数据映射
        val skuDataMap = skcDataList.associate { skcData ->
            val skuList = effectiveSaleSkuList
                .filter { it.saleSkcId == skcData.skcId }
                .map { LazadaProductPriceAlertSkuAdapter(it) }

            skcData.skcId to skuList
        }

        // 执行价格兜底
        val result = validateBeforePublish(
            PriceAlertValidationParam(
                product = product,
                platformId = platformId,
                categoryId = categoryId,
                countryCode = countryCode,
                skcDataList = skcDataList,
                skuDataMap = skuDataMap,
                skipValidation = skipValidation,
                sendNotification = Bool.YES.code,
            )
        )


        if (BooleanUtils.isFalse(result.valid)) {
            // 打标，独立事务，防止外部调用时事务未提交导致标签未生效
            TransactionUtils.runWithoutTransaction { result.checkResult?.let { applyLazadaSpuTags(lazadaSpu, it) }}
        }

        return result
    }

    /**
     * 上架后校验方法 - 针对已上架商品
     */
    fun validateAfterPublish(
        product: Product,
        saleGoods: SaleGoods,
        saleSkcList: List<SaleSkc>?,
        saleSkuList: List<SaleSku>?,
        skipValidation: Int = Bool.NO.code,
        sendNotification: Int = Bool.YES.code,
    ): PriceAlertValidationResult {
        // 检查基本条件
        val platformId = PlatformEnum.LAZADA.platformId
        val countryCode = saleGoods.country ?: throw BusinessException("${saleGoods.spuCode}-上架国家代码不能为空")
        val categoryId = product.categoryId ?: throw BusinessException("${product.spuCode}-商品分类ID不能为空")

        TransactionUtils.runWithoutTransaction { removeSaleGoodsTags(saleGoods) }

        val effectiveSaleSkcList = getOrFetchSaleSkcList(saleSkcList, saleGoods.saleGoodsId!!)

        // 过滤非组合SKC并构建数据模型
        val skcDataList = effectiveSaleSkcList
            .filter { it.combo != Bool.YES.code }
            .mapNotNull { saleSkc ->
                if (saleSkc.skc == null) return@mapNotNull null
                LazadaProductPriceAlertSkcAdapter(saleSkc, product.productId!!)
            }

        if (skcDataList.isEmpty()) {
            return PriceAlertValidationResult(valid = true, message = "没有有效的非组合SKC")
        }

        val effectiveSaleSkuList = getOrFetchSaleSkuList(saleSkuList, effectiveSaleSkcList)
        // 构建SKU数据映射
        val skuDataMap = skcDataList.associate { skcData ->
            val skuList = effectiveSaleSkuList
                .filter { it.saleSkcId == skcData.skcId }
                .map { LazadaProductPriceAlertSkuAdapter(it) }

            skcData.skcId to skuList
        }

        // 执行校验
        val result = super.validateAfterPublish(
            PriceAlertValidationParam(
                product = product,
                platformId = platformId,
                categoryId = categoryId,
                countryCode = countryCode,
                skcDataList = skcDataList,
                skuDataMap = skuDataMap,
                skipValidation = skipValidation,
                sendNotification = sendNotification
            )
        )

        // 如果校验不通过，应用标签到TemplateSpu
        if (BooleanUtils.isFalse(result.valid)) {
            TransactionUtils.runWithoutTransaction { result.checkResult?.let { applySaleGoodsTags(saleGoods, it) } }
        }

        return result
    }

    fun getOrFetchSaleSkcList(
        saleSkcList: List<SaleSkc>?,
        saleGoodsId: Long,
    ): List<SaleSkc> {
        return saleSkcList?.takeIf { it.isNotEmpty() }
            ?: saleSkcRepository.findBySaleGoodsId(saleGoodsId)
    }

    fun getOrFetchSaleSkuList(
        saleSkuList: List<SaleSku>?,
        skcList: List<SaleSkc>,
    ): List<SaleSku> {
        return saleSkuList?.takeIf { it.isNotEmpty() }
            ?: saleSkuRepository.ktQuery()
                .`in`(SaleSku::saleSkcId, skcList.map { it.saleSkcId }.toSet())
                .eq(SaleSku::enable, Bool.YES.code)
                .list()
    }

    /**
     * 应用商品价格异常标签
     */
    fun removeSaleGoodsTags(
        saleGoods: SaleGoods,
    ) {
        val saleGoodsId = saleGoods.saleGoodsId!!
        // 删除商品级别的标签
        productTagRepository.removeTag(saleGoodsId, ProductTagTargetTypeEnum.SALE_GOODS_ID, ProductTagEnum.TAG_POP_PRICE_ERROR.code)
        productTagRepository.removeTag(saleGoodsId, ProductTagTargetTypeEnum.SALE_GOODS_ID, ProductTagEnum.TAG_POP_PRICE_INTERCEPT.code)
        log.info { "已移除销售商品: ${saleGoodsId}, ${saleGoods.spuCode}的价格异常标签" }
    }

    /**
     * 应用商品价格异常标签
     */
    fun removeLazadaSpuTags(
        lazadaSpu: ProductTemplateLazadaSpu,
    ) {
        val lazadaSpuId = lazadaSpu.lazadaSpuId!!
        // 删除商品级别的标签
        productTagRepository.removeTag(lazadaSpuId, ProductTagTargetTypeEnum.LAZADA_SPU_ID, ProductTagEnum.TAG_POP_PRICE_ERROR.code)
        productTagRepository.removeTag(lazadaSpuId, ProductTagTargetTypeEnum.LAZADA_SPU_ID, ProductTagEnum.TAG_POP_PRICE_INTERCEPT.code)
        log.info { "已移除商品lazadaSpuId: ${lazadaSpuId}, spuCode: ${lazadaSpu.spuCode}的价格异常标签" }
    }

    /**
     * 上架前价格兜底 - 标签应用到lazadaSpuId
     */
    fun applyLazadaSpuTags(
        lazadaSpu: ProductTemplateLazadaSpu,
        checkResult: ProductPriceAlertCheckResp,
    ) {
        val allTags = collectAllTags(checkResult)
        val lazadaSpuId = lazadaSpu.lazadaSpuId!!

        // 应用标签到product_id
        log.info { "为商品lazadaSpuId[${lazadaSpuId}], spuCode: ${lazadaSpu.spuCode}应用价格兜底标签，上架前环境" }
        allTags.forEach { (tagKey, tagValues) ->
            tagValues.forEach { tagValue ->
                productTagRepository.addTagByLazadaSpuId(lazadaSpuId, tagKey, tagValue)
                log.debug { "为商品lazadaSpuId: ${lazadaSpuId}添加标签: $tagKey=$tagValue" }
            }
        }
    }

    /**
     * 标签应用到sale_goods_id
     */
    fun applySaleGoodsTags(
        saleGoods: SaleGoods,
        checkResult: ProductPriceAlertCheckResp
    ) {
        val saleGoodsId = saleGoods.saleGoodsId!!
        val allTags = collectAllTags(checkResult)

        // 应用标签到sale_goods_id
        log.info { "为销售商品saleGoodsId[${saleGoodsId}]应用价格兜底标签，上架后环境" }
        allTags.forEach { (tagKey, tagValues) ->
            tagValues.forEach { tagValue ->
                productTagRepository.addTagByLazadaSaleGoodsId(saleGoodsId, tagKey, tagValue)
                log.debug { "为销售商品saleGoodsId: ${saleGoodsId}添加标签: $tagKey=$tagValue" }
            }
        }
    }

}