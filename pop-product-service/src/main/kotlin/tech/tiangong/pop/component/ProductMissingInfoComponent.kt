package tech.tiangong.pop.component

import com.alibaba.fastjson2.parseArray
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.dto.CategoryPackageConfigVolumeDto
import tech.tiangong.pop.dao.entity.dto.ProductPackageInfoDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.AttributeGroupTypeEnum
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.utils.ImageUtils

/**
 * 商品信息缺失组件
 */
@Component
@Slf4j
class ProductMissingInfoComponent(
    private val productSkcRepository: ProductSkcRepository,
    private val productSizeDetailRepository: ProductSizeDetailRepository,
    private val productAttributesV2Repository: ProductAttributesV2Repository,
    private val publishCategoryAttrRepository: PublishCategoryAttrRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
) {

    /**
     * 检查商品信息是否完善
     * @param product
     * @return 1-完善 1-不完善
     */
    fun checkProduct(product: Product): Int {
        //检查商品规格：颜色，供货价&采购价，尺码，尺寸表任一信息缺失即不完善，反之为完善
        val skcFlag = checkSkc(product).isEmpty()
        //检查商品属性：必填项均有值则为完善，反之为不完善；必填项=各平台必填属性已映射的内部属性并集
        val requestAttributeFlag = checkRequestAttributeValue(product).isEmpty()
        //物流包装：必填项均有值则为完善，反之为不完善
        val packageInfoFlag = checkPackageInfo(product).isEmpty()
        //图片&视频：主图&详情图有值即为完善，反之为不完善
        val imageAndVideoFlag = checkImageVideo(product).isEmpty()
        //平台属性：必填项均有值则为完善，反之为不完善
        val platformAttributeRequestFlag = checkRequestPlatformAttributeValue(product).isEmpty()
        return if (skcFlag
            && requestAttributeFlag
            && packageInfoFlag
            && imageAndVideoFlag
            && platformAttributeRequestFlag
        ) {
            Bool.YES.code
        } else {
            Bool.NO.code
        }
    }

    /**
     * 检查商品信息是否完善
     * @param product
     * @return 缺失信息列表
     */
    fun getMissingInfoList(product: Product): List<String> {
        //检查商品规格：颜色，供货价&采购价，尺码，尺寸表任一信息缺失即不完善，反之为完善
        val skcFlagList = checkSkc(product)
        //检查商品属性：必填项均有值则为完善，反之为不完善；必填项=各平台必填属性已映射的内部属性并集
        val requestAttributeFlag = checkRequestAttributeValue(product)
        //物流包装：必填项均有值则为完善，反之为不完善
        val packageInfoFlagList = checkPackageInfo(product)
        //图片&视频：主图&详情图有值即为完善，反之为不完善
        val imageAndVideoFlag = checkImageVideo(product)
        //平台属性：必填项均有值则为完善，反之为不完善
        val platformAttributeRequestFlag = checkRequestPlatformAttributeValue(product)

        val missingInfoList = mutableListOf<String>()
        if (skcFlagList.isNotEmpty()) {
            missingInfoList.addAll(skcFlagList)
        }
        if (requestAttributeFlag.isNotEmpty()) {
            missingInfoList.addAll(requestAttributeFlag)
        }
        if (packageInfoFlagList.isNotEmpty()) {
            missingInfoList.addAll(packageInfoFlagList)
        }
        if (imageAndVideoFlag.isNotEmpty()) {
            missingInfoList.addAll(imageAndVideoFlag)
        }
        if (platformAttributeRequestFlag.isNotEmpty()) {
            missingInfoList.addAll(platformAttributeRequestFlag)
        }
        return missingInfoList
    }


    private fun checkImageVideo(product: Product): List<String> {
        val missing = mutableListOf<String>()
        val image = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        if (image == null) {
            log.warn { "spu${product.spuCode}商品信息不完善：没有图片信息" }
            missing.add("没有图片信息")
        } else {
            //主图
            if (image.mainUrl.isNullOrBlank()) {
                log.warn { "spu${product.spuCode}商品信息不完善：没有主图" }
                missing.add("没有主图")
            }
            //详情图
            if (image.imageUrls.isNullOrBlank()) {
                log.warn { "spu${product.spuCode}商品信息不完善：没有详情图" }
                missing.add("没有详情图")
            } else {
                val imageList = image.imageUrls.parseArray<ImageAniVo>()
                val detailImageList = ImageUtils.filterImagesByTypeCode(ImageTypeEnum.PRODUCT_DETAIL.code, imageList)
                if (detailImageList.isEmpty()) {
                    log.warn { "spu${product.spuCode}商品信息不完善：没有详情图" }
                    missing.add("没有详情图")
                }
            }
        }
        return missing
    }

    private fun checkSkc(product: Product): List<String> {
        val missing = mutableListOf<String>()
        //有skc就一定有颜色
        val skcList = productSkcRepository.listActiveByProductId(product.productId!!)
        if (skcList.isEmpty()) {
            log.warn { "spu${product.spuCode}商品信息不完善：没有SKC" }
            missing.add("没有SKC")
        }
        skcList.forEach { skc ->
            //供货价&采购价
            if (skc.localPrice == null || skc.purchasePrice == null) {
                log.warn { "spu${product.spuCode}商品信息不完善：没有供货价或者采购价" }
                missing.add("没有供货价或者采购价")
            }
            val sizeNameList = skc.sizeNames.parseArray<String>()
            //尺码
            if (sizeNameList.isNullOrEmpty()) {
                log.warn { "spu${product.spuCode}商品信息不完善：没有尺码范围" }
                missing.add("没有尺码范围")
            }
        }
        //尺寸表
        val sizeDetailList = productSizeDetailRepository.listByProductIds(listOf(product.productId!!))
        if (sizeDetailList.isEmpty()) {
            log.warn { "spu${product.spuCode}商品信息不完善：没有尺寸表" }
            missing.add("没有尺寸表")
        }
        return missing
    }

    private fun checkPackageInfo(product: Product): List<String> {
        val missing = mutableListOf<String>()

        val packageInfo = product.packageInfo?.parseJson<ProductPackageInfoDto>()
        if (packageInfo == null) {
            log.warn { "spu${product.spuCode}商品信息不完善：没有物流包装信息" }
            missing.add("没有物流包装信息")
        } else {
            //包装体积
            val volume = packageInfo.packingVolume
            if (volume == null || volume.maxSideLength == null || volume.minSideLength == null || volume.secondSideLength == null) {
                log.warn { "spu${product.spuCode}商品信息不完善：物流包装信息-包装体积不完善" }
                missing.add("物流包装信息-包装体积不完善")
            }
            //商品重量
            if (packageInfo.weight == null) {
                log.warn { "spu${product.spuCode}商品信息不完善：物流包装信息-商品重量不完善" }
                missing.add("物流包装信息-商品重量不完善")
            }
            //外包装类型
            if (packageInfo.outerPackingType == null) {
                log.warn { "spu${product.spuCode}商品信息不完善：物流包装信息-外包装类型不完善" }
                missing.add("物流包装信息-外包装类型不完善")
            }
            //外包装形状
            if (packageInfo.outerPackingShape == null) {
                log.warn { "spu${product.spuCode}商品信息不完善：物流包装信息-外包装形状不完善" }
                missing.add("物流包装信息-外包装形状不完善")
            }
            //外包装图片
            if (packageInfo.outerPackingImage.isNullOrEmpty()) {
                log.warn { "spu${product.spuCode}商品信息不完善：物流包装信息-外包装图片不完善" }
                missing.add("物流包装信息-外包装图片不完善")
            }
        }
        return missing
    }

    private fun checkRequestAttributeValue(product: Product): List<String> {
        val missing = mutableListOf<String>()
        //商品属性
        val productAttributes = productAttributesV2Repository.listByProductId(product.productId!!)
        //商品对应品类的属性及属性值
        val categoryAttributeList = publishCategoryAttrRepository.listAttributesByCategoryId(product.categoryId!!, Bool.YES.code)
        //必填属性ID
        val requestAttributeList = categoryAttributeList.filter { it.requestFlag == Bool.YES.code }
        //没关联任何属性（包括平台属性）不通过
        if (requestAttributeList.isEmpty()) {
            log.warn { "spu${product.spuCode}商品信息不完善：品类没关联任何属性" }
            missing.add("品类没有关联任何属性")
        } else {
            if (productAttributes.isNotNull() && productAttributes.isNotEmpty()) {
                val attributeIdToProductAttributeMap = productAttributes!!.associateBy { it.attributeId }
                requestAttributeList.forEach { requestAttribute ->
                    val pa = attributeIdToProductAttributeMap[requestAttribute.attributeId]
                    if (pa == null || pa.attributeValue.isNullOrBlank()) {
                        log.warn { "spu${product.spuCode}商品信息不完善：必填商品属性不完善" }
                        missing.add("必填属性:${requestAttribute.attributeName} 缺失")
                    }
                }
            } else {
                log.warn { "spu${product.spuCode}商品信息不完善：未填写商品属性" }
                missing.add("未填写商品属性")
            }
        }
        return missing
    }

    //平台属性必填
    private fun checkRequestPlatformAttributeValue(product: Product): List<String> {
        val missing = mutableListOf<String>()
        val productAttributes = productAttributesV2Repository.listByProductId(product.productId!!)
        //商品对应品类的属性及属性值
        val categoryAttributeList = publishCategoryAttrRepository.listAttributesByCategoryId(product.categoryId!!, Bool.YES.code)
        //必填属性ID
        val requestAttributeList = categoryAttributeList.filter { it.groupType == AttributeGroupTypeEnum.PLATFORM.code && it.requestFlag == Bool.YES.code }
        //如果品类没有关联平台必填属性，则通过
        if (requestAttributeList.isEmpty()) {
//            log.warn { "spu${product.spuCode}商品信息不完善：品类没关联任何平台属性" }
            return missing
        } else {
            if (productAttributes.isNotNull() && productAttributes.isNotEmpty()) {
                val attributeIdToProductAttributeMap = productAttributes!!.associateBy { it.attributeId }
                requestAttributeList.forEach { requestAttribute ->
                    val pa = attributeIdToProductAttributeMap[requestAttribute.attributeId]
                    if (pa == null || pa.attributeValue.isNullOrBlank()) {
                        log.warn { "spu${product.spuCode}商品信息不完善：必填平台属性不完善" }
                        missing.add("平台属性:${requestAttribute.attributeName} 缺失")
                    }
                }
            } else {
                log.warn { "spu${product.spuCode}商品信息不完善：未填写平台属性" }
                missing.add("未填写平台属性")
            }
        }
        return missing
    }
}