package tech.tiangong.pop.component.importfile.pending

import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.ProductCommonComponent
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ImportProductDTO
import tech.tiangong.pop.dto.product.ProductInitDto.ProductInitSkcDto
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.resp.product.ColorMapResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.product.price.ProductPricingServiceSelector
import kotlin.collections.get

/**
 * Lazada待上架-导入商品数据
 */
@Component
@Slf4j
class LazadaPendingProductImportComponent(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val shopRepository: ShopRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val barCodeService: BarCodeService,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val productCommonComponent: ProductCommonComponent,
    private val productPricingServiceSelector: ProductPricingServiceSelector,
) {

    private val platform = LAZADA

    /**
     * 商品导入-执行逻辑-Lazada
     * @param importDto
     * @param colorMap
     * @param imageMap
     * @param publishCategories
     */
    fun doImportLazadaProduct(
        importDto: ImportProductDTO,
        colorMap: List<ColorMapResp>,
        imageMap: Map<String, ImageRepository>,
        publishCategories: List<PublishCategory>,
    ) {

        val shop = shopRepository.ktQuery()
            .eq(Shop::shopName, importDto.listingShopName)
            .eq(Shop::platformId, platform.platformId)
            .list()
            .firstOrNull()
        if (shop == null) {
            throw BusinessException("Lazada店铺:${importDto.listingShopName}不存在")
        }
        // 查询待上架商品SPU: SPU+店铺, 且任务状态 != 已完成
        val templateSpuList = productTemplateLazadaSpuRepository.ktQuery()
            .eq(ProductTemplateLazadaSpu::shopId, shop.shopId)
            .eq(ProductTemplateLazadaSpu::spuCode, importDto.spuCode)
            .ne(ProductTemplateLazadaSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .list()
        if (templateSpuList.isEmpty()) {
            throw BusinessException("Lazada店铺:${importDto.listingShopName} SPU:${importDto.spuCode} 商品不存在或已完成")
        }

        templateSpuList.forEach { templateSpu ->

            // 更新product
            upsertProductByImportLazada(templateSpu, importDto, imageMap)

            val product = productRepository.getById(templateSpu.productId!!)

            // 设置商品属性
            productCommonComponent.setAttributesByImport(importDto, product, platform.platformId)

            // 更新模板SPU, SKC, SKU
            upsertLazadaTemplateSpuSkcSku(importDto, product, templateSpu)
        }
    }

    /**
     * 商品导入-新增更新商品-Lazada
     * @param templateSpu
     * @param importDto
     * @param imageMap
     * @return
     */
    private fun upsertProductByImportLazada(
        templateSpu: ProductTemplateLazadaSpu,
        importDto: ImportProductDTO,
        imageMap: Map<String, ImageRepository>,
    ) {
        val upsertProduct = productRepository.getById(templateSpu.productId!!)

        // 更新值
        val imageRepository = imageMap[upsertProduct.spuCode]
        if (imageRepository != null) {
            upsertProduct.mainImgUrl = imageRepository.mainUrl
        }
        val supplyModeEnum = SupplyModeEnum.getByName(importDto.supplyMode) ?: throw BaseBizException("找不到供给方式")
        upsertProduct.brandName = importDto.brandName
        upsertProduct.supplyMode = supplyModeEnum.dictCode
        upsertProduct.productTitle = importDto.productTitle
        upsertProduct.sizeGroupName = "字母码"
        upsertProduct.sizeGroupCode = ProductConstant.SOURCE_GROUP_CODE
        if (StringUtils.isNotBlank(importDto.packageWeight)) {
            upsertProduct.packageWeight = importDto.packageWeight
        }
        if (StringUtils.isNotBlank(importDto.cmpRetailPrice)) {
            upsertProduct.cmpRetailPrice = importDto.cmpRetailPrice?.toBigDecimal()
        }
        if (StringUtils.isNotBlank(importDto.cmpSalePrice)) {
            upsertProduct.cmpSalePrice = importDto.cmpSalePrice?.toBigDecimal()
        }
        //设置定价类型和现货类型
        if (StringUtils.isNotBlank(importDto.pricingType)) {
            val pricingTypeEnum = ProductPricingTypeEnum.getByDesc(importDto.pricingType?.trim()) ?: throw BaseBizException("找不到定价类型")
            upsertProduct.pricingType = pricingTypeEnum.code
        }
        if (StringUtils.isNotBlank(importDto.spotType)) {
            val spotTypeOpsEnum = SpotTypeOpsEnum.getByDesc(importDto.spotType?.trim()) ?: throw BaseBizException("找不到现货类型")
            upsertProduct.spotTypeCode = spotTypeOpsEnum.dictCode
        }

        // 处理默认值
        if (upsertProduct.packageWeight == null) {
            upsertProduct.packageWeight = lazadaDefaultProperties.packageWeight
        }
        if (upsertProduct.packageDimensionsLength == null) {
            upsertProduct.packageDimensionsLength = lazadaDefaultProperties.packageDimensionsLength.toString()
        }
        if (upsertProduct.packageDimensionsWidth == null) {
            upsertProduct.packageDimensionsWidth = lazadaDefaultProperties.packageDimensionsWidth.toString()
        }
        if (upsertProduct.packageDimensionsHeight == null) {
            upsertProduct.packageDimensionsHeight = lazadaDefaultProperties.packageDimensionsHeight.toString()
        }

        // 导入更新
        productRepository.updateById(upsertProduct)

        //写库后再同步款式类型和图包状态
        productCommonComponent.initStyleTypeAndImagePackageState(upsertProduct)
    }

    /**
     * 更新模板SPU, SKC, SKU
     * @param product
     */
    private fun upsertLazadaTemplateSpuSkcSku(importDto: ImportProductDTO, product: Product, templateSpu: ProductTemplateLazadaSpu) {

        val pair = upsertLazadaTemplateSpuSkc(product, templateSpu)
        val spuLazada = pair.first
        val lastSkcLazadaList = pair.second

        // 标题
        if (importDto.productTitle.isNotBlank()) {
            spuLazada.productTitle = importDto.productTitle
        }
        productTemplateLazadaSpuRepository.updateById(spuLazada)

        // 处理SKU
        // 取出import尺码
        val selectSizeList = ImportProductDTO.getUniqueSizes(listOf(importDto))
        if (CollectionUtils.isEmpty(selectSizeList)) {
            return
        }
        // 取出所有站点
        val countryList = LazadaCountryEnum.getCountryList()
        // 按照import的尺码先upsert sku
        lastSkcLazadaList?.forEach { skc ->
            selectSizeList.forEach { size ->

                // 判断尺码是否有条码
                val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuLazada.spuCode, skc.skc, size)
                var newBarcode: String? = null
                if (barCode == null) {
                    // 添加条码
                    val barcodeReq = BatchCreateBarCodeReq().apply {
                        this.categoryCode = product.categoryCode
                        this.categoryName = product.categoryName
                        this.skcCode = skc.skc
                        this.color = skc.color
                        this.spuCode = product.spuCode
                        this.groupName = product.sizeGroupName
                        this.sourceGroupCode = product.sizeGroupCode
                        this.sizeValues = listOf(size)
                        this.inspiraImgUrl = product.inspiraImgUrl
                        this.supplyMode = product.supplyMode
                        this.localPrice = skc.localPrice
                        this.designImgUrl = skc.pictures
                        this.mainImgUrl = skc.pictures
                    }
                    val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                    if (CollectionUtils.isNotEmpty(barcodeResp)) {
                        newBarcode = barcodeResp[0].barcode
                    }
                } else {
                    newBarcode = barCode.barcode
                }

                countryList.forEach { country ->
                    // 判断sku是否存在
                    val skuLazada = productTemplateLazadaSkuRepository.getByLazadaSkcIdAndSizeNameAndCountry(
                        skc.lazadaSkcId!!,
                        size,
                        country
                    )
                    if (skuLazada != null) {
                        // 更新
                        if (newBarcode != null) {
                            skuLazada.barcode = newBarcode
                        }
                        if (importDto.stockQuantity != null) {
                            skuLazada.stockQuantity = importDto.stockQuantity?.toLong()
                        }
                        skuLazada.enableState = Bool.YES.code
                        setPriceByCountry(importDto, country, skuLazada)
                        if (skuLazada.stockQuantity == null) {
                            skuLazada.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                        }
                        productTemplateLazadaSkuRepository.updateById(skuLazada)
                    } else {
                        // 新增
                        val skuLazada = ProductTemplateLazadaSku().apply {
                            this.lazadaSkuId = IdHelper.getId()
                            this.lazadaSkcId = skc.lazadaSkcId
                            this.lazadaSpuId = spuLazada.lazadaSpuId
                            if (newBarcode != null) {
                                this.barcode = newBarcode
                            }
                            importDto.stockQuantity?.let { this.stockQuantity = it.toLong() }
                            this.sizeName = size
                            this.country = country
                            this.enableState = Bool.YES.code
                        }
                        setPriceByCountry(importDto, country, skuLazada)
                        if (skuLazada.stockQuantity == null) {
                            skuLazada.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                        }
                        productTemplateLazadaSkuRepository.save(skuLazada)
                    }
                }
            }
        }
    }

    /**
     * 更新Lazada模板SKC和SKU
     */
    private fun upsertLazadaTemplateSpuSkc(product: Product, templateSpu: ProductTemplateLazadaSpu, skcList: List<ProductInitSkcDto>? = null): Pair<ProductTemplateLazadaSpu, List<ProductTemplateLazadaSkc>?> {
        val generatedTitleResp = productTitleGenerateService.previewTitle(ProductTitleGenerateReq().apply {
            this.productId = product.productId
            this.product = product
            this.shopId = product.shopId
        })

        // 内联 title 赋值逻辑
        fun setGeneratedTitle(spu: ProductTemplateLazadaSpu) {
            if (generatedTitleResp.productTitleRuleId.isNotNull()) {
                spu.productTitle = generatedTitleResp.title
                    .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(LAZADA) }
                    ?: spu.productTitle
                spu.generatedTitleOverLengthFlag = if (generatedTitleResp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                spu.generatedTitleMissingFieldsJson = generatedTitleResp.missingFields.toJson()
            }
        }
        // 处理SPU
        val spuLazada = templateSpu

        // 更新
        spuLazada.productTitle = product.productTitle
        spuLazada.brandName = product.brandName
        spuLazada.packageWeight = product.packageWeight
        spuLazada.packageDimensionsLength = product.packageDimensionsLength
        spuLazada.packageDimensionsHeight = product.packageDimensionsHeight
        spuLazada.packageDimensionsWidth = product.packageDimensionsWidth
        spuLazada.sizeGroupName = product.sizeGroupName
        spuLazada.sizeGroupCode = product.sizeGroupCode

        setGeneratedTitle(spuLazada)
        productTemplateLazadaSpuRepository.updateById(spuLazada)

        // 重算价格(里面使用product_skc来计算)
        val autoCulPriceList = productPricingServiceSelector
            .getSalePricingHandler(LAZADA)
            .autoCalPriceByTemplate(spuLazada.lazadaSpuId!!, LazadaConstants.LAZADA_DEFAULT_COUNTRY.map { it.code })
        val productSkcIdMap = autoCulPriceList.associateBy({ it.skc }, { it })

        // 处理SKC
        // 取出模板skc
        val skcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spuLazada.lazadaSpuId!!)
        val skcMap = skcLazadaList.associateBy { it.skc }
        // 取出商品skc
        val productSkcList = productSkcRepository.getByProductId(product.productId!!)
        // 以模板skc为基准, lazadaSpuId+color为key, 遍历productSkcList, 存在则更新, 不存在则新增
        productSkcList.forEach {
            val skc = skcMap[it.skc]
            if (skc != null) {
                // 更新
                skc.productSkcId = it.productSkcId
                skc.skc = it.skc
                skc.color = it.color
                skc.platformColor = it.platformColor
                skc.colorCode = it.colorCode
                skc.colorAbbrCode = it.colorAbbrCode
                skc.pictures = it.pictures
                skc.state = it.state
                skc.cbPrice = it.cbPrice
                skc.localPrice = it.localPrice
                skc.purchasePrice = it.purchasePrice
                skc.costPrice = it.costPrice
                productTemplateLazadaSkcRepository.updateById(skc)
            } else {
                // 新增
                val skc = ProductTemplateLazadaSkc().apply {
                    this.lazadaSkcId = IdHelper.getId()
                    this.lazadaSpuId = spuLazada.lazadaSpuId
                    this.productSkcId = it.productSkcId
                    this.skc = it.skc
                    this.color = it.color
                    this.platformColor = it.platformColor
                    this.colorCode = it.colorCode
                    this.colorAbbrCode = it.colorAbbrCode
                    this.pictures = it.pictures
                    this.state = it.state
                    this.cbPrice = it.cbPrice
                    this.localPrice = it.localPrice
                    this.purchasePrice = it.purchasePrice
                    this.costPrice = it.costPrice
                }
                productTemplateLazadaSkcRepository.save(skc)
            }
        }
        // 再获取一次SKC, 上面有新增和更新
        val lastSkcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spuLazada.lazadaSpuId!!)

        // 初始化尺码
        // 取出所有站点
        val countryList = LazadaCountryEnum.getCountryList()
        // skcList map key=skcCode
        val skcMapByCode = skcList?.groupBy { it.skcCode }
        lastSkcLazadaList.forEach { skc ->

            skcMapByCode?.get(skc.skc)?.forEach { skcDto ->
                skcDto.sizeList?.forEach { size ->
                    // 获取条码
                    val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(spuLazada.spuCode, skc.skc, size)

                    countryList.forEach { country ->

                        val price = productSkcIdMap[skc.skc]?.countryPriceList?.firstOrNull { it.country == country }

                        // 判断sku是否存在
                        val skuLazada = productTemplateLazadaSkuRepository.getByLazadaSkcIdAndSizeNameAndCountry(
                            skc.lazadaSkcId!!,
                            size,
                            country
                        )
                        if (skuLazada == null) {
                            // 新增
                            val skuLazada = ProductTemplateLazadaSku().apply {
                                this.lazadaSkuId = IdHelper.getId()
                                this.lazadaSkcId = skc.lazadaSkcId
                                this.lazadaSpuId = spuLazada.lazadaSpuId
                                //                            this.sellerSku = importDto.skuCode
                                barCode?.let { this.barcode = it.barcode }
                                this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                                this.sizeName = size
                                this.country = country
                                this.enableState = Bool.YES.code
                                if (price != null) {
                                    this.salePrice = price.salePrice
                                    this.retailPrice = price.retailPrice
                                }
                            }
                            productTemplateLazadaSkuRepository.save(skuLazada)
                        } else {
                            if (price != null) {
                                skuLazada.salePrice = price.salePrice
                                skuLazada.retailPrice = price.retailPrice
                            }
                            productTemplateLazadaSkuRepository.updateById(skuLazada)
                        }
                    }
                }
            }
        }
        return Pair(spuLazada, lastSkcLazadaList)
    }

    /**
     * 设置价格
     * @param skcImport
     * @param country
     * @param sku
     */
    private fun setPriceByCountry(skcImport: ImportProductDTO, country: String, sku: ProductTemplateLazadaSku) {
        if (country == LazadaCountryEnum.ID.code && (StringUtils.isNotEmpty(skcImport.salePriceId) && StringUtils.isNotEmpty(skcImport.retailPriceId))) {
            sku.salePrice = skcImport.salePriceId?.toBigDecimal()
            sku.retailPrice = skcImport.retailPriceId?.toBigDecimal()
        }
        if (country == LazadaCountryEnum.VN.code && (StringUtils.isNotEmpty(skcImport.salePriceVnd) && StringUtils.isNotEmpty(skcImport.retailPriceVnd))) {
            sku.salePrice = skcImport.salePriceVnd?.toBigDecimal()
            sku.retailPrice = skcImport.retailPriceVnd?.toBigDecimal()
        }
        if (country == LazadaCountryEnum.PH.code && (StringUtils.isNotEmpty(skcImport.salePricePhp) && StringUtils.isNotEmpty(skcImport.retailPricePhp))) {
            sku.salePrice = skcImport.salePricePhp?.toBigDecimal()
            sku.retailPrice = skcImport.retailPricePhp?.toBigDecimal()
        }
        if (country == LazadaCountryEnum.TH.code && (StringUtils.isNotEmpty(skcImport.salePriceThb) && StringUtils.isNotEmpty(skcImport.retailPriceThb))) {
            sku.salePrice = skcImport.salePriceThb?.toBigDecimal()
            sku.retailPrice = skcImport.retailPriceThb?.toBigDecimal()
        }
        if (country == LazadaCountryEnum.MY.code && (StringUtils.isNotEmpty(skcImport.salePriceMyr) && StringUtils.isNotEmpty(skcImport.retailPriceMyr))) {
            sku.salePrice = skcImport.salePriceMyr?.toBigDecimal()
            sku.retailPrice = skcImport.retailPriceMyr?.toBigDecimal()
        }
        if (country == LazadaCountryEnum.SG.code && (StringUtils.isNotEmpty(skcImport.salePriceSgd) && StringUtils.isNotEmpty(skcImport.retailPriceSgd))) {
            sku.salePrice = skcImport.salePriceSgd?.toBigDecimal()
            sku.retailPrice = skcImport.retailPriceSgd?.toBigDecimal()
        }
    }

}