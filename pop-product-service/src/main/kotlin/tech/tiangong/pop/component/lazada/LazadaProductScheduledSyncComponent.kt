package tech.tiangong.pop.component.lazada

import com.google.common.util.concurrent.RateLimiter
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.runAsync
import tech.tiangong.pop.common.constant.LazadaConstants.ErrorCode
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.dao.entity.SaleGoods
import tech.tiangong.pop.dao.entity.SaleSku
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dao.repository.SaleGoodsRepository
import tech.tiangong.pop.dao.repository.SaleSkuRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import team.aikero.blade.core.enums.Bool
import tech.tiangong.pop.enums.LazadaProductStateEnum
import tech.tiangong.pop.enums.LazadaSkuStateEnum
import tech.tiangong.pop.req.product.ProductPartSyncReq
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.utils.RetryTemplateUtils
import java.time.Instant
import java.time.LocalDateTime
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ExecutorService

/**
 * Lazada商品信息（部分）定时同步服务
 */
@Slf4j
@Component
class LazadaProductScheduledSyncComponent(
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val shopRepository: ShopRepository,
    private val lazadaApiService: LazadaApiService,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
) {
    companion object {
        // 每个批次处理的商品数量
        private const val PAGE_BATCH_SIZE = 100
        // 最大循环次数，防止死循环
        private const val MAX_LOOP = 100000
        // 重试查询Lazada信息的策略参数
        private val queryProductRetryParams = mapOf(
            RetryTemplateUtils.MAX_ATTEMPTS to 3.0,           // 最多重试次数
            RetryTemplateUtils.INITIAL_INTERVAL to 1000.0,     // 第一次重试等待
            RetryTemplateUtils.MULTIPLIER to 1.5,             // 间隔递增
            RetryTemplateUtils.MAX_INTERVAL to 3000.0         // 最大等待
        )
        // Lazada API请求限流控制
        // 只有 api 级别的 qps，没有针对单个 app 的 qps，具体数字没说，30在测试中没有问题
        private val apiRateLimiter = RateLimiter.create(30.0)
        // 不可重试的异常列表
        private val nonRetryableExceptions: Map<Class<out Throwable>, Boolean> = mapOf(
            ProductNotFoundException::class.java to false,
            IllegalAccessTokenException::class.java to false
        )
    }

    /**
     * 同步所有Lazada商品信息（部分）
     * 定时任务入口方法，每天凌晨执行
     */
    fun syncAllProductPart(req: ProductPartSyncReq) {
        log.info { "开始同步Lazada商品（部分）" }
        val startTime = Instant.now().toEpochMilli()
        var lastId: Long? = null
        var processedBatches = 0

        try {
            while (processedBatches < MAX_LOOP) {
                val records = saleGoodsRepository.pageAllSaleGoodsForSync(lastId, PAGE_BATCH_SIZE, req)

                if (records.isEmpty()) {
                    if (processedBatches == 0) {
                        log.warn { "首次拉取即无数据，检查平台数据源或同步条件是否异常。" }
                    }
                    break
                }

                // 仅处理未删除的商品
                val validSaleGoods = records.filter { it.deleted == Bool.NO.code }
                processBatch(validSaleGoods)
                log.info { "已处理第${processedBatches + 1}批商品，本批数量:${records.size}" }

                // 更新游标为本批最大id
                lastId = records.maxOf { it.saleGoodsId!! }
                processedBatches++

                // 若本批数量少于页大小，说明已取完
                if (records.size < PAGE_BATCH_SIZE) break
            }

            if (processedBatches >= MAX_LOOP) {
                log.error { "循环超过最大允许次数($MAX_LOOP)，可能分页死循环，当前游标：$lastId" }
            }
        } catch (e: Exception) {
            log.error(e) { "Lazada商品（部分）同步异常" }
        } finally {
            val duration = (Instant.now().toEpochMilli() - startTime)
            log.info { "同步商品信息（部分）完成，耗时：${duration}ms，共处理${processedBatches}批" }
        }
    }

    /**
     * 处理一批商品
     */
    private fun processBatch(saleGoodsList: List<SaleGoods>) {
        // 过滤掉platformProductId为空的记录
        val validSaleGoodsList = saleGoodsList.filter { it.platformProductId.isNotBlank() }

        if (validSaleGoodsList.isEmpty()) {
            return
        }

        // 创建异步任务列表
        val futures = validSaleGoodsList.map { saleGoods ->
            runAsync(asyncExecutor) {
                processOneProduct(saleGoods)
            }
        }

        // 等待所有任务完成
        CompletableFuture.allOf(*futures.toTypedArray()).join()
    }

    /**
     * 处理单个商品
     */
    private fun processOneProduct(saleGoods: SaleGoods) {
        try {
            // 检查商品ID
            val platformProductId = saleGoods.platformProductId ?: return
            val logPrefix = "商品同步信息（部分） saleGoodsId: ${saleGoods.saleGoodsId}, spuCode: ${saleGoods.spuCode}"

            // 获取店铺信息
            val shop = shopRepository.getById(saleGoods.shopId) ?: run {
                log.warn { "$logPrefix 店铺不存在，跳过同步, shopId: ${saleGoods.shopId}" }
                return
            }
            if (shop.isAuth.isNull() || shop.isAuth != YesOrNoEnum.YES.code) {
                log.warn { "$logPrefix 店铺未授权，跳过同步, shopId: ${saleGoods.shopId}" }
                return
            }
            if (shop.token.isNullOrBlank()) {
                log.warn { "$logPrefix 店铺token为空，跳过同步, shopId: ${saleGoods.shopId}" }
                return
            }

            // 调用Lazada API获取商品详情
            syncProduct(logPrefix, platformProductId, shop, saleGoods)
        } catch (e: Exception) {
            log.error(e) { "同步商品（部分） 异常 saleGoodsId: ${saleGoods.saleGoodsId}, platformProductId: ${saleGoods.platformProductId}, errorMessage: ${e.message}" }
        }
    }

    /**
     * 同步单个商品的部分信息
     * 使用wrapper更新，避免触发拦截器更新reviser字段
     */
    private fun syncProduct(
        logPrefix: String,
        platformProductId: String,
        shop: Shop,
        saleGoods: SaleGoods
    ) {
        val country = saleGoods.country ?: run {
            log.warn { "$logPrefix 商品国家为空，跳过同步" }
            return
        }

        try {
            val response = RetryTemplateUtils.retryExecute<LazadaProductItemDetailResp, Exception>(
                retryParams = queryProductRetryParams,
                nonRetryableExceptions = nonRetryableExceptions,
                retryCallback = { ctx ->
                    log.info { "$logPrefix 正在查询商品详情, platformProductId: $platformProductId, country=$country, attempt=${ctx.retryCount + 1}" }
                    queryLazadaProductWithRateLimit(logPrefix, platformProductId, country, shop.token!!)
                },
                recoveryCallback = { ctx ->
                    if (nonRetryableExceptions.keys.any { it.isInstance(ctx.lastThrowable) }) {
                        throw ctx.lastThrowable!!
                    }
                    throw ctx.lastThrowable ?: Exception("未知Lazada接口异常")
                },
            )

            val productDetail = response.data ?: run {
                log.warn { "$logPrefix 获取商品详情成功但数据为空" }
                return
            }
            // 执行同步逻辑
            performProductSync(logPrefix, saleGoods, productDetail)
        } catch (e: Exception) {
            if (nonRetryableExceptions.keys.any { it.isInstance(e) }) {
                when (e) {
                    is ProductNotFoundException -> {
                        markProductAsNotFoundIfNeeded(logPrefix, saleGoods, platformProductId)
                        return
                    }

                    else -> return
                }
            }
            log.error(e) { "$logPrefix 异常 platformProductId: $platformProductId, errorMessage: ${e.message}" }
        }
    }

    /**
     * 更新商品状态
     */
    private fun updateProductStatus(
        logPrefix: String,
        saleGoods: SaleGoods,
        productDetail: LazadaProductItemDetailResp.ProductData
    ): Boolean {
        val productStatusType = productDetail.status
        if (productStatusType.isNullOrBlank()) {
            return false
        }

        try {
            // 将Lazada状态映射为系统状态
            val publishState = when (productStatusType) {
                LazadaProductStateEnum.ACTIVE.code -> ProductPublishStateEnum.ACTIVE.code
                LazadaProductStateEnum.IN_ACTIVE.code -> ProductPublishStateEnum.IN_ACTIVE.code
                LazadaProductStateEnum.Deleted.code -> ProductPublishStateEnum.DELETED.code
                LazadaProductStateEnum.PendingQC.code -> ProductPublishStateEnum.IN_ACTIVE.code
                LazadaProductStateEnum.Suspended.code -> ProductPublishStateEnum.IN_ACTIVE.code
                else -> {
                    log.warn { "$logPrefix 无法识别的商品状态类型 productStatusType: $productStatusType" }
                    return false
                }
            }

            // 如果状态没有变化，不需要更新
            if (saleGoods.publishState == publishState) {
                return false
            }

            // 使用wrapper更新，避免触发拦截器
            saleGoodsRepository.ktUpdate()
                .eq(SaleGoods::saleGoodsId, saleGoods.saleGoodsId)
                .set(SaleGoods::publishState, publishState)
                .update()

            log.info { "$logPrefix 商品状态已更新, platformProductId: ${saleGoods.platformProductId}, oldState: ${saleGoods.publishState}, newState: $publishState" }
            return true
        } catch (e: Exception) {
            log.error(e) { "$logPrefix 更新商品状态异常, platformProductId: ${saleGoods.platformProductId}" }
        }

        return false
    }

    /**
     * 获取Lazada商品详情（带限流）
     */
    private fun queryLazadaProductWithRateLimit(
        logPrefix: String,
        platformProductId: String,
        country: String,
        token: String,
    ): LazadaProductItemDetailResp {
        // 限流
        apiRateLimiter.acquire()

        val resp = lazadaApiService.getProductDetailByItemId(country, platformProductId, token)

        // 检查业务错误码
        if (resp.code == ErrorCode.ITEM_NOT_FOUND || resp.code == ErrorCode.SKU_NOT_FOUND) { // 商品不存在
            val msg =
                "$logPrefix Lazada getProductDetail 业务异常: code=${resp.code}, 商品或SKU不存在, platformProductId=$platformProductId"
            log.warn { msg }
            throw ProductNotFoundException(msg)
        }

        if (resp.code == ErrorCode.ILLEGAL_ACCESS_TOKEN) { // 失效的token
            val msg = "$logPrefix Lazada getProductDetail 业务异常: code=${resp.code}, 失效的token, platformProductId=$platformProductId"
            log.warn { msg }
            throw IllegalAccessTokenException(msg)
        }

        if (resp.isSuccess && resp.data != null) return resp

        throw BusinessException("Lazada getProductDetail failed: code=${resp.code}, msg=${resp.message}")
    }

    private fun markProductAsNotFoundIfNeeded(logPrefix: String, saleGoods: SaleGoods, platformProductId: String) {
        if (saleGoods.productNotFound != YesOrNoEnum.YES.code) {
            saleGoods.productNotFound = YesOrNoEnum.YES.code
            saleGoodsRepository.ktUpdate()
                .eq(SaleGoods::saleGoodsId, saleGoods.saleGoodsId)
                .set(SaleGoods::productNotFound, YesOrNoEnum.YES.code)
                .update()
            log.warn { "$logPrefix 商品不存在，已标记为不可用，platformProductId: $platformProductId" }
        }
    }

    /**
     * 执行商品同步逻辑
     */
    private fun performProductSync(
        logPrefix: String,
        saleGoods: SaleGoods,
        productDetail: LazadaProductItemDetailResp.ProductData
    ) {
        // 更新商品状态
        val productStatusUpdated = updateProductStatus(logPrefix, saleGoods, productDetail)

        // 更新SKU状态
        val skuStatusUpdated = syncSkuStatuses(logPrefix, saleGoods, productDetail)

        // 如果有更新，则更新最后同步时间
        if (productStatusUpdated || skuStatusUpdated) {
            updateLastSyncTime(saleGoods.saleGoodsId!!)
            log.info { "$logPrefix 商品同步信息（部分） 已同步更新 (SPU更新: $productStatusUpdated, SKU更新: $skuStatusUpdated)" }
        }
    }

    /**
     * 同步SKU状态
     */
    private fun syncSkuStatuses(
        logPrefix: String,
        saleGoods: SaleGoods,
        productDetail: LazadaProductItemDetailResp.ProductData
    ): Boolean {
        val platformSkuList: List<LazadaProductItemDetailResp.ProductData.Sku>? = productDetail.skus

        if (platformSkuList.isNullOrEmpty()) {
            log.warn { "$logPrefix 商品SKU列表为空，无法同步SKU状态" }
            return false
        }

        // 查询该商品下的所有SKU
        val saleSkuList = saleSkuRepository.getByProductIdSaleGoodsId(
            saleGoods.productId!!,
            saleGoods.saleGoodsId!!
        )

        if (saleSkuList.isEmpty()) {
            return false
        }

        // 为了快速查找，将SKU按sellerSku组织成Map
        val saleSkuMap = saleSkuList.associateBy { it.platformSkuId }

        var skuStatusUpdated = false

        // 处理每个平台SKU
        for (platformSku: LazadaProductItemDetailResp.ProductData.Sku in platformSkuList) {
            val skuId = platformSku.skuId ?: continue
            val saleSku = saleSkuMap[skuId.toString()] ?: continue

            // 更新SKU状态
            val skuUpdated = updateSkuStatus(logPrefix, saleSku, platformSku.status)
            if (skuUpdated) {
                skuStatusUpdated = true
            }
        }

        return skuStatusUpdated
    }

    /**
     * 更新SKU状态
     */
    private fun updateSkuStatus(logPrefix: String, saleSku: SaleSku, skuStatusType: String?): Boolean {
        if (skuStatusType.isNullOrBlank()) {
            return false
        }

        try {
            // 将Lazada SKU状态映射为系统状态
            val publishState = when (skuStatusType) {
                LazadaSkuStateEnum.ACTIVE.code -> ProductPublishStateEnum.ACTIVE.code
                LazadaSkuStateEnum.IN_ACTIVE.code -> ProductPublishStateEnum.IN_ACTIVE.code
                else -> {
                    log.warn { "$logPrefix 无法识别的SKU状态类型 skuStatusType: $skuStatusType, sellerSku: ${saleSku.sellerSku}" }
                    return false
                }
            }

            // 如果状态没有变化，不需要更新
            if (saleSku.publishState == publishState) {
                return false
            }

            // 使用wrapper更新，避免触发拦截器
            saleSkuRepository.ktUpdate()
                .eq(SaleSku::saleSkuId, saleSku.saleSkuId)
                .set(SaleSku::publishState, publishState)
                .update()

            log.info { "$logPrefix SKU状态已更新, sellerSku: ${saleSku.sellerSku}, oldState: ${saleSku.publishState}, newState: $publishState" }
            return true
        } catch (e: Exception) {
            log.error(e) { "$logPrefix 更新SKU状态异常, sellerSku: ${saleSku.sellerSku}" }
        }

        return false
    }


    /**
     * 更新最后同步时间
     */
    private fun updateLastSyncTime(saleGoodsId: Long) {
        saleGoodsRepository.ktUpdate()
            .eq(SaleGoods::saleGoodsId, saleGoodsId)
            .set(SaleGoods::lastScheduledSyncTime, LocalDateTime.now())
            .update()
    }
}

/** 自定义业务异常：不重试的情况 */
class ProductNotFoundException(message: String) : RuntimeException(message)
/** 自定义业务异常：非法访问令牌 */
class IllegalAccessTokenException(message: String) : RuntimeException(message)