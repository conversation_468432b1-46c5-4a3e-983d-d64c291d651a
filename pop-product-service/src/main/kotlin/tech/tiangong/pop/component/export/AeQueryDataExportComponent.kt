package tech.tiangong.pop.component.export

import com.alibaba.excel.EasyExcel
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.core.enums.Bool.YES
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.AliexpressConstants
import tech.tiangong.pop.common.enums.CountryTypeEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductAePublishStateEnum
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.AePublishProductGeneralTemplateExcelDTO
import tech.tiangong.pop.dto.product.AePublishProductPricingDetailExcelDTO
import tech.tiangong.pop.dto.product.PublishProductGeneralTemplateDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.DictClientExternal
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.req.product.ae.ProductAePageQueryReq
import tech.tiangong.pop.resp.product.v2.AeUnifiedPricingResp
import tech.tiangong.pop.service.product.price.sale.AeSalePricingService
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.service.v2.pricing.PricingVariableService
import java.io.File
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * AE查询数据导出组件
 */
@Slf4j
@Component("aeQueryDataExportComponent")
class AeQueryDataExportComponent(
    private val uploaderOssHelper: UploaderOssHelper,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val dictClientExternal: DictClientExternal,
    private val marketStyleComponent: MarketStyleComponent,
    private val downloadTaskService: DownloadTaskService,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val aeSalePricingService: AeSalePricingService,
    private val pricingVariableService: PricingVariableService,
    private val aliexpressProperties: AliexpressProperties,
    private val aliexpressServiceHelper: AliexpressServiceHelper,
) : DownloadTaskInterface() {

    companion object {
        private val platformEnum = PlatformEnum.AE
    }

    /**
     * 创建并提交导出任务
     */
    fun createExportTask(req: ProductAePageQueryReq): String {
        try {
            // 处理时间条件
            if (Objects.equals(YES.code, req.today)) {
                req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
                req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
            }

            // 构建任务名称：导出AE商品查询数据_年年年年月月日日时时分分秒秒
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
            val taskName = "导出AE商品查询数据_$dateStr"
            
            // 创建下载任务
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PUBLISH_LISTING_AE_QUERY_DATA_TASK,
                req.toJson()
            )
            
            return "导出任务已生成，请至下载管理内下载文件"
        } catch (e: Exception) {
            log.error(e) { "创建AE查询数据导出任务失败: ${e.message}" }
            throw BusinessException("提交导出任务失败：${e.message}")
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson(ProductAePageQueryReq::class.java)
            ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        log.info { "开始导出AE查询数据，任务ID: ${task.downloadTaskId}" }

        try {
            // 使用新的查询方法获取数据，直接使用分页查询条件
            val productList = aeSaleGoodsRepository.findQueryDataForExport(req)
            
            if (productList.isEmpty()) {
                throw BusinessException("导出数据为空")
            }

            log.info { "查询到${productList.size}条AE商品数据" }

            // 统一获取字典数据（数据量小，可以预加载）
            val dictDataCache = createDictDataCache()

            // 流式处理：逐个处理产品，避免大量数据占用内存
            val generalTemplateList = mutableListOf<AePublishProductGeneralTemplateExcelDTO>()
            val pricingDetailList = mutableListOf<AePublishProductPricingDetailExcelDTO>()

            log.info { "开始流式处理产品数据, size=${productList.size}" }

            productList.forEach { productTemplate ->
                try {
                    // 转换通用模板数据
                    val generalTemplateData = convertToGeneralTemplateExcelDTO(
                        listOf(productTemplate), 
                        dictDataCache.supplyMethodNameMap, 
                        dictDataCache.trayTypeNameMap
                    ).firstOrNull()

                    if (generalTemplateData != null) {
                        generalTemplateList.add(generalTemplateData)

                        // 生成定价明细数据
                        val pricingDetailData = generatePricingDetailForQueryProduct(
                            generalTemplateData,
                            productTemplate,
                            dictDataCache
                        )
                        pricingDetailList.addAll(pricingDetailData)
                    }
                } catch (e: Exception) {
                    log.error(e) { "处理产品失败: saleGoodsId=${productTemplate.saleGoodsId}, error=${e.message}" }
                }
            }

            if (generalTemplateList.isEmpty()) {
                throw BusinessException("没有生成有效的Excel数据")
            }

            // 生成包含两个sheet的Excel文件
            val excelFile = generateGeneralTemplateExcelWithPricingDetail(
                tempDir, 
                task.taskName!!, 
                generalTemplateList, 
                pricingDetailList
            )

            val fileUploadDTO = uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
            
            log.info { "AE查询数据导出完成，任务ID: ${task.downloadTaskId}, 商品数据${generalTemplateList.size}条, 定价明细${pricingDetailList.size}条" }
            return fileUploadDTO

        } catch (e: Exception) {
            log.error(e) { "导出AE查询数据失败，任务ID: ${task.downloadTaskId}" }
            throw BusinessException("导出AE查询数据失败: ${e.message}", e)
        }
    }

    /**
     * 获取供给方式字典映射
     */
    private fun getSupplyMethodNameMap(): Map<String, DictVo> {
        return dictClientExternal.getTopByDictCode(DictEnum.SUPPLY_MODE)?.let { dictVo ->
            dictVo.children?.associateBy({ it.dictCode }, { it }) ?: emptyMap()
        } ?: emptyMap()
    }

    /**
     * 获取货盘类型字典映射
     */
    private fun getTrayTypeNameMap(): Map<String, DictVo> {
        return dictClientExternal.getTopByDictCode(DictEnum.TRAY_TYPE)?.let { dictVo ->
            dictVo.children?.associateBy({ it.dictCode }, { it }) ?: emptyMap()
        } ?: emptyMap()
    }

    /**
     * 转换为通用模板Excel DTO（复刻ProductPublishAeExportTaskStrategy#convertToGeneralTemplateExcelDTO逻辑）
     */
    private fun convertToGeneralTemplateExcelDTO(
        productList: List<PublishProductGeneralTemplateDTO>,
        supplyMethodNameMap: Map<String, DictVo>,
        trayTypeNameMap: Map<String, DictVo>
    ): List<AePublishProductGeneralTemplateExcelDTO> {
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        
        return productList.map { product ->
            AePublishProductGeneralTemplateExcelDTO().apply {
                this.platformProductId = product.platformProductId?.toString()
                this.shopName = product.shopName
                this.spu = product.spu
                this.goodsType = product.goodsType
                this.waves = product.waves
                this.clothingStyleName = product.clothingStyleName
                this.goodsRepType = trayTypeNameMap[product.goodsRepType]?.dictName ?: product.goodsRepType
                this.productTitle = product.productTitle
                this.shipsFromAttributeValueName = product.shipsFromAttributeValueName
                this.freightTemplateName = product.freightTemplateName
                this.stockType = product.stockType?.let { StockTypeEnum.getByCode(it)?.desc ?: "未知" }
                this.publishState = product.publishState?.let { ProductAePublishStateEnum.getByCode(it)?.desc ?: "未知" }
                this.publishTime = product.publishTime
                this.categoryName = product.categoryName
                this.mainImgUrl = product.mainImgUrl
                this.planningType = product.planningType
                this.planningTypeName = PlanningTypeEnum.getByCode(product.planningType)?.desc
                this.marketCode = product.marketCode
                this.marketName = marketMap?.get(product.marketCode)
                this.marketSeriesCode = product.marketSeriesCode
                this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
                this.productUrl = AliexpressConstants.getFrontUrl(product.productId.toString())
                // AE特有的价格设置
                retailPrice = product.retailPrice
                salePrice = product.salePrice

                // 计算供货价格
                val isCbType = product.shopCountryType == CountryTypeEnum.CB.code &&
                        product.supplyMode == SupplyModeEnum.LOGO_NUM.dictCode

                // 设置供货价格，如果是CB类型，则使用最大CB价格，否则使用最大本地价格
                maxCostPrice = when {
                    isCbType && product.maxCbPrice != null -> product.maxCbPrice
                    product.maxLocalPrice != null -> product.maxLocalPrice
                    else -> product.maxPurchasePrice
                }

                // 转换供给方式
                supplyMode = supplyMethodNameMap[product.supplyMode]?.dictName ?: product.supplyMode
            }
        }
    }

    /**
     * 创建字典数据缓存
     */
    private fun createDictDataCache(): DictDataCache {
        return DictDataCache(
            supplyMethodNameMap = getSupplyMethodNameMap(),
            trayTypeNameMap = getTrayTypeNameMap(),
            shippingFromDict = getShippingFromDict(),
            shippingToDict = getShippingToDict(),
            marketMap = marketStyleComponent.getMarketNodeMap(1),
            marketSeriesMap = marketStyleComponent.getMarketNodeMap(2),
        )
    }

    /**
     * 为查询产品生成定价明细数据（适配查询数据结构）
     */
    private fun generatePricingDetailForQueryProduct(
        generalTemplateData: AePublishProductGeneralTemplateExcelDTO,
        productTemplate: PublishProductGeneralTemplateDTO,
        dictDataCache: DictDataCache,
    ): List<AePublishProductPricingDetailExcelDTO> {
        return try {
            // 查询关联的销售商品、产品和店铺数据
            val saleGoods = productTemplate.saleGoodsId?.let { aeSaleGoodsRepository.getById(it) }
            val product = productTemplate.productId?.let { productRepository.getById(it) }
            val shop = saleGoods?.shopId?.let { shopRepository.getById(it) }

            // 基础数据校验
            if (saleGoods == null || product == null || shop == null) {
                log.warn { "基础数据不完整: saleGoodsId=${productTemplate.saleGoodsId}, productId=${saleGoods?.productId}, shopId=${saleGoods?.shopId}" }
                return emptyList()
            }

            // 查询SKU数据
            val aeSaleSkuList = aeSaleSkuRepository.findEnabledBySaleGoodsId(saleGoods.saleGoodsId!!)

            // 调用统一的定价明细生成方法
            generateSingleProductPricingDetailData(
                generalTemplateData,
                saleGoods,
                product,
                shop,
                aeSaleSkuList,
                dictDataCache,
            )
        } catch (e: Exception) {
            log.error(e) { "为查询产品生成定价明细失败: platformProductId=${generalTemplateData.platformProductId}" }
            emptyList()
        }
    }

    /**
     * 获取AE发货地字典数据
     */
    private fun getShippingFromDict(): Map<String, DictVo> {
        return dictClientExternal.getTopByDictCode(DictEnum.AE_SHIPPING_FROM)?.let { dictVo ->
            // 发货地字段使用 remark 存发货地ID
            dictVo.children?.filter { !it.remark.isNullOrBlank() }
                ?.associateBy({ it.remark!! }, { it }) ?: emptyMap()
        } ?: emptyMap()
    }

    /**
     * 获取AE目的地字典数据
     */
    private fun getShippingToDict(): Map<String, DictVo> {
        return dictClientExternal.getTopByDictCode(DictEnum.AE_SHIPPING_TO)?.let { dictVo ->
            dictVo.children?.associateBy({ it.dictCode }, { it }) ?: emptyMap()
        } ?: emptyMap()
    }

    /**
     * 根据店铺主体判断币种
     */
    private fun getCurrencyByShop(shop: Shop): String? {
        return try {
            aliexpressServiceHelper.getCachedSellerRelations(shop.shopId!!).sellerRelationList?.firstOrNull()?.channelCurrency
                ?: aliexpressProperties.aePlatform.currencyCode // 默认CNY
        } catch (e: Exception) {
            log.warn(e) { "获取店铺主体信息失败，使用默认币种: shopId=${shop.shopId}" }
            null
        }
    }

    /**
     * 计算指定发货地和目的地的Max价格
     * 根据发货地+目的地过滤价格结果，然后取最大值
     */
    private fun calculateMaxPricesForShipTo(pricingResult: AeUnifiedPricingResp, shipsFromId: String, shipToCountry: String?): MaxPriceResult {
        if (!pricingResult.success || pricingResult.results.isEmpty()) {
            log.warn { "价格计算失败或无结果: ${pricingResult.errorMessage}" }
            return MaxPriceResult(null, null)
        }

        // 过滤匹配发货地和目的地的价格结果
        val matchedPrices = pricingResult.results.filter { priceResult ->
            val matchShipFrom = priceResult.shipFrom == shipsFromId

            val matchShipTo = shipToCountry == null ||
                    priceResult.shipTo == shipToCountry

            matchShipFrom && matchShipTo && priceResult.success
        }

        if (matchedPrices.isEmpty()) {
            log.warn { "未找到匹配的价格结果: shipFrom=$shipsFromId, shipTo=$shipToCountry" }
            return MaxPriceResult(null, null)
        }

        // 计算Max划线价和Max售价
        val maxRetailPrice = matchedPrices.mapNotNull { it.retailPrice }.maxOrNull()
        val maxSalePrice = matchedPrices.mapNotNull { it.salePrice }.maxOrNull()

        return MaxPriceResult(maxRetailPrice, maxSalePrice)
    }

    /**
     * 生成单个产品的定价明细数据
     */
    private fun generateSingleProductPricingDetailData(
        generalTemplateData: AePublishProductGeneralTemplateExcelDTO,
        saleGoods: AeSaleGoods,
        product: Product,
        shop: Shop,
        aeSaleSkuList: List<AeSaleSku>,
        dictDataCache: DictDataCache,
    ): List<AePublishProductPricingDetailExcelDTO> {
        val pricingDetailList = mutableListOf<AePublishProductPricingDetailExcelDTO>()
        
        try {
            // 跳过V1版本的定价规则
            if (saleGoods.priceCalculateRule == PriceCalculateRuleEnum.V1.code) return emptyList()

            if (aeSaleSkuList.isEmpty()) return emptyList()

            // 根据店铺主体判断币种
            val currency = getCurrencyByShop(shop)

            // 在外层一次性计算价格
            val pricingResult = aeSalePricingService.autoCalPriceBySale(saleGoods.saleGoodsId!!, saleGoods.shopId!!)

            // 按发货地分组处理
            val shipsFromGroups = aeSaleSkuList.groupBy { it.shipsFromAttributeValueId }

            shipsFromGroups.forEach { (shipsFromId, skusInSameShipsFrom) ->
                val effectShipFromId = shipsFromId ?: aliexpressProperties.aePlatform.shipsFromPropertyValueId
                // 获取发货地中文名称
                val shipsFromName = dictDataCache.shippingFromDict[effectShipFromId.toString()]?.dictName ?: skusInSameShipsFrom.firstOrNull()?.shipsFromAttributeValueName

                // 获取当前发货地的默认目的地（同一发货地只需调用一次）
                val defaultShipTo = pricingVariableService.getDefaultShipTo(platformEnum.platformId, effectShipFromId.toString())
                
                // 获取目的地列表
                val shipToList = pricingVariableService.getShipToList(platformEnum.platformId, effectShipFromId.toString())

                shipToList.forEach { shipTo ->
                    // 获取定价参数变量
                    val pricingVariables = pricingVariableService.getProductPricingVariables(
                        product,
                        shop,
                        platformEnum.platformId,
                        effectShipFromId.toString(),
                        shipTo,
                    )

                    // 计算Max划线价和Max售价（基于已计算好的pricingResult）
                    val maxPrices = calculateMaxPricesForShipTo(pricingResult, effectShipFromId.toString(), shipTo)

                    // 获取目的地中文名称
                    val shipToChineseName = dictDataCache.shippingToDict[shipTo]?.dictName ?: shipTo

                    // 创建定价明细记录
                    val pricingDetail = AePublishProductPricingDetailExcelDTO().apply {
                        this.platformProductId = generalTemplateData.platformProductId
                        this.shopName = generalTemplateData.shopName
                        this.spu = generalTemplateData.spu
                        this.supplyMode = dictDataCache.supplyMethodNameMap[product.supplyMode]?.dictName ?: product.supplyMode
                        this.shipsFrom = shipsFromName
                        this.shipTo = shipToChineseName
                        this.currency = currency
                        this.maxRetailPrice = maxPrices.maxRetailPrice
                        this.maxSalePrice = maxPrices.maxSalePrice
                        this.defaultFlag = if (shipTo == defaultShipTo) "是" else "-"
                        this.priceCalculateType = "公式定价" // 固定为公式定价

                        // 填充定价变量
                        this.logisticsCost = pricingVariables.logisticsCost
                        this.storageCost = pricingVariables.storageCost
                        this.taxRate = pricingVariables.taxRate
                        this.returnRate = pricingVariables.returnRate
                        this.adCost = pricingVariables.adCost
                        this.commission = pricingVariables.commission
                        this.withdrawalFee = pricingVariables.withdrawalFee
                        this.targetMargin = pricingVariables.targetMargin
                        this.marketingRate = pricingVariables.marketingRate
                        this.logisticsRate = pricingVariables.logisticsRate
                        this.discountRate = pricingVariables.discountRate
                    }

                    pricingDetailList.add(pricingDetail)
                }
            }

        } catch (e: Exception) {
            log.error(e) { "生成单个产品定价明细数据失败: platformProductId=${generalTemplateData.platformProductId}" }
        }

        return pricingDetailList
    }

    /**
     * 生成包含定价明细的通用模板Excel文件
     */
    private fun generateGeneralTemplateExcelWithPricingDetail(
        tempDir: File, 
        taskName: String, 
        generalDataList: List<AePublishProductGeneralTemplateExcelDTO>,
        pricingDetailList: List<AePublishProductPricingDetailExcelDTO>,
    ): File {
        val fileName = "$taskName.xlsx"
        val excelFile = File(tempDir, fileName)
        
        try {
            // 创建Excel工作簿
            val writer = EasyExcel.write(excelFile).build()

            // sheet1 - 商品数据
            val sheet1 = EasyExcel.writerSheet("AE商品查询数据")
                .head(AePublishProductGeneralTemplateExcelDTO::class.java)
                .build()
            writer.write(generalDataList, sheet1)

            // sheet2 - 定价明细
            val sheet2 = EasyExcel.writerSheet("定价明细")
                .head(AePublishProductPricingDetailExcelDTO::class.java)
                .build()
            writer.write(pricingDetailList, sheet2)

            writer.finish()
            return excelFile
        } catch (e: Exception) {
            log.error(e) { "生成包含定价明细的Excel文件失败" }
            throw BusinessException("生成Excel文件失败：${e.message}")
        }
    }
}

/**
 * Max价格结果
 */
private data class MaxPriceResult(
    /** Max划线价 */
    val maxRetailPrice: BigDecimal?,
    /** Max售价 */
    val maxSalePrice: BigDecimal?,
)

/**
 * 字典数据缓存
 */
private data class DictDataCache(
    /** 供给方式字典 */
    val supplyMethodNameMap: Map<String, DictVo>,
    /** 货盘类型字典 */
    val trayTypeNameMap: Map<String, DictVo>,
    /** 发货地字典 */
    val shippingFromDict: Map<String, DictVo>,
    /** 目的地字典 */
    val shippingToDict: Map<String, DictVo>,
    /** 市场字典 */
    val marketMap: Map<String, String>?,
    /** 系列字典 */
    val marketSeriesMap: Map<String, String>?,
) 