package tech.tiangong.pop.component.price

import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import tech.tiangong.pop.bo.ProductPriceAlertSkcData
import tech.tiangong.pop.bo.ProductPriceAlertSkuData
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.config.PigeonMessageProperties
import tech.tiangong.pop.constant.ImageConstants
import tech.tiangong.pop.dao.entity.ImageRepository
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.ImageRepositoryRepository
import tech.tiangong.pop.dao.repository.ProductTagRepository
import tech.tiangong.pop.dto.settings.PriceAlertConfigKey
import tech.tiangong.pop.enums.PriceAlertTypeEnum
import tech.tiangong.pop.enums.TagPopPriceErrorEnum
import tech.tiangong.pop.enums.TagPopPriceInterceptEnum
import tech.tiangong.pop.external.PigeonMessageClientExternal
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.product.ProductPriceAlertCheckResp
import tech.tiangong.pop.resp.product.ProductPriceAlertSkcCheckResp
import tech.tiangong.pop.resp.product.ProductPriceAlertSkuCheckResp
import tech.tiangong.pop.resp.settings.PriceAlertConfigurationDetailResp
import tech.tiangong.pop.resp.settings.PriceAlertConfigurationResp
import tech.tiangong.pop.service.settings.PriceAlertConfigurationService
import tech.tiangong.pop.utils.ImageUtils
import java.math.BigDecimal

/**
 * 价格兜底基础组件
 */
@Slf4j
abstract class ProductPriceAlertBaseComponent(
    protected val priceAlertConfigurationService: PriceAlertConfigurationService,
    protected val pigeonMessageClientExternal: PigeonMessageClientExternal,
    protected val productTagRepository: ProductTagRepository,
    protected val pigeonMessageProperties: PigeonMessageProperties,
    protected val imageRepositoryRepository: ImageRepositoryRepository,
) {
    /**
     * 上架后校验方法 - 使用统一的数据模型
     *
     * @return 校验结果
     */
    fun validateAfterPublish(
        param: PriceAlertValidationParam
    ): PriceAlertValidationResult {
        // 检查基本条件
        if (param.skcDataList.isEmpty()){
            log.info { "商品[${param.product.productId}]没有有效的SKC，跳过价格兜底" }
            return PriceAlertValidationResult(
                valid = true,
                message = "没有有效的SKC，跳过价格兜底"
            )
        }

        // 获取价格预警配置
        val priceAlertConfigs = loadPriceAlertConfigs(param.platformId, param.categoryId)
        val priceAlertConfig = priceAlertConfigs.firstOrNull()

        // 获取站点价格区间配置
        val detailConfig = priceAlertConfig?.let {
            priceAlertConfigs.flatMap { it.details }
                .find { it.countryCode == param.countryCode }
        }

        val product = param.product
        // 如果没有找到价格配置，则跳过校验
        if (priceAlertConfig == null || detailConfig == null) {
            log.info { "商品[${product.productId}], categoryId: $param.categoryId, 未找到价格配置，跳过价格兜底" }
            return PriceAlertValidationResult(
                valid = true,
                message = "未找到价格配置，跳过价格兜底"
            )
        }

        // 执行价格兜底
        val checkResult = checkProductComplete(
            product,
            param.skcDataList,
            param.skuDataMap,
            param.platformId,
            param.countryCode,
            priceAlertConfig,
            detailConfig,
        )

        var notificationContent: String? = null
        if (!checkResult.passed) {
            try {
                if (param.sendNotification == Bool.YES.code) {
                    notificationContent = sendPriceAlertNotification(param.product, checkResult)
                }
            } catch (e: Exception) {
                log.error(e) { "发送价格异常通知失败, productId: ${param.product.productId}, spuCode: ${param.product.spuCode}: ${e.message}" }
            }

            val msg = buildString {
                append("价格兜底不通过，存在价格异常")
                if (notificationContent.isNotBlank()) {
                    append("，已发送通知: $notificationContent")
                }
            }

            if (param.skipValidation == Bool.NO.code) {
                return PriceAlertValidationResult(
                    valid = false,
                    message = msg,
                    checkResult = checkResult,
                    tagged = true
                )
            }

            log.warn { "商品[${param.product.productId}], spuCode: ${param.product.spuCode}价格兜底不通过，但设置了跳过校验，继续上架" }

            return PriceAlertValidationResult(
                valid = true,
                message = buildString {
                    append("价格兜底不通过，但已设置跳过校验")
                    if (notificationContent.isNotBlank()) {
                        append("，已发送通知: $notificationContent")
                    }
                },
                checkResult = checkResult,
                tagged = true
            )
        }

        return PriceAlertValidationResult(
            valid = true,
            message = "价格兜底通过",
            checkResult = checkResult,
        )
    }

    /**
     * 上架前校验方法 - 使用统一的数据模型
     * @return 校验结果
     */
    fun validateBeforePublish(
        param: PriceAlertValidationParam
    ): PriceAlertValidationResult {
        // 上架前和上架后逻辑一致，复用相同的方法
        return validateAfterPublish(param)
    }

    /**
     * 获取所有需要应用的标签
     * 将SKC和SKU级别的标签汇总到一个集合中
     */
    protected fun collectAllTags(checkResult: ProductPriceAlertCheckResp): Map<String, Set<String>> {
        val allTags = mutableMapOf<String, MutableSet<String>>()

        // 收集所有SKC和SKU的标签
        checkResult.skcResults.forEach { skcResult ->
            // 跳过通过校验的SKC
            if (skcResult.passed) {
                return@forEach
            }

            // 收集SKC级别标签
            skcResult.tags.forEach { (tagKey, tagValues) ->
                val values = allTags.getOrPut(tagKey) { mutableSetOf() }
                values.addAll(tagValues)
            }

            // 收集SKU级别标签
            skcResult.skuResults.forEach { skuResult ->
                if (!skuResult.passed) {
                    skuResult.tags.forEach { (tagKey, tagValues) ->
                        val values = allTags.getOrPut(tagKey) { mutableSetOf() }
                        values.addAll(tagValues)
                    }
                }
            }
        }

        return allTags
    }

    /**
     * 发送价格异常通知
     */
    private fun sendPriceAlertNotification(
        product: Product,
        checkResult: ProductPriceAlertCheckResp
    ): String {
        // 确定价格异常类型
        val alertType = ProductPriceAlertNotificationTemplates.determinePriceAlertType(checkResult)

        // 根据异常类型选择接收者列表
        val receivers = when (alertType) {
            PriceAlertTypeEnum.COST_PRICE_LOW, PriceAlertTypeEnum.COST_PRICE_HIGH -> pigeonMessageProperties.costPriceAlertReceivers
            PriceAlertTypeEnum.RETAIL_PRICE_LOW, PriceAlertTypeEnum.RETAIL_PRICE_HIGH -> pigeonMessageProperties.retailPriceAlertReceivers
            PriceAlertTypeEnum.SALE_PRICE_LOW, PriceAlertTypeEnum.SALE_PRICE_HIGH -> pigeonMessageProperties.salePriceAlertReceivers
            PriceAlertTypeEnum.OTHER -> pigeonMessageProperties.defaultReceivers
        }

        // 获取第一个异常的SKC
        val abnormalSkc = checkResult.skcResults.firstOrNull { !it.passed }
            ?: return "没有发现价格异常"
        val spuCode = checkResult.spuCode!!
        val colorCode = abnormalSkc.colorCode

        val imageRepository = imageRepositoryRepository.ktQuery()
            .select(ImageRepository::imageUrls, ImageRepository::mainUrl)
            .eq(ImageRepository::spuCode, product.spuCode)
            .orderByDesc(ImageRepository::createdTime)
            .list().firstOrNull()

        val skcImageUrl: String? = if (colorCode.isNotBlank()) {
            val images: List<ImageAniVo> = imageRepository?.imageUrls?.parseJsonList(ImageAniVo::class.java) ?: emptyList()
            ImageUtils.filterImagesBySkc(images, spuCode, colorCode!!).firstOrNull()?.ossImageUrl
        } else {
            null
        } ?: imageRepository?.mainUrl?.split(ImageConstants.Url.DELIM)?.firstOrNull()

        // 获取标题和内容
        val title = ProductPriceAlertNotificationTemplates.getNotificationTitle(alertType)
        val baseContent  = ProductPriceAlertNotificationTemplates.getNotificationContent(alertType, product, checkResult)
        // 如果存在图片 URL，就拼接为 Markdown 格式；否则不拼接
        val imageMarkdown = skcImageUrl.takeIf { it.isNotBlank() }
            ?.let { "**图片：** ![详情图]($it${pigeonMessageProperties.aliyunImageParameters})" }
            ?: ""

        // 将图片 Markdown 追加到基础内容后面
        val content = buildString {
            append(baseContent)
            if (imageMarkdown.isNotBlank()) {
                appendLine()
                appendLine()
                append(imageMarkdown)
            }
        }

        // 发送通知
        pigeonMessageClientExternal.sendPriceFloorNotify(
            receivers = receivers,
            title = title,
            content = content,
            queryKey = "price_alert_${product.productId}"
        )

        log.info { "已发送价格异常通知，类型: $alertType, 商品ID: ${product.productId}, spuCode: ${product.spuCode}" }
        return title + content
    }

    /**
     * 完整的商品价格兜底流程
     */
    private fun checkProductComplete(
        product: Product,
        skcDataList: List<ProductPriceAlertSkcData>,
        skuDataMap: Map<Long, List<ProductPriceAlertSkuData>>,
        platformId: Long,
        countryCode: String,
        priceAlertConfig: PriceAlertConfigurationResp,
        detailConfig: PriceAlertConfigurationDetailResp,
    ): ProductPriceAlertCheckResp {
        // 创建响应对象
        val resp = ProductPriceAlertCheckResp(
            productId = product.productId,
            spuCode = product.spuCode,
            platformId = platformId,
            categoryId = product.categoryId,
            categoryName = product.categoryName,
            countryCode = countryCode,
            supplyMode = product.supplyMode
        )

        // 填充平台名称
        PlatformEnum.getByPlatformId(platformId)?.let {
            resp.platformName = it.platformName
        }

        // 填充价格区间信息
        resp.costPriceMin = priceAlertConfig.costPriceMin
        resp.costPriceMax = priceAlertConfig.costPriceMax
        resp.salePriceMin = detailConfig.salePriceMin
        resp.salePriceMax = detailConfig.salePriceMax
        resp.retailPriceMin = detailConfig.retailPriceMin
        resp.retailPriceMax = detailConfig.retailPriceMax
        resp.currencyType = detailConfig.currencyType

        // 检查每个SKC
        val skcResults = skcDataList.map { skcData ->
            val skcResult = ProductPriceAlertSkcCheckResp(
                skc = skcData.skc,
                colorCode = skcData.colorCode,
                productSkcId = skcData.productSkcId,
                costPrice = skcData.costPrice,
                supplyPrice = skcData.supplyPrice,
            )

            // 首先检查定价成本
            checkCostPrice(skcResult, priceAlertConfig)

            // 如果定价成本不合理，则不再检查售价和划线价
            if (!skcResult.interceptPassed) {
                log.info { "SKC[${skcData.skc}], skcId: ${skcData.skcId}的定价成本不合理，跳过售价和划线价检查" }
                return@map skcResult
            }

            // 添加并检查SKU级别价格
            val skuResults = mutableListOf<ProductPriceAlertSkuCheckResp>()
            val skuList = skuDataMap[skcData.skcId] ?: emptyList()

            for (skuData in skuList) {
                val skuResult = ProductPriceAlertSkuCheckResp(
                    sizeName = skuData.sizeName,
                    salePrice = skuData.salePrice,
                    retailPrice = skuData.retailPrice,
                )

                // 检查SKU价格合理性
                checkSkuPriceValidity(skuResult)

                // 检查SKU价格区间
                checkSkuPriceRanges(
                    skuResult,
                    detailConfig.salePriceMin,
                    detailConfig.salePriceMax,
                    detailConfig.retailPriceMin,
                    detailConfig.retailPriceMax,
                )

                skuResults.add(skuResult)
            }

            // 如果SKC下有任何一个SKU不通过，则SKC也不通过
            if (skuResults.isNotEmpty() && skuResults.any { !it.passed }) {
                skcResult.passed = false

                // 继承SKU的兜底和异常标识
                if (skuResults.any { !it.interceptPassed }) {
                    skcResult.interceptPassed = false
                }

                if (skuResults.any { !it.errorPassed }) {
                    skcResult.errorPassed = false
                }
            }

            skcResult.skuResults = skuResults
            skcResult
        }

        resp.skcResults = skcResults

        // 更新商品级别的状态
        if (skcResults.any { !it.passed }) {
            resp.passed = false
        }

        // 聚合商品级别的标签
        val allTags = mutableMapOf<String, MutableSet<String>>()
        skcResults.forEach { skcResult ->
            skcResult.tags.forEach { (tagKey, tagValues) ->
                val values = allTags.getOrPut(tagKey) { mutableSetOf() }
                values.addAll(tagValues)
            }
        }

        // 转换为不可变集合
        allTags.forEach { (tagKey, tagValues) ->
            resp.tags[tagKey] = tagValues.toList()
        }

        return resp
    }

    /**
     * 检查SKU价格合理性
     */
    private fun checkSkuPriceValidity(result: ProductPriceAlertSkuCheckResp) {
        val salePrice = result.salePrice
        val retailPrice = result.retailPrice

        // 检查划线价是否为0
        if (retailPrice != null && retailPrice.compareTo(BigDecimal.ZERO) == 0) {
            result.addErrorReason(TagPopPriceErrorEnum.RETAIL_PRICE_ZERO)
        }

        // 检查划线价是否小于售价
        if (salePrice != null && retailPrice != null && retailPrice < salePrice) {
            result.addErrorReason(TagPopPriceErrorEnum.RETAIL_PRICE_LOWER_THAN_SALE_PRICE)
        }
    }

    /**
     * 检查SKU价格区间
     */
    private fun checkSkuPriceRanges(
        result: ProductPriceAlertSkuCheckResp,
        salePriceMin: BigDecimal?,
        salePriceMax: BigDecimal?,
        retailPriceMin: BigDecimal?,
        retailPriceMax: BigDecimal?
    ) {
        // 检查售价是否在配置的区间内
        val salePrice = result.salePrice
        if (salePrice != null) {
            if (salePriceMin != null && salePrice <= salePriceMin) {
                result.addInterceptReason(TagPopPriceInterceptEnum.SALE_PRICE_LOW)
            }

            if (salePriceMax != null && salePrice >= salePriceMax) {
                result.addInterceptReason(TagPopPriceInterceptEnum.SALE_PRICE_HIGH)
            }
        }

        // 检查划线价是否在配置的区间内
        val retailPrice = result.retailPrice
        if (retailPrice != null) {
            if (retailPriceMin != null && retailPrice <= retailPriceMin) {
                result.addInterceptReason(TagPopPriceInterceptEnum.RETAIL_PRICE_LOW)
            }

            if (retailPriceMax != null && retailPrice >= retailPriceMax) {
                result.addInterceptReason(TagPopPriceInterceptEnum.RETAIL_PRICE_HIGH)
            }
        }
    }

    /**
     * 加载价格预警配置
     */
    protected fun loadPriceAlertConfigs(platformId: Long, categoryId: Long): List<PriceAlertConfigurationResp> {
        val key = PriceAlertConfigKey(platformId, categoryId)
        return priceAlertConfigurationService.findByPlatformAndCategoryId(key)
    }

    /**
     * 检查定价成本是否在配置的区间内
     */
    private fun checkCostPrice(
        result: ProductPriceAlertSkcCheckResp,
        config: PriceAlertConfigurationResp
    ) {
        val costPrice = result.costPrice ?: return
        // 如果没有定价成本，则跳过检查

        // 检查是否设置了成本价区间
        val costPriceMin = config.costPriceMin
        val costPriceMax = config.costPriceMax

        if (costPriceMin != null && costPrice <= costPriceMin) {
            result.addInterceptReason(TagPopPriceInterceptEnum.COST_PRICE_LOW)
        }

        if (costPriceMax != null && costPrice >= costPriceMax) {
            result.addInterceptReason(TagPopPriceInterceptEnum.COST_PRICE_HIGH)
        }
    }

}