package tech.tiangong.pop.component.export

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dao.entity.AeSelectedProductSpu
import tech.tiangong.pop.dao.entity.AeUpdatePlatformTask
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.repository.AeSelectedProductSpuRepository
import tech.tiangong.pop.dao.repository.AeUpdatePlatformTaskRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.export.AeSelectedUpdateExportDto
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.selected.AeUpdateTaskStatusEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.FileExportUtils
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * AE精选商品-导出-更新任务
 * <AUTHOR>
 * @date 2025-7-18 17:55:53
 */
@Component
@Slf4j
class AeSelectedUpdateTaskExportComponent(
    private val aeUpdatePlatformTaskRepository: AeUpdatePlatformTaskRepository,
    private val aeSelectedProductSpuRepository: AeSelectedProductSpuRepository,
    private val shopRepository: ShopRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val downloadTaskService: DownloadTaskService,
) : DownloadTaskInterface() {

    /**
     * 创建下载任务
     */
    fun createExportTask() {
        val dateStr = LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault())
        )
        downloadTaskService.createTask("AE精品商品导出更新结果_$dateStr", DownloadTaskTypeEnum.AE_SELECTED_UPDATE_TASK, "{}")
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        // 组装excel dto集合
        val dataDtoList = mutableListOf<AeSelectedUpdateExportDto>()

        // 获取数据
        var pageNum = 1L
        val pageSize = 1000L
        while (true) {
            val queryWrapper = QueryWrapper<AeUpdatePlatformTask>()
            queryWrapper.orderByDesc("created_time")
            val resultPage = aeUpdatePlatformTaskRepository.page(Page(pageNum, pageSize), queryWrapper)
            if (resultPage == null || resultPage.records.isEmpty()) {
                break
            }

            val baseData = resultPage.records

            // 获取店铺Map
            val shopIds = baseData.map { it.shopId }.toSet()
            val shopMap = shopRepository.listByIds(shopIds)?.associateBy { it.shopId }

            // 获取selectedProduct
            val latestUpdateTaskIds = mutableListOf<Long>()
            val productids = baseData.map { it.productId }.toSet()
            if (productids.isNotEmpty()) {
                aeSelectedProductSpuRepository.ktQuery()
                    .eq(AeSelectedProductSpu::productId, productids)
                    .select(AeSelectedProductSpu::updateTaskId)
                    .list()
                    .mapNotNull { c -> c.updateTaskId }
                    .forEach { updateTaskId ->
                        latestUpdateTaskIds.add(updateTaskId)
                    }
            }
            val respList = baseData.map {
                AeSelectedUpdateExportDto().apply {
                    this.aePid = it.productId?.toString()
                    this.shopName = shopMap?.get(it.shopId)?.shopName
                    this.updateUserName = it.creatorName
                    this.updateTime = it.createdTime
                    this.completeTime = it.completeTime
                    this.updateStatus = it.taskStatus?.let { ts -> AeUpdateTaskStatusEnum.getByCode(ts)?.desc ?: "未知状态" }
                    this.exceptionInfo = it.exceptionInfo
                    this.latest = if (latestUpdateTaskIds.isNotEmpty() && latestUpdateTaskIds.contains(it.id)) "是" else "否"
                }
            }
            pageNum += 1
            if (respList.isNotEmpty()) {
                dataDtoList.addAll(respList)
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, AeSelectedUpdateExportDto::class.java, dataDtoList)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
