package tech.tiangong.pop.component.lazada

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import jakarta.annotation.Resource
import okhttp3.internal.toImmutableList
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper.getId
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.dto.ProductImageChangeStateDto
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.enums.ProductImagePackageStateEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.common.req.BarcodeQuery
import tech.tiangong.pop.common.req.CreateBarCodeReq
import tech.tiangong.pop.common.resp.BarCodeListResp
import tech.tiangong.pop.common.resp.BarCodeResp
import tech.tiangong.pop.constant.ImageConstants.Url
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.mq.MessageRecord
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.dto.product.PublishPlatformAttributeDTO
import tech.tiangong.pop.enums.LazadaSkuStateEnum
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.ProductPublishHelper
import tech.tiangong.pop.helper.ProductPublishLazadaHelper
import tech.tiangong.pop.req.sdk.lazada.CreateProductRequest
import tech.tiangong.pop.req.sdk.lazada.LazadaProductAttributes
import tech.tiangong.pop.req.settings.SkuCodeGenerateReq
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp
import tech.tiangong.pop.service.SkuCodeGenerateService
import tech.tiangong.pop.service.category.LazadaCategoryService
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.mq.MessageRecordService
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductCreateV2Service
import tech.tiangong.pop.utils.AssertUtils.requireBusiness
import tech.tiangong.pop.utils.AttributesMapperUtils
import tech.tiangong.pop.utils.ImageUtils
import tech.tiangong.pop.utils.getRootMessage
import tech.tiangong.sdp.common.req.ProductOnlineNoticeReq
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.stream.Collectors

/**
 * Lazada 创建/更新商品
 */
@Component
@Slf4j
class LazadaUpdateProductComponent(
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val productCreateV2Service: ProductCreateV2Service,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val lazadaBrandRepository: LazadaBrandRepository,
    private val lazadaApiService: LazadaApiService,
    private val sizeTemplateRepository: SizeTemplateRepository,
    private val barCodeService: BarCodeService,
    private val skuCodeGenerateService: SkuCodeGenerateService,
    private val inspirationClientExternal: InspirationClientExternal,
    private val messageRecordService: MessageRecordService,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val productPublishLazadaHelper: ProductPublishLazadaHelper,
    private var productSaleAttributesV2Repository: ProductSaleAttributesV2Repository,
) {
    @Resource
    private lateinit var lazadaCategoryService: LazadaCategoryService

    /**
     * 创建/更新商品
     */
    fun updateProduct(shopId: Long, saleGoods: SaleGoods, temuSpuId: Long? = null) {
        val logPrefix = "[LazadaProductSync] spuCode=${saleGoods.spuCode}, productId=${saleGoods.productId}, saleGoodsId=${saleGoods.saleGoodsId} - "
        log.info { "${logPrefix}开始创建/更新商品" }

        saleGoodsRepository.updateSyncState(saleGoods.saleGoodsId!!, PlatformSyncStateEnum.PROCESSING)

        val productId = saleGoods.productId!!
        val product = productRepository.getById(productId) ?: throw BusinessException("未设置商品Product!")
        val spuCode = saleGoods.spuCode!!
        val saleGoodsId = saleGoods.saleGoodsId!!
        var requestParams: String? = null
        var respParams: String? = null
        var opType: Int? = null
        val countryCode = saleGoods.country ?: throw PublishGlobalBizException("站点不能为空")

        try {
            val shop = shopRepository.getById(shopId) ?: throw BusinessException("店铺不存在 shopId=$shopId")
            val accessToken = shop.token ?: throw BusinessException("${shop.shopName}店铺没有token")
            val saleSkcList = saleSkcRepository.findBySaleGoodsId(saleGoodsId)
                .takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKC!")

            saleGoods.productTitle.takeIf { it.isNotBlank() } ?: throw BusinessException("商品标题不能为空")

            var saleSkuList = saleSkuRepository.ktQuery()
                .`in`(SaleSku::saleSkcId, saleSkcList.map { it.saleSkcId }.toSet())
                .eq(SaleSku::enable, Bool.YES.code)
                .list().takeIf { it.isNotEmpty() } ?: throw BusinessException("未设置商品SKU!")

            //查询商品属性
//            var platformAttributes = saleGoodsRepository.findPlatformAttributes(product.categoryId!!, LAZADA.platformId, saleGoods.productId!!)
//            if (CollectionUtils.isEmpty(platformAttributes)) {
//                throw BaseBizException("属性面料成分不能为空，请在品类管理关联面料属性")
//            }
//            platformAttributes = platformAttributes.filter { k ->
//                StringUtils.isNotBlank(k.platformAttributeKeyName) && StringUtils.isNotBlank(k.platformAttributeValue)
//            }

            val platformAttributes = productSaleAttributesV2Repository
                .findPlatformAttributes(product.categoryId!!, LAZADA.platformId, saleGoods.saleGoodsId!!)
                .filter { k -> StringUtils.isNoneBlank(k.platformAttributeCode, k.platformAttributeKeyName) }
                .takeIf { it.isNotEmpty() }
                ?: throw BusinessException("属性不能为空，请在品类管理关联属性，或检查平台属性是否正确设置")

            //校验品类
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
                product.categoryId!!,
                LAZADA.platformId,
                ALIBABA.channelId
            ) ?: throw PublishAscBizException("找不到品类映射信息")

            if (categoryMapping.platformCategoryId.isNullOrBlank()) {
                throw PublishAscBizException("找不到平台品类ID")
            }

            val platformCategoryId = lazadaCategoryService.getCategoryId(saleGoods.country!!, categoryMapping.platformCategoryName!!)?.toString()
                ?: throw PublishAscBizException("找不到lazada品类, country: $saleGoods.country")

            validateSaleSku(saleSkcList, saleSkuList)
            //校验图库资料是否存在
            val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
                ?: throw PublishAscBizException("图库不存在，请先上传图片")
            // 校验是否缺少101-106以及规格图的图片资料
            validateImageData(imageRepository)

            val paramWrapperDto = ProductSyncParamWrapper(
                product = product,
                saleGoods = saleGoods,
                shop = shop,
                platformCategoryId = platformCategoryId,
                country = saleGoods.country!!,
                logPrefix = logPrefix,
                title = saleGoods.productTitle!!,
                imageRepository = imageRepository
            )

            // 延迟设置paramWrapperDto其他参数
            paramWrapperDto.picture = getPicture(paramWrapperDto)
            //上传图片至lazada
            paramWrapperDto.imageCollection = uploadImgToLazada(paramWrapperDto, saleSkcList.mapNotNull { it.colorCode }.toSet())

            val newSkcList = saleSkcList.map { skc ->
                skc.pictures = ImageUtils.filterImagesBySkc(
                    images = paramWrapperDto.requiredImageCollection.skcImages,
                    spuCode = spuCode,
                    colorCode = skc.colorCode!!
                ).firstOrNull()?.ossImageUrl

                skc
            }

            validateSkcPictures(newSkcList)

            saleSkcRepository.updateBatchById(
                newSkcList.map { SaleSkc().apply { saleSkcId = it.saleSkcId; pictures = it.pictures } }
            )

            saleGoods.apply {
                if (this.platformCategoryId != platformCategoryId) this.platformCategoryId = platformCategoryId
                if (this.platformCategoryName != categoryMapping.platformCategoryName) this.platformCategoryName = categoryMapping.platformCategoryName
            }

            paramWrapperDto.skcList = newSkcList
            //获取品牌ID
            paramWrapperDto.brandId = getBrandId(saleGoods.country!!, shop, product)
            setTitle(paramWrapperDto)

            paramWrapperDto.attributes = AttributesMapperUtils.mapToLazadaProductAttributes(platformAttributes)

            val skcMap = newSkcList.associateBy { it.platformColor }

            // 组装Lazada商品参数, 并调用API
            if (saleGoods.platformProductId == null) {
                // Lazada创建商品
                opType = PlatformOperatorTypeEnum.ACTIVE.code
                saleSkuList = saleSkuList.filter { k -> Objects.equals(k.enable, Bool.YES.code) }
                paramWrapperDto.skuList = saleSkuList
                val createProductRequest = buildSingleProduct(paramWrapperDto)
                requestParams = createProductRequest.toJson()
                val createLazadaProductResponse = lazadaApiService.createProduct(countryCode, createProductRequest, accessToken, productId)
                respParams = createLazadaProductResponse.toJson()
                if (!createLazadaProductResponse.isSuccess()) {
                    throw PublishAscBizException("调用lazada创建商品异常:${createLazadaProductResponse.message}\n${createLazadaProductResponse.detail?.toJson()}")
                }
                // 更新saleGoods信息
                saleGoods.platformProductId = createLazadaProductResponse.data?.itemId.toString()
                saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                saleGoods.publishState = ProductPublishStateEnum.ACTIVE.code

                // 更新saleSku信息
                val updateSaleSkuList = mutableListOf<SaleSku>()
                createLazadaProductResponse.data?.skuList?.forEach { platformSku ->
                    val saleSku = saleSkuList.find { it.sellerSku == platformSku.sellerSku }
                    if (saleSku != null) {
                        saleSku.shopSku = platformSku.shopSku
                        saleSku.platformSkuId = platformSku.skuId.toString()
                        updateSaleSkuList.add(saleSku)
                    }
                }
                if (updateSaleSkuList.isNotEmpty()) {
                    saleSkuRepository.updateBatchById(updateSaleSkuList)
                }

                //更新标识
                val updateProduct = Product()
                updateProduct.productId = product.productId
                updateProduct.isUpdate = Bool.NO.code
                product.setIsUpdate(Bool.NO.code)
                productRepository.updateById(updateProduct)

                runAsync(asyncExecutor) { pushToAIDC(paramWrapperDto) }
            } else {
                // Lazada更新商品
                opType = PlatformOperatorTypeEnum.UPDATE_PRODUCT.code
                saleSkuList = saleSkuList.filter { k ->
                    ((StringUtils.isBlank(k.platformSkuId)) && Objects.equals(k.enable, Bool.YES.code))
                            || ((StringUtils.isNotBlank(k.platformSkuId)) && Objects.equals(k.enable, Bool.NO.code))
                            || ((StringUtils.isNotBlank(k.platformSkuId)) && Objects.equals(k.enable, Bool.YES.code))
                }
                //获取lazada接口更新sku和条码相关数据
                val lazadaProductDetail =
                    updateSkuAndBarcodeInfo(false, paramWrapperDto, skcMap, saleSkuList)

                paramWrapperDto.lazadaProductDetail = lazadaProductDetail
                saleSkuList = saleSkuRepository.listByIds(saleSkuList.map { it.saleSkuId }.toSet())
                paramWrapperDto.skuList = saleSkuList

                //构建参数
                val createProductRequest = buildSingleProduct(paramWrapperDto)
                requestParams = createProductRequest.toJson()
                //走更新接口
                val updateLazadaProductResponse = lazadaApiService.updateProduct(countryCode, createProductRequest, accessToken, productId)
                respParams = updateLazadaProductResponse.toJson()
                if (!updateLazadaProductResponse.isSuccess()) {
                    throw PublishAscBizException("调用lazada更新商品异常:${updateLazadaProductResponse.message}\n${updateLazadaProductResponse.detail?.toJson()}")
                }
                saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            }

            saleGoodsRepository.updateById(saleGoods)

            //获取lazada接口更新sku和条码相关数据
            updateSkuAndBarcodeInfo(true, paramWrapperDto, skcMap, saleSkuList)
            // 设置商品状态
            updateProductState(paramWrapperDto)

            //设置更新状态
            productCreateV2Service.updateProductStateByCreateError(product)
            //发布后记录发布时使用的图包版本
            updateImageVersionNumAfterPublish(product.productId!!)
            Thread.sleep(200L)

            //推送上架图片到款式平台
            runAsync(asyncExecutor) { handleSuccessSyncLog(paramWrapperDto, requestParams, respParams, opType) }
            runAsync(asyncExecutor) { pushPublishImage(paramWrapperDto, newSkcList) }
        } catch (e: Exception) {
            runAsync(asyncExecutor) {
                handleFailure(product, saleGoods, requestParams, respParams, opType, e.getRootMessage(), logPrefix, temuSpuId)
            }
            throw PublishGlobalBizException(e)
        }
    }


    fun updateImageVersionNumAfterPublish(productId: Long) {
        val product = productRepository.getById(productId)
        if (product.spuCode != null && product.spuCode.isNotBlank()) {
            val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
            if (imageRepository != null) {
                val updateProduct = Product()
                updateProduct.productId = product.productId
                updateProduct.imageVersionNum = imageRepository.versionNum
                if (product.imagePackageState == ProductImagePackageStateEnum.UPDATED.code) {
                    updateProduct.imagePackageState = ProductImagePackageStateEnum.COMPLETED.code
                }
                productRepository.updateById(updateProduct)
            }
        }

    }

    /**
     * 处理失败的同步日志
     */
    private fun handleFailure(
        product: Product,
        saleGoods: SaleGoods,
        requestParams: String?,
        respParams: String?,
        opType: Int?,
        errorMessage: String?,
        logPrefix: String,
        temuSpuId: Long?,
    ) {
        log.error { "${logPrefix}发布商品到lazada失败1, error=${errorMessage}" }

        if (ObjectUtils.notEqual(saleGoods.platformSyncState, PlatformSyncStateEnum.SUCCESS.code)) {
            saleGoodsRepository.updateById(SaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.platformSyncState = PlatformSyncStateEnum.FAILURE.code
                if (StringUtils.isBlank(saleGoods.platformProductId)) {
                    this.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                }
            })

        }
        addErrorSyncLog(LAZADA.platformName, saleGoods.shopName, saleGoods, errorMessage, requestParams, respParams, opType, temuSpuId)

        productCreateV2Service.updateProductStateByCreateError(product)

        saleGoodsRepository.updateSyncState(saleGoods.saleGoodsId!!, PlatformSyncStateEnum.FAILURE)
    }

    /**
     * 推送图片到款式平台
     */
    private fun pushPublishImage(paramWrapper: ProductSyncParamWrapper, saleSkcList: List<SaleSkc>) {
        val product = paramWrapper.product
        val imageCollection = paramWrapper.requiredImageCollection

        // 非数码印花推数据到款式平台
        // 不处理数码印花类型的商品
        if (!ProductPublishHelper.isLoginNum(product)) {
            //商品详情
            val dataList = mutableListOf<ProductImageChangeStateDto.Data>()
            val pushSkcList = mutableListOf<ProductImageChangeStateDto.Skc>()
            for (saleSkc in saleSkcList) {
                val skcImages = saleSkc.pictures?.split(Url.DELIM)?.map { it.trim() }?.filter { it.isNotBlank() }?.toList()
                val skc = ProductImageChangeStateDto.Skc().apply {
                    this.skcImageList = skcImages
                    this.skc = saleSkc.skc
                }
                pushSkcList.add(skc)
            }

            val data = ProductImageChangeStateDto.Data().apply {
                this.spuCode = product.spuCode
                this.productDetailsImageList = imageCollection.detailImages.map { it.ossImageUrl!! }
                this.skcList = pushSkcList
            }
            dataList.add(data)

            val productImageChangeStateDto = ProductImageChangeStateDto()
            productImageChangeStateDto.dataList = dataList

            val record = MessageRecord().apply {
                this.businessId = product.productId.toString()
                this.exchange = MqConstants.EXCHANGE_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.routingKey = MqConstants.KEY_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE
                this.content = productImageChangeStateDto.toJson()
            }
            val msgId = messageRecordService.preCommitAndGetId(record)
            messageRecordService.commit(msgId, true)
        }
    }

    private fun updateSkuAndBarcodeInfo(
        isfinal: Boolean,
        paramWrapperDto: ProductSyncParamWrapper,
        skcMap: Map<String?, SaleSkc>,
        saleSkus: List<SaleSku>,
    ): LazadaProductItemDetailResp {
        val saleGoods = paramWrapperDto.saleGoods
        val product = paramWrapperDto.product
        //获取详情设置skuID等
        val lazadaProductDetailResp = getLzdProductDetailByCount(saleGoods.platformProductId!!, saleGoods.country!!, paramWrapperDto.shopToken)
        //按颜色分组
        val lazadaSkuMap = lazadaProductDetailResp.data?.skus?.stream()?.collect(Collectors.groupingBy(LazadaProductItemDetailResp.ProductData.Sku::colorFamily))
        val values = paramWrapperDto.skcList
        //按颜色分组遍历每个尺码
        lazadaSkuMap?.forEach { (lazadaColor, skuList) ->
            run {
                val skuListBySkc: List<SaleSku>
                val saleSku1 =
                    saleSkus.firstOrNull { Objects.nonNull(it.platformSkuId) && skuList.first().skuId.toString() == it.platformSkuId }
                var saleSkc: SaleSkc?
                if (Objects.nonNull(saleSku1)) {
                    saleSkc = values?.firstOrNull { Objects.equals(it.productSkcId, saleSku1?.productSkcId) }
                    if (saleSkc == null) {
                        saleSkc =
                            skcMap.values.firstOrNull { it.platformColor.equals(lazadaColor, ignoreCase = true) }
                    }
                } else {
                    log.info { "lazadaColor=${lazadaColor}, skuId=${skuList.first().skuId}" }
                    saleSkc =
                        skcMap.values.firstOrNull { it.platformColor.equals(lazadaColor, ignoreCase = true) }
                }

                if (saleSkc == null) {
                    return@forEach
                }

                //获取某个颜色下的所有尺码
                val finalProductSkc = saleSkc
                skuListBySkc = saleSkus.filter { k -> Objects.equals(finalProductSkc.productSkcId, k.productSkcId) }
                //生成条码
                val barCodeResp = createBarcode(saleGoods, saleSkc, product, skuListBySkc)

                //尺码维度设置skuID
                for (lazadaSku in skuList) {
                    paramWrapperDto.skuId = lazadaSku.skuId.toString()
                    var saleSku =
                        saleSkus.firstOrNull { k -> StringUtils.isNotBlank(k.sellerSku) && k.sellerSku.equals(lazadaSku.sellerSku) }
                    if (saleSku == null) {
                        saleSku = skuListBySkc.firstOrNull { k ->
                            StringUtils.isNotBlank(k.sizeName) && k.sizeName.equals(lazadaSku.size)
                        }
                    }
                    if (saleSku == null) {
                        saleSku = skuListBySkc.firstOrNull { k ->
                            StringUtils.isNotBlank(k.sizeName) && k.sizeName.equals(lazadaSku.sizeUp)
                        }
                    }
                    if (saleSku == null) {
                        saleSku = skuListBySkc.firstOrNull { k ->
                            StringUtils.isNotBlank(k.lzdSizeName) && k.lzdSizeName.equals(lazadaSku.sizeUp)
                        }
                    }
                    if (saleSku == null) {
                        saleSku = skuListBySkc.firstOrNull { k ->
                            StringUtils.isNotBlank(k.lzdSizeName) && k.lzdSizeName.equals(lazadaSku.size)
                        }
                    }
                    if (saleSku != null) {
                        val updateSku = SaleSku()
                        updateSku.saleSkuId = saleSku.saleSkuId
                        updateSku.platformSkuId = lazadaSku.skuId.toString()
                        if (isfinal) {
                            //设置lazada sku相关值
                            if (LazadaSkuStateEnum.ACTIVE.code == lazadaSku.status) {
                                updateSku.publishState = ProductPublishStateEnum.ACTIVE.code
                            }
                            if (LazadaSkuStateEnum.IN_ACTIVE.code == lazadaSku.status) {
                                updateSku.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                            }
                        }
                        updateSku.platformSkuId = lazadaSku.skuId.toString()
                        updateSku.platformProductId = lazadaProductDetailResp.data?.itemId
                        updateSku.sellerSku = lazadaSku.sellerSku
                        updateSku.shopSku = lazadaSku.shopSku
                        //设置条码关联skuID信息
                        if (CollectionUtils.isNotEmpty(barCodeResp)) {
                            val codeResp = barCodeResp.firstOrNull { k -> k.sizeName.equals(lazadaSku.size) }
                            if (codeResp != null) {
                                updateSku.barcode = codeResp.barcode
                                if (StringUtils.isBlank(codeResp.shopSku)
                                    || StringUtils.isBlank(codeResp.skuId)
                                    || (StringUtils.isNotBlank(codeResp.sellerSku) && !codeResp.sellerSku.equals(
                                        lazadaSku.sellerSku
                                    ))
                                    || StringUtils.isBlank(codeResp.sellerSku)
                                ) {
                                    val productBarcode = ProductBarcode().apply {
                                        this.productBarcodeId = codeResp.productBarcodeId
                                        this.skuId = lazadaSku.skuId.toString()
                                        this.shopSku = lazadaSku.shopSku
                                        this.sellerSku = lazadaSku.sellerSku
                                    }
                                    barCodeService.updateById(productBarcode)
                                }
                            }
                        }
                        saleSkuRepository.updateById(updateSku)

                    }
                }
            }
        }
        return lazadaProductDetailResp
    }

    private fun createBarcode(saleGoods: SaleGoods, saleSkc: SaleSkc, finalProduct: Product, skuListBySkc: List<SaleSku>): List<BarCodeResp> {
        if (saleSkc.combo == Bool.YES.code) {
            return listOf()
        }
        //生成条码
        val createBarCodeReq = CreateBarCodeReq().apply {
            this.spuCode = saleGoods.spuCode
            this.skcCode = saleSkc.skc
            this.color = saleSkc.color
            this.supplyMode = finalProduct.supplyMode
            this.mainImgUrl = finalProduct.mainImgUrl
            this.sourceGroupCode = finalProduct.sizeGroupCode
            this.groupName = finalProduct.sizeGroupName
            this.categoryCode = finalProduct.categoryCode
            this.mainImgUrl = finalProduct.mainImgUrl
            this.sizeValues = skuListBySkc.mapNotNull { it.sizeName }
        }

        return barCodeService.createBarcodeByCreateProduct(createBarCodeReq)
    }

    private fun getLzdProductDetailByCount(itemId: String, country: String, token: String): LazadaProductItemDetailResp {
        val productDetail = lazadaApiService.getProductDetailByItemId(country, itemId, token)
        if (productDetail?.data != null && Objects.equals(productDetail.code, "0")) {
            return productDetail
        } else {
            throw PublishAscBizException("lazada查询商品详情异常, ${productDetail.toJson()}")
        }
    }

    private fun setTitle(paramWrapper: ProductSyncParamWrapper) {
        // 原始标题
        val rawTitle = paramWrapper.title
        val country = paramWrapper.country.trim()
        val shop = paramWrapper.shop

        val finalTitle = ProductPublishHelper.buildTitle(shop, rawTitle)

        paramWrapper.apply {
            saleGoodsSourceTitle = rawTitle
            title = finalTitle
        }
        log.info { "${paramWrapper.logPrefix} 设置产品标题完成 → [$finalTitle] (国家: $country)" }
    }

    private fun getAttributeValue(keyName: String, platformAttributes: List<PublishPlatformAttributeDTO>): String {
        if (CollectionUtils.isNotEmpty(platformAttributes)) {
            return platformAttributes
                .filter { k -> k.platformAttributeKeyName.equals(keyName) }
                .map { k -> k.platformAttributeValue }
                .firstOrNull()
                ?: ""
        }
        return ""
    }

    private fun getBrandId(country: String, shop: Shop, product: Product): Int? {
        var shopBrandName = shop.brandName
        if (StringUtils.isBlank(shopBrandName)) {
            shopBrandName = product.brandName
        }
        if (StringUtils.isBlank(shopBrandName)) {
            return null
        }
        val lazadaBrand = lazadaBrandRepository.getOne(
            KtQueryWrapper(LazadaBrand::class.java)
                .eq(LazadaBrand::country, country)
                .eq(LazadaBrand::nameEn, shopBrandName)
        )
        if (lazadaBrand != null) {
            return lazadaBrand.brandId
        }
        return null
    }

    private fun getPicture(paramWrapperDto: ProductSyncParamWrapper): ProductPicture {
        val productId = paramWrapperDto.product.productId!!
        val spuCode = paramWrapperDto.spu
        val imageRepository = paramWrapperDto.requiredImageRepository

        val picture = productPictureRepository.getOneByProductId(productId)
        if (picture == null) {
            val productPicture = ProductPicture()
            productPicture.productId = productId
            productPicture.spuCode = spuCode
            productPicture.sourceImageUrl = imageRepository.mainUrl
            productPictureRepository.save(productPicture)
            return productPicture
        } else {
            picture.sourceImageUrl = imageRepository.imageUrls
            productPictureRepository.updateById(picture)
        }
        return picture
    }

    private fun validateImageData(imageRepository: ImageRepository) {
        val imgList = imageRepository.imageUrls!!.parseJsonList(ImageAniVo::class.java)
        val imgOrgList = imgList.mapNotNull { it.orgImgName }
        val imageNoList = ImageUtils.extractTypeCodesFromNames(imgOrgList)
        if (CollectionUtils.isEmpty(imageNoList)) {
            throw PublishGlobalBizException("无上传图片资料!")
        }
        val loseImage = ImageUtils.missingImageTypeCodes(imageNoList)
        if (StringUtils.isNotBlank(loseImage)) {
            throw PublishGlobalBizException("缺少" + loseImage + "图片资源")
        }
    }

    private fun uploadImgToLazada(
        paramWrapperDto: ProductSyncParamWrapper,
        skcCodeList: Set<String>,
    ): ImageCollectionDTO {
        val saleGoods = paramWrapperDto.saleGoods
        val lazadaShop = paramWrapperDto.shop
        val imageRepository = paramWrapperDto.requiredImageRepository
        val logPrefix = paramWrapperDto.logPrefix

        val picture = paramWrapperDto.requiredPicture

        log.info { "${logPrefix}上传图片到lazada，shopId: ${lazadaShop.shopId}, 图片数量: ${skcCodeList.size}" }

        val lazadaImgList = productPublishLazadaHelper.checkImageAndUpdateToLazada(
            paramWrapperDto.country,
            imageRepository,
            paramWrapperDto.shopToken,
            picture.flowerUrl
        )

        if (CollectionUtils.isEmpty(lazadaImgList)) {
            throw PublishGlobalBizException("图库上传至lazada失败（请重试），请检查图片大小（不超过3m）和图片尺寸")
        }

        return imageCollectionHelper.updateProductImagesAndBuildCollection(lazadaImgList, imageRepository.spuCode!!, picture, skcCodeList)
    }

    private fun buildSingleProduct(paramWrapperDto: ProductSyncParamWrapper): CreateProductRequest {
        val createProductRequest = CreateProductRequest()
        val request = CreateProductRequest.Request()
        val product = CreateProductRequest.Product()
        val saleGoods = paramWrapperDto.saleGoods
        val imageCollection = paramWrapperDto.requiredImageCollection

        // 若skuList里存在skuId为空的数据, 则赋值associatedSku(新增sku才需要赋值)
        val newSku = paramWrapperDto.skuList!!.any { it.platformSkuId == null }
        if (newSku) {
            product.associatedSku = paramWrapperDto.skuId
        }

        // 设置 PrimaryCategory
        product.primaryCategory = paramWrapperDto.platformCategoryId
        // 设置 Images
        val images = CreateProductRequest.Images()
        images.image = imageCollection.limitedMainImages().map { it.ossImageUrl!! }
        paramWrapperDto.mainUrlList = imageCollection.limitedMainImages().map { it.ossImageUrl!! }
        product.images = images

        val attributes = paramWrapperDto.attributes
        // 设置 Attributes
        if (StringUtils.isNotBlank(attributes?.blouseSleeveStyle) && StringUtils.isBlank(attributes?.sleeveStyle)) {
            attributes?.sleeveStyle = attributes?.blouseSleeveStyle
        }
        if (StringUtils.isNotBlank(attributes?.sleeveStyle) && StringUtils.isBlank(attributes?.blouseSleeveStyle)) {
            attributes?.blouseSleeveStyle = attributes?.sleeveStyle
        }
        if (saleGoods.platformProductId != null) {
            product.itemId = saleGoods.platformProductId
        }
        val propCascade = CreateProductRequest.PropCascade()
        attributes?.propCascade = propCascade
        //设置标题(有标题存用数据库，没有则用翻译的)
        if (StringUtils.isBlank(saleGoods.productTitle)) {
            attributes?.name = paramWrapperDto.title
        } else {
            attributes?.name = saleGoods.productTitle
        }
        attributes?.name = paramWrapperDto.title
        attributes?.disableAttributeAutoFill = false
        //设置尺码模板ID
        val sizeTemplate = sizeTemplateRepository.getOne(
            KtQueryWrapper(SizeTemplate::class.java)
                .eq(SizeTemplate::shopId, paramWrapperDto.shop.platformSellerId)
                .eq(SizeTemplate::country, paramWrapperDto.country)
        )
        if (Objects.nonNull(sizeTemplate)) {
            attributes?.sizeChart = sizeTemplate.templateId
        }

        //商品详情
        val details = StringBuilder()
        imageCollection.detailImages.map { it.ossImageUrl!! }
            .filter { k -> StringUtils.isNotBlank(k) }
            .forEach { k ->
                details.append("<img src=\"").append(k).append("\" /> <br>")
            }
        attributes?.description = details.toString()

        if (paramWrapperDto.brandId != null) {
            attributes?.brandId = paramWrapperDto.brandId
        } else {
            attributes?.brand = "No Brand"
        }
        product.attributes = attributes

        // 设置 Skus
        val skus = CreateProductRequest.Skus()
        val skuDatas = mutableListOf<CreateProductRequest.Sku>()

        //普通模式设置
        setSkusIfSimple(paramWrapperDto, skuDatas)
        skus.sku = skuDatas
        product.skus = skus

        // 设置销售属性及对应挂图关系
        setVariations(product, skuDatas, paramWrapperDto.logPrefix)

        // 将 Product 设置到 Request 中
        request.product = product

        // 将 Request 设置到 CreateProductRequest 中
        createProductRequest.request = request
        return createProductRequest
    }

    private fun setSkusIfSimple(paramWrapperDto: ProductSyncParamWrapper, skuDatas: MutableList<CreateProductRequest.Sku>) {
        val saleGoods = paramWrapperDto.saleGoods
        val product = paramWrapperDto.product
        val skcList = paramWrapperDto.requiredSkcList
        val imageCollection = paramWrapperDto.requiredImageCollection

        val barCodeList = skcList.map { k ->
            val barcodeData = BarcodeQuery.BarcodeData()
            barcodeData.skcCode = k.skc
            barcodeData.sizeGroupCode = product.sizeGroupCode
            barcodeData
        }
        val barcodeQuery = BarcodeQuery()
        barcodeQuery.dataList = barCodeList
        val barCodeListResps = barCodeService.barcodeList(barcodeQuery).toImmutableList()

        for (saleSkc in skcList) {
            val skuList = paramWrapperDto.skuList?.filter { k -> k.stockQuantity != null && Objects.equals(k.saleSkcId, saleSkc.saleSkcId) }
            if (CollectionUtils.isEmpty(skuList)) {
                continue
            }
            val skcImag = mutableListOf<String>()
            for (saleSku in skuList!!) {
                val sku = CreateProductRequest.Sku()
                val sizeName = saleSku.sizeName?.trim()
                val saleProp = CreateProductRequest.SaleProp()
                saleProp.colorFamily = saleSkc.platformColor
                saleProp.size = sizeName
                sku.saleProp = saleProp
                if (saleSku.platformSkuId.isNotBlank()) {
                    sku.skuId = saleSku.platformSkuId
                }
                setBarcode(paramWrapperDto, saleSkc, saleSku, barCodeListResps)
                //  查询sku编码规则
                if (StringUtils.isNotBlank(saleSku.sellerSku)) {
                    sku.sellerSku = saleSku.sellerSku
                } else {
                    val skuCodeGenerateReq = SkuCodeGenerateReq.builder().channelId(ALIBABA.channelId)
                        .platformId(saleGoods.platformId!!)
                        .supplyMode(product.supplyMode!!)
                        .spu(product.spuCode!!)
                        .size(saleSku.sizeName)
                        .color1(saleSkc.colorCode)
                        .color2(saleSkc.colorAbbrCode)
                        .barcode(saleSku.barcode).build()
                    // String sellerSku = paramWrapperDto.getProduct().spuCode+"-"+productSkc.getSkc()+"-"+saleSku.getSizeName()
                    val sellerSku = skuCodeGenerateService.generateSkuCode(skuCodeGenerateReq)
                    if (StringUtils.isBlank(sellerSku)) {
                        throw PublishAscBizException("查询不到上架sku编码，请确认配置规则是否正确")
                    }
                    sku.sellerSku = sellerSku
                }
                // 更新场景: 按照本地状态
                if (Objects.equals(saleSku.publishState, ProductPublishStateEnum.ACTIVE.code)) {
                    sku.status = LazadaSkuStateEnum.ACTIVE.code
                }
                if (Objects.equals(saleSku.publishState, ProductPublishStateEnum.IN_ACTIVE.code)) {
                    sku.status = LazadaSkuStateEnum.IN_ACTIVE.code
                }
                if (Objects.equals(saleSku.publishState, ProductPublishStateEnum.DELETED.code)) {
                    sku.status = LazadaSkuStateEnum.DELETED.code
                }
                sku.specialPrice = saleSku.salePrice?.toEngineeringString()
                sku.price = saleSku.retailPrice?.toEngineeringString()
                sku.quantity = saleSku.stockQuantity.toString()
                sku.packageHeight = saleGoods.packageDimensionsHeight
                sku.packageLength = saleGoods.packageDimensionsLength
                sku.packageWidth = saleGoods.packageDimensionsWidth
                sku.packageWeight = saleGoods.packageWeight
                //获取颜色图片URL，没有则用主图
                val skuImages = CreateProductRequest.Images().apply {
                    image = saleSkc.pictures?.split(Url.DELIM)?.map { it.trim() }?.filter { it.isNotBlank() }?.toList()
                }
                sku.images = skuImages
                sku.delayDeliveryDays = saleSku.delayDeliveryDays?.toString()
                skuImages.image?.let { skcImag.addAll(it) }
                skuDatas.add(sku)
            }
        }
    }

    private fun setBarcode(paramWrapperDto: ProductSyncParamWrapper, saleSkc: SaleSkc, saleSku: SaleSku, barCodeListResps: List<BarCodeListResp>) {
        if (StringUtils.isNotBlank(saleSku.barcode)) return
        val product = paramWrapperDto.product
        if (CollectionUtils.isNotEmpty(barCodeListResps)) {
            barCodeListResps.firstOrNull { k ->
                Objects.equals(k.spuCode, product.spuCode)
                        && Objects.equals(saleSkc.skc, k.skc)
                        && Objects.equals(saleSku.sizeName, k.sizeName)
            }?.let { barCodeListResp ->
                saleSku.barcode = barCodeListResp.barcode
            }
        }
    }

    private fun addErrorSyncLog(platformName: String, shop: String?, saleGoods: SaleGoods, error: String?, platformRequestParams: String?, resp: String?, opType: Int?, temuSpuId: Long?) {
        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.templateSpuId = temuSpuId
            this.opType = opType
            this.errorMsg = error
            this.platformName = platformName
            this.shopName = shop
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
            this.errorMsg = "站点:" + saleGoods.country + "," + error
        }
        productSyncLogRepository.save(productSyncLog)
    }

    private fun updateProductState(paramWrapperDto: ProductSyncParamWrapper) {
        val product = paramWrapperDto.product

        val updateProduct = Product()
        updateProduct.productId = product.productId
        if (Objects.isNull(product.getIsSyncPlatform()) || product.getIsSyncPlatform()?.equals(Bool.NO.code) == true) {
            updateProduct.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
        }
        val judeSaleGoodsList = saleGoodsRepository.ktQuery()
            .select(SaleGoods::saleGoodsId, SaleGoods::platformProductId, SaleGoods::publishState)
            .eq(SaleGoods::productId, product.productId).list()

        if (CollectionUtils.isNotEmpty(judeSaleGoodsList)) {
            val publishCount = judeSaleGoodsList.count { k -> StringUtils.isNotBlank(k.platformProductId) && Objects.equals(k.publishState, ProductPublishStateEnum.ACTIVE.code) }
            //有一个站点上架就认为上架成功
            if (publishCount > 0) {
                updateProduct.publishState = ProductPublishStateEnum.ACTIVE.code
                if (product.getIsSyncPlatform() == null || product.getIsSyncPlatform()?.equals(Bool.NO.code) == true) {
                    updateProduct.setIsSyncPlatform(Bool.YES.code)
                }
            }
        }
        if (Objects.isNull(product.publishTime)) {
            val currentUser = CurrentUserHolder.get()
            updateProduct.publishUserId = currentUser.id
            updateProduct.publishUserName = currentUser.name
            updateProduct.publishTime = LocalDateTime.now()
        }
        updateProduct.mainImgUrl = paramWrapperDto.mainUrlList?.first()
        updateProduct.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
        productRepository.updateById(updateProduct)
    }

    /**
     * 校验SKU资料
     */
    private fun validateSaleSku(saleSkcList: List<SaleSkc>, saleSkuList: MutableList<SaleSku>) {
        // 收集所有错误信息
        val errorMessages = mutableListOf<String>()

        // 检查SKC颜色是否有重复
        saleSkcList.groupBy { it.platformColor }
            .filter { (platformColor, skcs) -> platformColor != null && skcs.size > 1 }
            .forEach { (platformColor, _) ->
                errorMessages.add("平台颜色 [$platformColor] 在多个SKC中重复使用")
            }

        // 检查SKC颜色是否为空
        saleSkcList.filter { it.platformColor.isNullOrBlank() }
            .forEach { skc ->
                errorMessages.add("SKC ID [${skc.saleSkcId}] 未设置平台颜色")
            }

        // 创建SKC映射，用于后续验证
        val saleSkcMap = saleSkcList.associateBy { it.saleSkcId }

        // 按SKC分组SKU，用于校验尺码重复
        val skusBySkc = saleSkuList.groupBy { it.saleSkcId }

        // 检查每个颜色组中尺码是否重复
        skusBySkc.forEach { (skcId, skusWithSameColor) ->
            val colorName = saleSkcMap[skcId]?.platformColor!!
            val skc = saleSkcMap[skcId]?.skc

            // 检查尺码重复
            skusWithSameColor.groupBy { it.sizeName }
                .filter { (size, skus) -> size != null && skus.size > 1 }
                .forEach { (size, _) ->
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的尺码 [$size] 重复使用")
                }

            // 检查每个SKU
            skusWithSameColor.forEach { sku ->
                val sizeName = sku.sizeName

                // 检查尺码是否为空
                if (sizeName.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 的SKU缺少尺码名称")
                }
                if (sku.salePrice == null) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 未设置销售价格")
                } else if (sku.salePrice!! <= BigDecimal.ZERO) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 销售价格必须大于 0")
                }
                if (sku.retailPrice == null) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 未设置划线价格")
                } else if (sku.retailPrice!! <= BigDecimal.ZERO) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 划线价格必须大于 0")
                }
                if (sku.stockQuantity?.let { it <= 0 } == true) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置库存数量")
                }
                if (sku.sellerSku.isNullOrBlank()) {
                    errorMessages.add("SKC [$skc] 颜色 [$colorName] 尺码 [$sizeName] 无设置sellerSku")
                }
            }
        }

        // 如果有错误，抛出异常
        if (errorMessages.isNotEmpty()) {
            throw PublishAscBizException(errorMessages.joinToString("\n"))
        }
    }

    /**
     * 校验SKC图片
     */
    private fun validateSkcPictures(skcList: List<SaleSkc>) {
        val missingPictureColorCodes = skcList.filter { it.pictures.isNullOrBlank() }.map { it.colorCode }
        if (missingPictureColorCodes.isNotEmpty()) {
            val errorMessages = missingPictureColorCodes.joinToString("\n") { "颜色 [$it] 缺少图片" }
            requireBusiness(false) { errorMessages }
        }
    }

    /**
     * 推送到AIDC
     */
    private fun pushToAIDC(paramWrapper: ProductSyncParamWrapper) {
        val product = paramWrapper.product
        val saleGoods = paramWrapper.saleGoods
        if (product.inspiraSourceId != null) {
            val onlineNoticeReq = ProductOnlineNoticeReq().apply {
                this.inspireSourceId = product.inspiraSourceId!!
                this.onlineSaleItemId = saleGoods.platformProductId!!.toLong()
            }
            inspirationClientExternal.productOnlineNotice(onlineNoticeReq)
        }
    }

    /**
     * 处理成功的同步日志
     */
    private fun handleSuccessSyncLog(
        paramWrapper: ProductSyncParamWrapper,
        requestParams: String?,
        respParams: String,
        opType: Int?,
    ) {
        val saleGoods = paramWrapper.saleGoods

        //记录操作日志
        saveSyncLog(saleGoods, LAZADA.platformName, requestParams, respParams, opType)

        // 删除错误日志
        val errorLogList = KtQueryWrapper(ProductSyncLog::class.java)
            .eq(ProductSyncLog::saleGoodId, saleGoods.saleGoodsId)
            .eq(ProductSyncLog::logType, Bool.NO.code)
            .`in`(
                ProductSyncLog::opType,
                listOf(PlatformOperatorTypeEnum.ACTIVE.code, PlatformOperatorTypeEnum.UPDATE_PRODUCT.code)
            )

        productSyncLogRepository.remove(errorLogList)
    }

    /**
     * 添加成功同步日志
     */
    private fun saveSyncLog(saleGoods: SaleGoods, platformName: String, platformRequestParams: String?, resp: String, opType: Int?) {
        val shopName = saleGoods.shopName

        val productSyncLog = ProductSyncLog().apply {
            this.productId = saleGoods.productId
            this.saleGoodId = saleGoods.saleGoodsId
            this.opType = opType
            this.logType = Bool.YES.code
            this.platformName = platformName
            this.shopName = shopName
            this.platformRequestParams = platformRequestParams
            this.platformHttpResp = resp
        }
        productSyncLogRepository.save(productSyncLog)
    }

    /**
     * 配置产品变体信息及图片关联
     *
     * @param product 产品对象
     * @param skuDatas SKU数据列表
     */
    private fun setVariations(product: CreateProductRequest.Product, skuDatas: List<CreateProductRequest.Sku>, logPrefix: String) {
        if (skuDatas.isEmpty()) {
            log.info { "${logPrefix}无SKU数据，跳过变体配置" }
            return
        }

        // 获取所有唯一的颜色和尺码
        val colorFamilies = skuDatas
            .mapNotNull { it.saleProp?.colorFamily }
            .filter { it.isNotBlank() }
            .distinct()

        val sizes = skuDatas
            .mapNotNull { it.saleProp?.size }
            .filter { it.isNotBlank() }
            .distinct()

        log.info { "${logPrefix}提取变体信息 - 颜色数量: ${colorFamilies.size}, 尺码数量: ${sizes.size}" }

        // 只有存在变体选项时才创建variation对象
        if (colorFamilies.isEmpty() && sizes.isEmpty()) {
            log.info { "${logPrefix}无变体属性，跳过variation配置" }
            return
        }

        val variation = CreateProductRequest.Variation()

        // 配置颜色变体
        if (colorFamilies.isNotEmpty()) {
            variation.variation1 =
                CreateProductRequest.VariationDetail.of(name = "color_family", hasImage = true, options = colorFamilies)

            log.info { "${logPrefix}设置颜色变体: ${colorFamilies.joinToString(", ")}" }
        }

        // 配置尺码变体
        if (sizes.isNotEmpty()) {
            // 如果已经设置了颜色变体，尺码作为variation2
            // 否则尺码作为variation1
            if (colorFamilies.isNotEmpty()) {
                variation.variation2 =
                    CreateProductRequest.VariationDetail.of(name = "size", hasImage = false, options = sizes)

            } else {
                variation.variation1 =
                    CreateProductRequest.VariationDetail.of(name = "size", hasImage = false, options = sizes)
            }
            log.info { "${logPrefix}设置尺码变体: ${sizes.joinToString(", ")}" }
        }

        // 设置产品变体
        product.variation = variation
    }

    /**
     * 更新Lazada库存和价格
     */
    fun updateStockAndPrice(saleGoodsIds: List<Long>) {
        // 组装MQ数据, 更新Lazada库存/价格
        val lazadaDataList: MutableList<LazadaPriceMqDto> = mutableListOf()

        val saleGoodsList = saleGoodsRepository.listByIds(saleGoodsIds)

        val saleSkuList = saleSkuRepository.getBySaleGoodsIds(saleGoodsIds)
        saleGoodsList.forEach { saleGoods ->
            val shop = shopRepository.getById(saleGoods.shopId!!)
            saleSkuList
                .filter { sku -> sku.platformSkuId.isNotNull() }
                .filter { sku -> sku.saleGoodsId == saleGoods.saleGoodsId }
                .forEach { sku ->
                    val dto = LazadaPriceMqDto().apply {
                        this.platformId = saleGoods.platformId
                        this.shopId = saleGoods.shopId
                        this.shopName = saleGoods.shopName
                        this.productId = saleGoods.productId
                        this.saleProductId = saleGoods.saleGoodsId
                        this.saleSkuId = sku.saleSkuId
                        this.lazadaCountry = saleGoods.country
                        this.lazadaShopToken = shop.token
                        this.lazadaItemId = saleGoods.platformProductId
                        this.lazadaSkuId = sku.platformSkuId
                        this.lazadaSellerSku = sku.sellerSku
                        this.lazadaPrice = sku.retailPrice?.toString()
                        this.lazadaSalePrice = sku.salePrice?.toString()
                        this.lazadaStockQuantity = sku.stockQuantity?.toString()
                    }
                    lazadaDataList.add(dto)
                }
        }
        if (lazadaDataList.isNotEmpty()) {
            val msgId = getId()
            val messageRecord = MessageRecord()
            messageRecord.id = msgId
            messageRecord.businessId = msgId.toString()
            messageRecord.exchange = MqConstants.EXCHANGE_POP_LAZADA_PRICE_UPDATE
            messageRecord.routingKey = MqConstants.KEY_POP_LAZADA_PRICE_UPDATE
            messageRecord.content = lazadaDataList.toJson()

            messageRecordService.preCommit(messageRecord)
            messageRecordService.commit(msgId, true)
        }
    }
}

/**
 * Lazada 平台商品创建或更新的上下文信息。
 * 此类用于封装向Lazada平台创建或更新商品时所需的所有相关数据，
 * 包括商品基本信息、SKU变体信息、类目、属性、图片等。
 */
private data class ProductSyncParamWrapper(
    /** 商品主信息（Product 表） */
    val product: Product,
    /** 销售商品信息（含平台商品 ID、标题等） */
    val saleGoods: SaleGoods,
    /** 店铺信息（含 token、sellerId 等） */
    val shop: Shop,
    /** Lazada 平台类目 ID */
    val platformCategoryId: String,
    /** 国家代码，如 SG/MY/PH */
    val country: String,
    /** 日志前缀（用于日志定位） */
    val logPrefix: String = "",
    /** 品牌 ID，若为 null 表示使用 “No Brand” */
    var brandId: Int? = null,
    /** 商品标题（最终用于平台展示） */
    var title: String,
    /** 商品源标题（原始未处理标题） */
    var saleGoodsSourceTitle: String? = null,
    /** Lazada 平台商品详情（回查信息） */
    var lazadaProductDetail: LazadaProductItemDetailResp? = null,
    /** 商品属性（平台格式） */
    var attributes: LazadaProductAttributes? = null,
    /** SKU 列表（后续延迟注入） */
    var skuList: List<SaleSku>? = null,
    /** SKC 列表（颜色维度，后续注入） */
    var skcList: List<SaleSkc>? = null,
    /** 图片集合（主图 + 详情图） */
    var imageCollection: ImageCollectionDTO? = null,
    /** 主图 URL 集合，用于提交给平台 */
    var mainUrlList: List<String>? = null,
    /** Lazada 构建参数阶段使用的内部 SKU ID（仅创建场景用） */
    var skuId: String? = null,
    /** 商品的图片资源（ImageRepository 表），包括主图、详情图等 */
    var imageRepository: ImageRepository? = null,
    /** 当前商品对应的图片记录（ProductPicture 表），用于上传与回写（延迟设置） */
    var picture: ProductPicture? = null,
) {
    /** SPU 编码（便于统一引用） */
    val spu get() = product.spuCode!!

    /** 店铺 token，缺失即报错 */
    val shopToken get() = shop.token ?: throw BusinessException("$logPrefix 店铺未配置 token")

    /** 强制获取 imageCollection */
    val requiredImageCollection get() = imageCollection ?: throw BusinessException("$logPrefix imageCollection 未初始化")

    /** 强制获取 imageRepository，如未初始化则抛出异常 */
    val requiredImageRepository: ImageRepository
        get() = imageRepository ?: throw BusinessException("$logPrefix imageRepository 未初始化")

    /** 强制获取 picture，如未初始化则抛出异常 */
    val requiredPicture: ProductPicture
        get() = picture ?: throw BusinessException("$logPrefix picture 未初始化")
    val requiredSkcList: List<SaleSkc>
        get() = skcList ?: throw BusinessException("$logPrefix skcList 未初始化")
}