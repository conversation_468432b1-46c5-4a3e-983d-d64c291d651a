package tech.tiangong.pop.component.importfile.center

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.read.listener.PageReadListener
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.dao.entity.ImportProductRecord
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.dto.CategoryPackageConfigVolumeDto
import tech.tiangong.pop.dao.entity.dto.ProductPackageInfoDto
import tech.tiangong.pop.dao.repository.ImportProductRecordRepository
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dto.export.ProductCenterLogisticsPackageExportDto
import tech.tiangong.pop.enums.ImportDataTypeEnum
import tech.tiangong.pop.enums.ImportSourceEnum
import tech.tiangong.pop.enums.OuterPackingShapeEnum
import tech.tiangong.pop.enums.OuterPackingTypeEnum


/**
 * 商品中心-物流包装导入组件
 * 负责导入商品中心的物流包装数据
 */
@Component
@Slf4j
class ProductCenterLogisticsPackageImportComponent(
    private val importProductRecordRepository: ImportProductRecordRepository,
    private val productRepository: ProductRepository,
) {

    /**
     * 导入
     */
    fun importExcel(file: MultipartFile) {

        val importDataList = mutableListOf<ProductCenterLogisticsPackageExportDto>()

        // 读取Excel并映射到UserData实体类
        EasyExcel.read(
            file.inputStream,
            ProductCenterLogisticsPackageExportDto::class.java,
            PageReadListener { dataList ->
                log.info { "读取到${dataList.size}条数据" }
                dataList.forEach { user ->
                    importDataList.add(user)
                }
            }
        ).sheet() // 读取第一个sheet
            .doRead()

        val importList = importDataList
            .asSequence()
            .filter { it.spuCode.isNotBlank() }
            .filter { it.category.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入数据为空")
        }

        // 创建导入任务
        val list = importList.map {
            ImportProductRecord().apply {
                this.spuCode = it.spuCode
                this.importDataType = ImportDataTypeEnum.LOGISTICS_PACKAGE.code
                this.importSource = ImportSourceEnum.PRODUCT_CENTER.code
                this.importData = it.toJson()
            }
        }
        importProductRecordRepository.saveBatch(list, 100)
    }

    /**
     * 执行导入逻辑
     */
    fun doImport(importData: String) {
        if (importData.isBlank()) {
            throw BaseBizException("解析json失败: $importData")
        }
        val data = importData.parseJson<ProductCenterLogisticsPackageExportDto>()
        val productList = productRepository.listBySpuCodes(listOf(data.spuCode!!))
        if (productList.isEmpty()) {
            return
        }

        productList.forEach { product ->
            val packageInfo = product.packageInfo?.parseJson<ProductPackageInfoDto>() ?: ProductPackageInfoDto()
            if (data.productWeight.isNotBlank() && data.productWeight != "-") {
                packageInfo.weight = data.productWeight?.toBigDecimal()
            }
            if (data.wrapperType.isNotBlank() && data.wrapperType != "-") {
                packageInfo.outerPackingShape = data.wrapperType?.let { OuterPackingShapeEnum.getByDesc(it)?.code }
            }
            if (data.wrapperShape.isNotBlank() && data.wrapperShape != "-") {
                packageInfo.outerPackingType = data.wrapperShape?.let { OuterPackingTypeEnum.getByDesc(it)?.code }
            }

            var isUpdateVolume = false
            val volumeInfo = packageInfo.packingVolume ?: CategoryPackageConfigVolumeDto()
            if (data.longestEdge.isNotBlank() && data.longestEdge != "-") {
                volumeInfo.maxSideLength = data.longestEdge!!.toBigDecimal()
                isUpdateVolume = true
            }
            if (data.secondLongestEdge.isNotBlank() && data.secondLongestEdge != "-") {
                volumeInfo.secondSideLength = data.secondLongestEdge!!.toBigDecimal()
                isUpdateVolume = true
            }
            if (data.shortestEdge.isNotBlank() && data.shortestEdge != "-") {
                volumeInfo.minSideLength = data.shortestEdge!!.toBigDecimal()
                isUpdateVolume = true
            }
            if (isUpdateVolume) {
                packageInfo.packingVolume = volumeInfo
            }

            // 更新
            productRepository.updateById(Product().apply {
                this.productId = product.productId
                this.packageInfo = packageInfo.toJson()
            })
        }
    }
}
