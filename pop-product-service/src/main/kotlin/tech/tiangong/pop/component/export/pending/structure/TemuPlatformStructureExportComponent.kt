package tech.tiangong.pop.component.export.pending.structure

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.temu.enums.TemuCurrencyTypeEnum
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.component.export.DownloadTaskInterface
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductTemplateTemuSpu
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.product.TemuPlatformProductExportDTO
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.AttributePairReq
import tech.tiangong.pop.req.product.AutoCalPriceReq
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.image.ImageAniVo
import tech.tiangong.pop.resp.product.AutoCalPriceResp
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.reflect.KClass
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.javaField

/**
 * 导出平台商品结构-待上架
 */
@Component
@Slf4j
class TemuPlatformStructureExportComponent(
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val downloadTaskService: DownloadTaskService,
    private val productRepository: ProductRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val productPriceManagementService: ProductPriceManagementService,
    private val temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository,
    private val productAttributesRepository: ProductAttributesRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
) : DownloadTaskInterface() {

    private val platformEnum = PlatformEnum.TEMU

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductPendingTemuPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productTemplateTemuSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出待上架TEMU结构商品_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PENDING_LISTING_TEMU_PLATFORM_STRUCTURE_TASK,
                req.toJson()
            );
            log.info { "导出平台商品结构-Temu待上架-创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出平台商品结构-Temu待上架-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("导出平台商品结构-Temu待上架失败任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 导出数据逻辑
     */
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<ProductPendingTemuPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 分页所有数据
        req.pageNum = 1
        req.pageSize = 500
        // key=sheet(品类), value=sheetData
        val sheetDataMap = mutableMapOf<String, MutableList<TemuPlatformProductExportDTO>>()

        while (true) {
            val pageVo = productTemplateTemuSpuRepository.pendingPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
            if (pageVo.records.isEmpty()) {
                break
            }
            var productIdMap: Map<Long?, Product?> = mutableMapOf()
            val productIds = pageVo.records.mapNotNull { it.productId }.distinct()
            if (productIds.isNotEmpty()) {
                productIdMap = productRepository.listByIds(productIds).associateBy { it.productId }
            }
            pageVo.records.forEach { vo ->
                val dataList = exportProductByTemu(vo, productIdMap)
                if (dataList.isNotEmpty()) {
                    dataList.forEach { data ->
                        sheetDataMap.getOrPut(data.categoryName ?: "") { mutableListOf() }.add(data)
                    }
                }
            }
            req.pageNum++
        }
        if (sheetDataMap.isEmpty()) {
            throw BusinessException("导出数据为空")
        }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {

                // 步骤 1：通过反射动态获取固定头（排除 dynamicColumns 字段）
                val fixedHeaders = getFixedHeaders(
                    dtoClass = TemuPlatformProductExportDTO::class,
                    excludeFields = listOf("dynamicColumns")
                )

                EasyExcel.write(excelFile, TemuPlatformProductExportDTO::class.java)
                    .build()
                    .use { excelWriter ->
                        sheetDataMap.entries.toList().forEachIndexed { index, entry ->
                            val sheetName = entry.key
                            val dataList = entry.value

                            // 步骤 2：收集动态列的 key（去重）
                            val dynamicHeaders = dataList.flatMap { it.dynamicColumns.keys }.distinct()

                            // 步骤 3：合并固定头 + 动态头（动态头父级标题为 "动态列"）
                            val allHeaders = fixedHeaders + dynamicHeaders.map { listOf("系统属性", it) }

                            // 步骤 4：构造数据行（固定字段 + 动态字段）
                            val dataRows = dataList.map { product ->
                                // 获取所有固定字段（排除 dynamicColumns）并按 @ExcelProperty.index 排序
                                val sortedProperties = TemuPlatformProductExportDTO::class.declaredMemberProperties
                                    .filterNot { it.name in listOf("dynamicColumns") }
                                    .mapNotNull { prop ->
                                        val excelProp = prop.javaField?.getAnnotation(ExcelProperty::class.java)
                                        excelProp?.let {
                                            prop to (it.index.takeIf { idx -> idx >= 0 } ?: Int.MAX_VALUE)
                                        }
                                    }
                                    .sortedBy { it.second } // 按 index 排序
                                    .map { it.first } // 提取排序后的字段

                                // 按排序后的字段顺序提取值
                                val fixedData = sortedProperties.map { prop -> prop.get(product) }

                                // 动态字段按 dynamicHeaders 顺序取值
                                val dynamicData = dynamicHeaders.map { product.dynamicColumns[it] }

                                fixedData + dynamicData
                            }

                            if (dataList.isNotEmpty()) {
                                val writeSheet = EasyExcel.writerSheet(sheetName).head(allHeaders).build()
                                excelWriter.write(dataRows, writeSheet)
                            }
                        }
                    }
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败，使用 EasyExcel 写入文件时出错", e)
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            throw BusinessException("导出失败", e)
        }
    }

    /**
     * 处理TEMU数据导出
     */
    private fun exportProductByTemu(
        vo: ProductTemplateTemuSpu,
        productIdMap: Map<Long?, Product?>,
    ): MutableList<TemuPlatformProductExportDTO> {
        val allTemuData: MutableList<TemuPlatformProductExportDTO> = mutableListOf()
        // 获取product
        val product = productIdMap[vo.productId]
        if (product == null) {
            return allTemuData
        }
        // 获取Temu模板SPU
        val spuTemu = productTemplateTemuSpuRepository.getByProductId(vo.productId!!)
        val productTemplateTemuSpu = spuTemu?.find { it.businessType == ProductShopBusinessType.FULLY_MANAGED.value }
        if (productTemplateTemuSpu == null) {
            return allTemuData
        }
        // 获取Temu模板SKC
        val skcTemuList = productTemplateTemuSkcRepository.listByTemuSpuId(productTemplateTemuSpu.temuSpuId!!)
        if (skcTemuList.isEmpty()) {
            return allTemuData
        }
        val skcMap = skcTemuList.associateBy { it.temuSkcId }
        // 获取Temu模板SKU
        val skuTemuList = productTemplateTemuSkuRepository.listByTemuSkcIdList(skcTemuList.mapNotNull { it.temuSkcId })
        if (skuTemuList.isEmpty()) {
            return allTemuData
        }
        if (productTemplateTemuSpu.productNameEn.isBlank() || productTemplateTemuSpu.productName.isBlank()) {
            val resp = productTitleGenerateService.previewTitle(
                ProductTitleGenerateReq().apply {
                    productId = product.productId
                    this.product = product
                }
            )
            resp.productTitleRuleId?.let {
                fun assignTitleIfValid(newTitle: String?, oldTitle: String?) =
                    if (!newTitle.isNullOrBlank() && newTitle.length < ProductInnerConstant.getTitleMaxLength(PlatformEnum.TEMU)) newTitle else oldTitle

                productTemplateTemuSpu.productNameEn = assignTitleIfValid(resp.title, productTemplateTemuSpu.productNameEn)
                productTemplateTemuSpu.productName = assignTitleIfValid(resp.title, productTemplateTemuSpu.productName)

                productTemplateTemuSpu.generatedTitleOverLengthFlag =
                    if (resp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                productTemplateTemuSpu.generatedTitleMissingFieldsJson = resp.missingFields.toJson()
            }
        }

        val imageCollection = getImages(product.spuCode!!)
        val detailImageList = imageCollection.sellingPointImages

        //获取SKC的轮播图
        val productDetailImages: List<ImageAniVo> = imageCollection.productDetailImages
        val skcList = skcTemuList.map { it.skc!! }
        val skcImages = imageCollectionHelper.fetchSkcImages(
            productDetailImages,
            skcList,
            ImageTypeEnum.PRODUCT_DETAIL.code
        )

        val calPriceReq = AutoCalPriceReq().apply {
            this.productId = product.productId!!
            this.shopId = product.shopId
            this.platform = platformEnum
            this.shopBusinessType = ProductShopBusinessType.FULLY_MANAGED.value
        }
        val autoPriceList: List<AutoCalPriceResp> = productPriceManagementService.autoCalPrice(calPriceReq)
        val priceMap = autoPriceList.associateBy { autoPrice -> autoPrice.skc }

        skuTemuList.forEach { skuTemu ->

            val skcInfo = skcMap[skuTemu.temuSkcId!!]

            val importDto = TemuPlatformProductExportDTO().apply {
                this.level = "SKU"
                this.spuCode = product.spuCode                  // *SPU
                this.productName = productTemplateTemuSpu.productName                 // 商品名称(中文)
                this.productNameEn = productTemplateTemuSpu.productName               // 商品名称(英文)
                this.categoryName = product.categoryName               // (内部)商品类目
                val originJson = productTemplateTemuSpu.countryOriginPlace?.parseJson(ProductTemuCountryOriginPlaceReq::class.java)
                if (originJson != null) {
                    var originPlaceNameTmp = "${originJson.originName}"
                    if (originJson.secondaryOriginName.isNotBlank()) {
                        originPlaceNameTmp = originPlaceNameTmp + ",${originJson.secondaryOriginName}"
                    }
                    this.originPlaceName = originPlaceNameTmp
                }
                this.skcCode = skcInfo?.skc
                this.skuCode = skuTemu.sellerSku
                this.mainColor = skcInfo?.color
                this.sizeGroup = "字母码"
                this.sizeType = ""
                this.specificationType = "尺码"
                this.size = skuTemu.sizeName

                val price = priceMap[skcInfo?.skc]
                val cnyPrice = price?.countryPriceList?.find { it.country == CountryEnum.CN.code }?.salePrice
                val usdPrice = price?.countryPriceList?.find { it.country == CountryEnum.US.code }?.salePrice

                if (skuTemu.declaredPrice.isNotBlank()) {
                    val declaredPriceJson = skuTemu.declaredPrice!!.parseJsonList(FullyManagedPrice::class.java)
                    if (declaredPriceJson.isNotEmpty()) {
                        this.declaredPriceUsd = declaredPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.USD.value }?.price
                        this.declaredPriceCny = declaredPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.CNY.value }?.price
                    }
                }
                if (skuTemu.recommendedPrice.isNotBlank()) {
                    val recommendedPriceJson = skuTemu.recommendedPrice!!.parseJsonList(FullyManagedPrice::class.java)
                    if (recommendedPriceJson.isNotEmpty()) {
                        this.retailPriceUsd = recommendedPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.USD.value }?.price
                        this.retailPriceCny = recommendedPriceJson.firstOrNull { f -> f.code == TemuCurrencyTypeEnum.CNY.value }?.price
                    }
                }

                // 价格为空, 则使用计算价格
                if (this.declaredPriceUsd == null) {
                    this.declaredPriceUsd = usdPrice
                }
                if (this.declaredPriceCny == null) {
                    this.declaredPriceCny = cnyPrice
                }
                // 2025年6月19日, 产品说不用计算建议零售价
//                if (this.retailPriceCny == null) {
//                    this.retailPriceCny = cnyPrice
//                }
//                if (this.retailPriceUsd == null) {
//                    this.retailPriceUsd = usdPrice
//                }
                if (skuTemu.classifiedAttrs.isNotBlank()) {
                    val classifiedAttrsJson = skuTemu.classifiedAttrs?.parseJson<ClassifiedAttr>()
                    if (classifiedAttrsJson != null) {
                        this.skuClassification = ClassifiedAttrTypeCodeEnum.getByCode(classifiedAttrsJson.typeCode)?.desc
                        this.quantity = classifiedAttrsJson.quantity
                        this.unit = ClassifiedAttrUnitEnum.getByCode(classifiedAttrsJson.unitCode)?.desc
                    }
                }
                this.maxLength = skuTemu.longestEdge
                this.middleLength = skuTemu.secondEdge
                this.minLength = skuTemu.shortestEdge
                this.weight = skuTemu.weight

                val skcImageTmpList = skcImages[skcInfo?.skc]
                if (skcImageTmpList.isNotEmpty()) {
                    this.productImage1 = skcImageTmpList?.getOrNull(0)?.ossImageUrl
                    this.productImage2 = skcImageTmpList?.getOrNull(1)?.ossImageUrl
                    this.productImage3 = skcImageTmpList?.getOrNull(2)?.ossImageUrl
                    this.productImage4 = skcImageTmpList?.getOrNull(3)?.ossImageUrl
                    this.productImage5 = skcImageTmpList?.getOrNull(4)?.ossImageUrl

                    // 详情图是70+勾选的skc
                    var detailSkcImageTmpList = listOf<ImageAniVo>()
                    // 先找出勾选的skc
                    val detailedImageSetting = temuSkcDetailedImageRepository.getByProductId(product.productId!!)
                    if (detailedImageSetting != null) {
                        // 有勾选, 则使用勾选的skc图
                        val tmpList = skcImages[detailedImageSetting.skc]
                        if (tmpList.isNotEmpty()) {
                            detailSkcImageTmpList = tmpList!!
                        }
                    }
                    if (detailSkcImageTmpList.isNotEmpty()) {
                        // 空, 则使用第一个skc图
                        val firstSkcImage = skcImages.values.firstOrNull()
                        if (firstSkcImage.isNotEmpty()) {
                            detailSkcImageTmpList = firstSkcImage!!
                        }
                    }

                    this.detailImages = (detailImageList + detailSkcImageTmpList).mapNotNull { it.ossImageUrl }.joinToString("|")
                }
            }
            // 设置属性
            val attributes = productAttributesRepository.listByProductId(vo.productId!!, PlatformEnum.TEMU.platformId)
            if (attributes.isNotEmpty()) {
                val attributePairResps = productRepository.selectAttributesById(
                    attributes.map { att ->
                        AttributePairReq().apply {
                            this.attributeId = att.attributeId
                            this.attributeValueId = att.attributeValueId
                        }
                    }
                )
                if (attributePairResps.isNotEmpty()) {
                    val map = mutableMapOf<String, Any?>()
                    attributePairResps.forEach { att ->
                        map[att.attributeName!!] = att.attributeValue
                    }
                    importDto.dynamicColumns = map
                }
            }
            allTemuData.add(importDto)
        }

        // 特殊处理: Temu需要分级, SPU和SKU, 上面的数据是SKU, 因此需要提取一个SPU, 加到SKU的最前面, 且SKU需要分组排序
        val newDataList: MutableList<TemuPlatformProductExportDTO> = mutableListOf()
        allTemuData.groupBy { it.spuCode }.forEach { (spuCode, skus) ->
            val spu = skus.first()
            val spuImportDto = TemuPlatformProductExportDTO().apply {
                this.level = "SPU"
                this.spuCode = spu.spuCode
                this.productName = spu.productName
                this.productNameEn = spu.productNameEn
                this.categoryName = spu.categoryName
                this.originPlaceName = spu.originPlaceName
                this.material = spu.material
                this.composition = spu.composition
                this.compositionRatio = spu.compositionRatio
                this.pattern = spu.pattern
                this.detail = spu.detail
                this.collarType = spu.collarType
                this.style = spu.style
                this.careInstructions = spu.careInstructions
                this.fabric = spu.fabric
                this.applicableCrowd = spu.applicableCrowd
                this.season = spu.season
                this.isTransparent = spu.isTransparent
                this.silhouette = spu.silhouette
                this.weavingMethod = spu.weavingMethod
                this.printingType = spu.printingType
                this.fabricTexture = spu.fabricTexture
                this.fabricWeight1 = spu.fabricWeight1
                this.fabricWeight2 = spu.fabricWeight2
                this.liningTexture = spu.liningTexture
                this.dynamicColumns = spu.dynamicColumns
            }
            newDataList.add(spuImportDto)
            newDataList.addAll(skus.map { sku ->
                sku.productName = null
                sku.productNameEn = null
                sku.originPlaceName = null
                sku.material = null
                sku.composition = null
                sku.compositionRatio = null
                sku.pattern = null
                sku.detail = null
                sku.collarType = null
                sku.style = null
                sku.careInstructions = null
                sku.fabric = null
                sku.applicableCrowd = null
                sku.season = null
                sku.isTransparent = null
                sku.silhouette = null
                sku.weavingMethod = null
                sku.printingType = null
                sku.fabricTexture = null
                sku.fabricWeight1 = null
                sku.fabricWeight2 = null
                sku.liningTexture = null
                sku
            })
        }
        return newDataList
    }

    /**
     * 从 DTO 类中动态提取 @ExcelProperty 定义的固定头（排除指定字段）
     * @param excludeFields 需要排除的字段名（如动态列字段名）
     */
    fun <T : Any> getFixedHeaders(dtoClass: KClass<T>, excludeFields: List<String> = emptyList()): List<List<String>> {
        return dtoClass.declaredMemberProperties
            .filterNot { it.name in excludeFields }
            .mapNotNull { prop ->
                val excelProp = prop.javaField?.getAnnotation(ExcelProperty::class.java)
                excelProp?.let {
                    // 使用 index 属性作为排序依据
                    it.value.toList() to (it.index.takeIf { idx -> idx >= 0 } ?: Int.MAX_VALUE)
                }
            }
            .sortedBy { it.second } // 按 index 排序
            .map { it.first } // 提取 value 列表
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }
}