package tech.tiangong.pop.component.lazada

import com.alibaba.fastjson2.JSON
import com.lazada.lazop.util.FileItem
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.ofp.open.common.req.SpuItemStatusQueryReq
import tech.tiangong.pop.common.enums.ChannelEnum
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.TranslateComponent
import tech.tiangong.pop.constant.RedisConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.LazadaVideoUploadPartDTO
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.enums.StockTypeEnum
import tech.tiangong.pop.external.EtaClientExternal
import tech.tiangong.pop.helper.ProductPublishLazadaHelper
import tech.tiangong.pop.req.product.AutoCalPriceReq
import tech.tiangong.pop.req.product.lazada.ProductLazadaExistingPublishReq
import tech.tiangong.pop.req.product.lazada.ProductPendingLazadaPlatformUpdateReq
import tech.tiangong.pop.resp.product.AutoCalCountryPriceResp
import tech.tiangong.pop.resp.product.ProductTitleResp
import tech.tiangong.pop.service.category.LazadaCategoryService
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.ProductBarcodeService
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.ProductSaleAttributeV2Service
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.FileInputStream
import java.nio.file.Files
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ExecutorService


/**
 * 调用Lazada逻辑组件
 * <AUTHOR>
 * @date 2025-4-1 11:13:37
 */
@Component
@Slf4j
class CallLazadaComponent(
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val shopSellerMappingRepository: ShopSellerMappingRepository,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val lazadaBrandRepository: LazadaBrandRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val productBarcodeService: ProductBarcodeService,
    private val lazadaUpdateProductComponent: LazadaUpdateProductComponent,
    private val etaClientExternal: EtaClientExternal,
    private val transactionManager: PlatformTransactionManager,
    private val productPriceManagementService: ProductPriceManagementService,
    private val sellerSkuFlatInfoRepository: SellerSkuFlatInfoRepository,
    private val lockComponent: LockComponent,
    private val translateComponent: TranslateComponent,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val lazadaCategoryService: LazadaCategoryService,
    private val lazadaProductPriceAlertComponent: LazadaProductPriceAlertComponent,
    private val productSyncLogRepository: ProductSyncLogRepository,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val lazadaApiService: LazadaApiService,
    private val productSaleAttributeV2Service: ProductSaleAttributeV2Service
) {

    private val platform = PlatformEnum.LAZADA
    private val channel = ChannelEnum.ALIBABA

    /**
     * 上架新商品-Lazada
     * @param req
     */
    fun publishProduct(req: ProductPendingLazadaPlatformUpdateReq) {
        val countryList: List<String> = req.countryList
        if (countryList.isEmpty()) {
            throw IllegalArgumentException("国家站点不能为空")
        }

        val templateSpu = productTemplateLazadaSpuRepository.getById(req.lazadaSpuId) ?: throw PublishAscBizException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw PublishAscBizException("商品不存在")
        val shop = shopRepository.getById(templateSpu.shopId) ?: throw PublishAscBizException("商品绑定的店铺不存在")
        val currentUser = CurrentUserHolder.get()

        // 获取店铺信息
        val shopId: Long = shop.shopId!!

        // 获取shop映射数据
        val shopMappingList = shopSellerMappingRepository.getByShopId(shopId)
        if (shopMappingList.isEmpty()) {
            throw BaseBizException("该店铺无可用站点")
        }
        // 提取可用站点
        val availableCountryList = shopMappingList.mapNotNull { it.country }
        val autoCulPriceList = productPriceManagementService.autoCalPrice(AutoCalPriceReq().apply {
            this.productId = templateSpu.productId!!
            this.shopId = shop.shopId
        })
        val priceMap = mutableMapOf<String, AutoCalCountryPriceResp>()
        autoCulPriceList.forEach { priceSkc ->
            priceSkc.countryPriceList.forEach {
                priceMap[priceSkc.productSkcId.toString() + it.country!!] = it
            }
        }

        // 获取SKC模板信息
        val lazadaSkcList = productTemplateLazadaSkcRepository.getByLazadaSpuId(templateSpu.lazadaSpuId!!).filter { it.state == Bool.YES.code }
        // 获取SKU模板信息
        val lazadaSkcIdList = lazadaSkcList.mapNotNull { it.lazadaSkcId }.distinct()
        val lazadaSkuList = productTemplateLazadaSkuRepository.getByLazadaSkcId(lazadaSkcIdList)
        val lazadaSkuMap = lazadaSkuList.groupBy { it.lazadaSkcId }

        // 获取发货期(默认期货)
        val stockTypeEnum = StockTypeEnum.FUTURES
        var delayDeliveryDays: Int? = null
        val etaConfigList = etaClientExternal.getAllEtaConfig()
        etaConfigList
            ?.filter { it.platformId == LAZADA.platformId }
            ?.filter { it.styleType == product.supplyMode }
            ?.forEach { etaConfig ->
                delayDeliveryDays = etaConfig.futures
            }

        // 获取品类映射
        val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
            product.categoryId!!,
            LAZADA.platformId,
            ALIBABA.channelId
        ) ?: throw BusinessException("找不到品类映射信息")

        if (Objects.isNull(categoryMapping.platformCategoryId)) {
            throw PublishAscBizException("找不到平台品类ID")
        }

        val productTitleList = JSON.parseArray(templateSpu.allCountryTitle, ProductTitleResp::class.java) ?: emptyList()

        val saleGoodsIdList = mutableListOf<Long>()
        countryList
            .filter { availableCountryList.contains(it) }
            .forEach { country ->
                // 锁5分钟
                val expiredDuration = Duration.ofMinutes(5)
                lockComponent.lock(RedisConstants.LAZADA_CREATE_PRODUCT_LOCK + templateSpu.spuCode!! + shopId + country, expiredDuration) {
                    // 手动事务
                    TransactionTemplate(transactionManager).execute { status ->
                        val now = LocalDateTime.now()
                        try {
                            val platformCategoryId = lazadaCategoryService.getCategoryId(country, categoryMapping.platformCategoryName!!)?.toString()
                                ?: throw PublishAscBizException("找不到lazada品类, country: $country")
                            // 获取sale系列的表, 是否已经存在Lazada
                            val existSaleGoods = saleGoodsRepository.getByProductIdAndShopIdAndCountry(templateSpu.productId!!, shopId, country)
                            if (existSaleGoods != null) {
                                throw PublishAscBizException("[SPU:${templateSpu.spuCode} 店铺:${shop.shopName} 站点:${country}]已上过架")
                            }
                            val lazadaBrand = shop.brandId?.let { lazadaBrandRepository.getById(it.toLong()) }

                            val countryTitle: String = getCountryTitle(productTitleList, country, templateSpu, product, shopId = shopId)

                            // 创建sale相关表
                            val saleGoods = SaleGoods().apply {
                                this.saleGoodsId = IdHelper.getId()
                                this.productId = templateSpu.productId
                                this.publishState = ProductPublishStateEnum.ACTIVE.code
                                this.channelId = channel.channelId
                                this.platformId = platform.platformId
                                this.isHistory = Bool.NO.code
                                this.productTitle = countryTitle
                                this.spuCode = templateSpu.spuCode
                                this.country = country
                                this.publishTime = now
                                this.latestPublishTime = now
                                this.createdTime = now
                                this.revisedTime = now
                                this.publishUserId = currentUser.id
                                this.latestPublishUserId = currentUser.id
                                this.publishUserName = currentUser.name
                                this.latestPublishUserName = currentUser.name
                                this.shopId = shop.shopId
                                this.shopName = shop.shopName
                                this.brandId = lazadaBrand?.brandId
                                this.brandName = lazadaBrand?.name
                                this.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                                this.platformCategoryId = platformCategoryId
                                this.platformCategoryName = categoryMapping.platformCategoryName
                                this.stockType = stockTypeEnum.code
                                this.countryStockType = stockTypeEnum.code
                                this.delayDeliveryDays = delayDeliveryDays
                                this.packageWeight = templateSpu.packageWeight
                                this.packageDimensionsLength = templateSpu.packageDimensionsLength
                                this.packageDimensionsHeight = templateSpu.packageDimensionsHeight
                                this.packageDimensionsWidth = templateSpu.packageDimensionsWidth
                            }
                            saleGoodsRepository.save(saleGoods)
                            saleGoodsIdList.add(saleGoods.saleGoodsId!!)

                            //商品属性值
                            productSaleAttributeV2Service.insertSaleAttributes(templateSpu.lazadaSpuId!!, saleGoods.saleGoodsId!!, platform)

                            // 创建sale skc相关表
                            val saveSkcList = mutableListOf<SaleSkc>()
                            val saveSkuList = mutableListOf<SaleSku>()
                            lazadaSkcList.forEach { lazadaSkc ->

                                val saleSkc = SaleSkc().apply {
                                    this.saleSkcId = IdHelper.getId()
                                    this.saleGoodsId = saleGoods.saleGoodsId
                                    this.productSkcId = lazadaSkc.productSkcId
                                    this.skc = lazadaSkc.skc
                                    this.color = lazadaSkc.color
                                    this.platformColor = lazadaSkc.platformColor
                                    this.colorCode = lazadaSkc.colorCode
                                    this.colorAbbrCode = lazadaSkc.colorAbbrCode
                                    this.pictures = lazadaSkc.pictures
                                    this.combo = lazadaSkc.combo
                                    this.state = lazadaSkc.state
                                    this.cbPrice = lazadaSkc.cbPrice
                                    this.localPrice = lazadaSkc.localPrice
                                    this.purchasePrice = lazadaSkc.purchasePrice
                                    this.costPrice = lazadaSkc.costPrice
                                }
                                saveSkcList.add(saleSkc)
                                // 获取对应的计算价格信息
                                val autoPrice = priceMap[saleSkc.productSkcId.toString() + country]
                                // 创建sale sku相关表
                                lazadaSkuMap[lazadaSkc.lazadaSkcId]
                                    ?.filter { Objects.equals(it.country, country) }
                                    ?.forEach { lazadaSku ->
                                        val saleSku = SaleSku().apply {
                                            this.saleSkuId = IdHelper.getId()
                                            this.saleGoodsId = saleGoods.saleGoodsId
                                            this.saleSkcId = saleSkc.saleSkcId
                                            this.productId = templateSpu.productId
                                            this.productSkcId = lazadaSkc.productSkcId
                                            this.country = country
                                            this.stockQuantity = lazadaSku.stockQuantity
                                            this.sizeName = lazadaSku.sizeName
                                            this.lzdSizeName = lazadaSku.sizeName
                                            this.shopName = saleGoods.shopName
                                            this.brandId = saleGoods.brandId
                                            this.brandName = saleGoods.brandName
                                            this.enable = lazadaSku.enableState
                                            this.salePrice = lazadaSku.salePrice ?: autoPrice?.salePrice
                                            this.retailPrice = lazadaSku.retailPrice ?: autoPrice?.retailPrice
                                            this.purchaseSalePrice = lazadaSku.purchaseSalePrice ?: autoPrice?.purchaseSalePrice
                                            this.purchaseRetailPrice = lazadaSku.purchaseRetailPrice ?: autoPrice?.purchaseRetailPrice
                                            this.regularSalePrice = lazadaSku.regularSalePrice
                                            this.regularRetailPrice = lazadaSku.regularRetailPrice
                                            this.platformCategoryId = saleGoods.platformCategoryId
                                            this.barcode = lazadaSku.barcode
                                            this.barcodes = lazadaSku.barcodes
                                            this.platformCategoryName = saleGoods.platformCategoryName
                                            this.publishState = if (lazadaSku.flagFrontend == Bool.YES.code) ProductPublishStateEnum.ACTIVE.code else ProductPublishStateEnum.IN_ACTIVE.code
                                            this.delayDeliveryDays = saleGoods.delayDeliveryDays
                                            this.sellerSku = lazadaSku.sellerSku ?: generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, this)
                                        }
                                        saveSkuList.add(saleSku)
                                    }
                            }
                            if (saveSkcList.isNotEmpty()) {
                                saleSkcRepository.saveBatch(saveSkcList)
                            }
                            if (saveSkuList.isNotEmpty()) {
                                saleSkuRepository.saveBatch(saveSkuList)
                            }

                            // 绑定Lazada条码
                            bindBarcode(product, saleGoods)

                            // 上架前价格兜底
                            val validationResult = lazadaProductPriceAlertComponent.validateBeforePublish(
                                product = product,
                                saleGoods = saleGoods,
                                saleSkcList = null,
                                saleSkuList = null,
                            )
                            if (!validationResult.valid) {
                                throw BusinessException("商品价格兜底不通过，无法上架：${validationResult.message}")
                            }

                            // 创建/更新 Lazada商品
                            lazadaUpdateProductComponent.updateProduct(shopId, saleGoods, templateSpu.lazadaSpuId)

                            // 标记提交成功
                            tagSubmitPlatform(templateSpu.productId!!)

                            // 更新待上架状态
                            productTemplateLazadaSpuRepository.ktUpdate()
                                .set(ProductTemplateLazadaSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
                                .set(ProductTemplateLazadaSpu::taskCompleteTime, LocalDateTime.now())
                                .eq(ProductTemplateLazadaSpu::lazadaSpuId, templateSpu.lazadaSpuId)
                                .update()

                            null
                        } catch (e: Exception) {
                            log.error(e) { "事务执行过程中出现异常，触发回滚 ${e.message}" }

                            runAsync(asyncExecutor) {
                                if (e !is PublishGlobalBizException) {
                                    productSyncLogRepository.addErrorSyncLog(
                                        productId = product.productId!!,
                                        templateSpuId = templateSpu.lazadaSpuId,
                                        platformName = platform.platformName,
                                        shopId = shop.shopId,
                                        shopName = shop.shopName,
                                        country = country,
                                        error = e.getRootMessage(),
                                        opType = PlatformOperatorTypeEnum.ACTIVE.code
                                    )
                                }

                                // 更新待上架状态
                                productTemplateLazadaSpuRepository.ktUpdate()
                                    .set(ProductTemplateLazadaSpu::taskStatus, ProductPendingTaskStatusEnum.FAILED.code)
                                    .eq(ProductTemplateLazadaSpu::lazadaSpuId, templateSpu.lazadaSpuId)
                                    .update()
                            }

                            // 手动标记事务回滚
                            status.setRollbackOnly()
                            null
                        }
                    }

                    // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
                    setBarcodeImage(templateSpu.productId!!, shopId, country)
                }
            }
    }

    /**
     * 获取站点翻译标题
     */
    private fun getCountryTitle(
        productTitleList: List<ProductTitleResp>,
        country: String,
        lazadaSpu: ProductTemplateLazadaSpu?,
        product: Product,
        existingSaleGoodsMap: Map<String, SaleGoods> = emptyMap(),
        generatedProductTitle: String = "",
        shopId: Long? = null,
    ): String {
        // 1. 优先使用生成的标题（需要翻译）
        if (generatedProductTitle.isNotBlank()) {
            return translateTitleIfNeeded(generatedProductTitle, country)
        }

        // 2. 前端预翻译（无需翻译）
        productTitleList.firstOrNull { it.country == country }?.title
            ?.takeIf { it.isNotBlank() }
            ?.let { return it }

        // 3. 同站点已发布商品（无需翻译）
        existingSaleGoodsMap[country]?.productTitle
            ?.takeIf { it.isNotBlank() }
            ?.let { return it }

        // 4. 其他来源标题（需要翻译）
        val fallbackTitles = sequence {
            yield(lazadaSpu?.productTitle)
            // PH/SG标题（按优先顺序）
            yield(existingSaleGoodsMap[LazadaCountryEnum.PH.code]?.productTitle)
            yield(existingSaleGoodsMap[LazadaCountryEnum.SG.code]?.productTitle)
            // 已发布任一标题
            yield(existingSaleGoodsMap.values.firstOrNull { it.productTitle.isNotBlank() }?.productTitle)
            // 原始商品标题
            yield(product.productTitle)
        }.filterNotNull().filter { it.isNotBlank() }.firstOrNull()

        return fallbackTitles?.let { translateTitleIfNeeded(it, country) }
            ?: throw PublishAscBizException(
                "商品标题不能为空, 商品ID: ${product.productId}, SPU: ${lazadaSpu?.spuCode ?: "未知"}"
            )
    }

    /**
     * 翻译商品标题
     */
    private fun translateTitleIfNeeded(title: String, country: String): String {
        return if (country in ProductPublishLazadaHelper.translateCountries) {
            translateComponent.translateAndSetTitle(title, country)
                ?: throw BusinessException("翻译商品标题失败, 国家: $country, 标题: $title")
        } else {
            title
        }
    }

    /**
     * 标记提交平台
     */
    private fun tagSubmitPlatform(productId: Long) {
        // 重新获取最新数据库, 多站点场景
        val newProduct = productRepository.getById(productId)
        var submitPlatformIdList: List<Long>? = newProduct.submitPlatform?.parseJsonList(Long::class.java)
        if (submitPlatformIdList == null) {
            submitPlatformIdList = mutableListOf()
        }
        if (!submitPlatformIdList.contains(LAZADA.platformId)) {
            val updateSubmit = mutableListOf<Long>()
            updateSubmit.addAll(submitPlatformIdList)
            updateSubmit.add(LAZADA.platformId)
            val updateProduct = Product().apply {
                this.productId = newProduct.productId
                this.isSyncPlatform = Bool.YES.code
                this.submitPlatform = updateSubmit.toJson()
            }
            productRepository.updateById(updateProduct)
        }
    }

    /**
     * 更新或新增Lazada站点数据
     * @param req 请求参数，包含多个产品和店铺组合以及目标国家
     */
    fun publishExistingOrAddCountry(req: ProductLazadaExistingPublishReq) {
        // 处理每一个productId和shopId组合
        val currentUser = CurrentUserHolder.get()
        req.productShopItems.forEach { item ->
            val productId = item.productId
            val shopId = item.shopId

            // 获取shop映射数据
            val shopMappingList = shopSellerMappingRepository.getByShopId(shopId)
            if (shopMappingList.isEmpty()) {
                log.warn { "该店铺无可用站点" }
                return@forEach
            }
            // 提取可用站点
            val availableCountryList = shopMappingList.mapNotNull { it.country }

            // 获取商品信息
            val product =
                productRepository.getById(productId) ?: throw PublishAscBizException("商品 ID:$productId 不存在")
            val lazadaSpu = productTemplateLazadaSpuRepository.getByProductId(productId)
            // 获取店铺信息
            val shop = shopRepository.getById(shopId) ?: throw PublishAscBizException("店铺 ID:$shopId 不存在")
            // 查找品类映射
            val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
                product.categoryId!!,
                LAZADA.platformId,
                ALIBABA.channelId
            ) ?: throw PublishAscBizException("找不到品类映射信息")

            if (Objects.isNull(categoryMapping.platformCategoryId)) {
                throw PublishAscBizException("找不到平台品类ID")
            }
            // 获取该商品在该店铺下已上架的所有国家数据
            val existingSaleGoodsList = saleGoodsRepository.listByProductIdAndShopId(productId, shopId).also {
                if (it.isEmpty()) {
                    throw PublishAscBizException("找不到已上架的Lazada商品，无法进行追加上架操作。商品SPU: ${product.spuCode}, 店铺: ${shop.shopName}")
                }
            }

            // 计算价格
            val autoCulPriceList = productPriceManagementService.autoCalPrice(AutoCalPriceReq().apply {
                this.productId = productId
                this.shopId = shopId
            })
            val priceMap = mutableMapOf<String, AutoCalCountryPriceResp>()
            autoCulPriceList.forEach { priceSkc ->
                priceSkc.countryPriceList.forEach {
                    priceMap[priceSkc.productSkcId.toString() + it.country!!] = it
                }
            }

            // 远程调用履约，获取发货期信息
            val etaReqList = listOf(SpuItemStatusQueryReq().apply {
                this.spuCode = product.spuCode
                this.platformId = LAZADA.platformId
            })
            val etaRespList = etaClientExternal.querySpuItemStatus(etaReqList)
            if (!etaRespList.isNullOrEmpty()) {
                // 更新SALE_SPU发货信息
                existingSaleGoodsList.forEach { saleGoods ->
                    val etaResp = etaRespList.firstOrNull { eta -> eta.spuCode == saleGoods.spuCode }
                    var isUpdate = false
                    if (etaResp != null && etaResp.delayDeliveryDays != null && saleGoods.delayDeliveryDays != etaResp.delayDeliveryDays) {
                        saleGoods.delayDeliveryDays = etaResp.delayDeliveryDays
                        isUpdate = true
                    }
                    if (etaResp != null && etaResp.stockType != null && saleGoods.stockType != etaResp.stockType) {
                        saleGoods.stockType = etaResp.stockType
                        isUpdate = true
                    }

                    val stockType = etaResp?.countryList?.firstOrNull { stock -> stock.country == saleGoods.country }?.stockType
                    if (stockType != null && saleGoods.countryStockType != stockType) {
                        saleGoods.countryStockType = stockType
                        isUpdate = true
                    }
                    if (isUpdate) {
                        saleGoodsRepository.updateById(saleGoods)
                    }
                }
            } else {
                log.warn { "获取发货期信息失败，商品ID:$productId, SPU:${product.spuCode}" }
            }

            val existingSaleGoodsMap = existingSaleGoodsList.associateBy { it.country!! }
            // 获取参考模板(取第一个已上架的国家数据作为参考)
            val referenceSaleGoods = existingSaleGoodsList.first()

            // 先获取参考SaleGoods下的所有SKC和SKU数据，避免重复查询
            val referenceSaleSkcList = saleSkcRepository.findBySaleGoodsId(referenceSaleGoods.saleGoodsId!!)
            val referenceSkcIds = referenceSaleSkcList.mapNotNull { it.saleSkcId }.toSet()

            val referenceSkuListAll = saleSkuRepository.findBySaleSkcIds(referenceSkcIds)
            // 将参考SKC和SKU按照ID分组，方便后续查找
            val referenceSkuMap = referenceSkuListAll.groupBy { it.saleSkcId!! }
            val productTitleList = if (lazadaSpu.isNotNull()) {
                JSON.parseArray(lazadaSpu!!.allCountryTitle, ProductTitleResp::class.java) ?: emptyList()
            } else emptyList()

            req.countryList
                .filter { availableCountryList.contains(it) }
                .forEach { country ->
                    val now = LocalDateTime.now()
                    // 手动事务
                    TransactionTemplate(transactionManager).execute { status ->
                        try {
                            var saleGoodsId = existingSaleGoodsMap[country]?.saleGoodsId
                            // 检查该国家是否已上架
                            val existSaleGoods = existingSaleGoodsMap[country]
                            if (existSaleGoods != null) {
                                log.info { "商品已在国家[$country]上架，进行更新操作. 商品ID:$productId, SPU:${product.spuCode}, 店铺ID:$shopId" }
                                saleGoodsRepository.updateById(SaleGoods().apply {
                                    this.saleGoodsId = existSaleGoods.saleGoodsId
                                    this.publishState = ProductPublishStateEnum.ACTIVE.code
                                    this.latestPublishTime = now
                                    this.latestPublishUserId = currentUser.id
                                    this.latestPublishUserName = currentUser.name
                                })

                                // 更新所有SKU的发货期信息 - 批量更新提高性能
                                val saleSkuList = saleSkuRepository.getBySaleGoodsIds(listOf(existSaleGoods.saleGoodsId!!))
                                val updateSaleSkuList = saleSkuList.map { sku ->
                                    SaleSku().apply {
                                        this.saleSkuId = sku.saleSkuId
                                        this.publishState = if (sku.enable == Bool.YES.code) {
                                            ProductPublishStateEnum.ACTIVE.code
                                        } else {
                                            sku.publishState
                                        }
                                        this.delayDeliveryDays = existSaleGoods.delayDeliveryDays
                                    }
                                }
                                if (updateSaleSkuList.isNotEmpty()) {
                                    saleSkuRepository.updateBatchById(updateSaleSkuList)
                                }
                            } else {
                                // 未上架的情况，复制已有站点数据
                                log.info { "商品在国家[$country]未上架，进行复制上架操作. 商品ID:$productId, SPU:${product.spuCode}, 店铺ID:$shopId" }
                                val lazadaBrand = shop.brandId?.let { lazadaBrandRepository.getById(it.toLong()) }
                                val countryTitle: String = getCountryTitle(productTitleList, country, lazadaSpu, product, existingSaleGoodsMap, shopId = shopId)
                                // 创建新的saleGoods
                                val newSaleGoods = SaleGoods().apply {
                                    this.saleGoodsId = IdHelper.getId()
                                    this.productId = referenceSaleGoods.productId
                                    this.publishState = ProductPublishStateEnum.ACTIVE.code
                                    this.publishTime = now
                                    this.createdTime = now
                                    this.latestPublishTime = now
                                    this.revisedTime = now
                                    this.publishUserId = currentUser.id
                                    this.latestPublishUserId = currentUser.id
                                    this.publishUserName = currentUser.name
                                    this.latestPublishUserName = currentUser.name
                                    this.channelId = LAZADA.platformId
                                    this.platformId = ALIBABA.channelId
                                    this.isHistory = Bool.NO.code
                                    this.productTitle = countryTitle
                                    this.spuCode = referenceSaleGoods.spuCode
                                    this.country = country
                                    this.shopId = referenceSaleGoods.shopId
                                    this.shopName = referenceSaleGoods.shopName
                                    this.brandId = lazadaBrand?.brandId
                                    this.brandName = lazadaBrand?.name
                                    this.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                                    this.platformCategoryId = categoryMapping.platformCategoryId
                                    this.platformCategoryName = categoryMapping.platformCategoryName
                                    this.stockType = referenceSaleGoods.stockType
                                    this.countryStockType = referenceSaleGoods.countryStockType
                                    this.delayDeliveryDays = referenceSaleGoods.delayDeliveryDays
                                    this.packageWeight = referenceSaleGoods.packageWeight
                                    this.packageDimensionsLength = referenceSaleGoods.packageDimensionsLength
                                    this.packageDimensionsHeight = referenceSaleGoods.packageDimensionsHeight
                                    this.packageDimensionsWidth = referenceSaleGoods.packageDimensionsWidth
                                }
                                saleGoodsRepository.save(newSaleGoods)

                                // 创建新的SaleSkc和SaleSku
                                val saveSkcList = mutableListOf<SaleSkc>()
                                val saveSkuList = mutableListOf<SaleSku>()

                                // 复制参考的SKC数据，创建新的SKC
                                referenceSaleSkcList.forEach { referenceSkc ->
                                    if (referenceSkc.combo == Bool.YES.code) {
                                        log.info { "跳过组合SKC的复制，商品ID:$productId, SPU:${product.spuCode}, SKC ID: ${referenceSkc.saleSkcId}" }
                                        return@forEach
                                    }
                                    val newSaleSkc = SaleSkc().apply {
                                        this.saleSkcId = IdHelper.getId()
                                        this.saleGoodsId = newSaleGoods.saleGoodsId
                                        this.productSkcId = referenceSkc.productSkcId
                                        this.skc = referenceSkc.skc
                                        this.color = referenceSkc.color
                                        this.platformColor = referenceSkc.platformColor
                                        this.colorCode = referenceSkc.colorCode
                                        this.colorAbbrCode = referenceSkc.colorAbbrCode
                                        this.pictures = referenceSkc.pictures
                                        this.combo = referenceSkc.combo
                                        this.state = referenceSkc.state
                                        this.cbPrice = referenceSkc.cbPrice
                                        this.localPrice = referenceSkc.localPrice
                                        this.purchasePrice = referenceSkc.purchasePrice
                                        this.costPrice = referenceSkc.costPrice
                                    }
                                    saveSkcList.add(newSaleSkc)

                                    // 获取对应的计算价格信息
                                    val autoPrice = priceMap[referenceSkc.productSkcId.toString() + country]

                                    // 获取参考SKC对应的所有SKU数据
                                    val referenceSkuList = referenceSkuMap[referenceSkc.saleSkcId] ?: listOf()

                                    // 创建新的SaleSku
                                    referenceSkuList.forEach { referenceSku ->
                                        val newSaleSku = SaleSku().apply {
                                            this.saleSkuId = IdHelper.getId()
                                            this.saleGoodsId = newSaleGoods.saleGoodsId
                                            this.saleSkcId = newSaleSkc.saleSkcId
                                            this.productId = referenceSku.productId
                                            this.productSkcId = referenceSku.productSkcId
                                            this.country = country
                                            this.stockQuantity = referenceSku.stockQuantity
                                            this.sizeName = referenceSku.sizeName
                                            this.lzdSizeName = referenceSku.sizeName
                                            this.shopName = newSaleGoods.shopName
                                            this.brandId = newSaleGoods.brandId
                                            this.brandName = newSaleGoods.brandName
                                            this.enable = referenceSku.enable
                                            // 根据需要使用新国家的价格计算数据
                                            this.salePrice = autoPrice?.salePrice
                                            this.retailPrice = autoPrice?.retailPrice
                                            this.purchaseSalePrice = referenceSku.purchaseSalePrice
                                            this.purchaseRetailPrice = referenceSku.purchaseRetailPrice
                                            this.regularSalePrice = referenceSku.regularSalePrice
                                            this.regularRetailPrice = referenceSku.regularRetailPrice
                                            this.platformCategoryId = newSaleGoods.platformCategoryId
                                            this.barcode = referenceSku.barcode
                                            this.barcodes = referenceSku.barcodes
                                            this.platformCategoryName = newSaleGoods.platformCategoryName
                                            this.publishState = if (referenceSku.enable == Bool.YES.code) {
                                                ProductPublishStateEnum.ACTIVE.code
                                            } else {
                                                referenceSku.publishState
                                            }
                                            this.delayDeliveryDays = referenceSaleGoods.delayDeliveryDays

                                            // 复用参考的sellerSku或生成新的
                                            this.sellerSku = referenceSku.sellerSku ?: generateSellerSkuComponent.generateSellerSku(product, newSaleGoods, newSaleSkc, this)
                                        }
                                        saveSkuList.add(newSaleSku)
                                    }
                                }

                                // 批量保存SKC和SKU提高性能
                                if (saveSkcList.isNotEmpty()) {
                                    saleSkcRepository.saveBatch(saveSkcList)
                                }
                                if (saveSkuList.isNotEmpty()) {
                                    saleSkuRepository.saveBatch(saveSkuList)
                                }

                                // 绑定Lazada条码
                                bindBarcode(product, newSaleGoods)
                                saleGoodsId = newSaleGoods.saleGoodsId
                            }

                            val saleGoods = saleGoodsRepository.getById(saleGoodsId)

                            // 创建/更新 Lazada商品
                            lazadaUpdateProductComponent.updateProduct(shopId, saleGoods)

                            try {
                                // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
                                setBarcodeImage(product.productId!!, shopId, country)
                            } catch (e: Exception) {
                                log.error(e) { "修复barcode关系表的SKC图片异常: ${e.message}" }
                            }

                            null
                        } catch (e: Exception) {
                            log.error(e) { "更新或新增Lazada站点失败: ${e.message}, 商品ID:$productId, SPU:${product.spuCode}d, 店铺ID:$shopId, 国家:$country" }

                            if (e !is PublishGlobalBizException) {
                                runAsync(asyncExecutor) {
                                    productSyncLogRepository.addErrorSyncLog(
                                        product, LAZADA.platformName, shop.shopName, e.getRootMessage(),
                                        PlatformOperatorTypeEnum.ACTIVE.code, country
                                    )
                                }
                            }

                            // 手动标记事务回滚
                            status.setRollbackOnly()
                        }
                    }
                }
        }
    }

    /**
     * 绑定Lazada条码
     * @param product
     * @param saleGoods
     */
    fun bindBarcode(product: Product, saleGoods: SaleGoods) {
        val saleSkcList = saleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        saleSkcList.forEach { saleSkc ->
            // 组合商品 且 组合颜色为空, 则生成生成sellerSku用的comoColorCode
            if (saleSkc.combo == Bool.YES.code && saleSkc.comboColorCode.isNullOrBlank()) {
                val comboColorCode = sellerSkuFlatInfoRepository.generateComboColorCode(saleGoods.spuCode!!, saleSkc.colorCode!!)
                saleSkc.comboColorCode = comboColorCode
                saleSkcRepository.updateById(saleSkc)
            }
            val saleSkuList = saleSkuRepository.findBySaleSkcId(saleSkc.saleSkcId!!).filter { it.enable == Bool.YES.code }
            saleSkuList.forEach { saleSku ->
                if (saleSkc.combo == Bool.YES.code && saleSku.platformSkuId.isNullOrBlank()) {
                    // 组合商品 & 新sku(对于平台), 校验sellerSku是否存在, 存在则重新生成
                    val newSellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, saleSku, saleSkc.comboColorCode)
                    if (!Objects.equals(newSellerSku, saleSku.sellerSku)) {
                        saleSku.sellerSku = newSellerSku
                        saleSkuRepository.updateById(saleSku)
                    }
                }
                productBarcodeService.snapshotSellerSkuToBarcodeByLazada(LAZADA.platformId, saleGoods.shopId!!, saleSkc, saleSku)
            }
        }
    }

    /**
     * 修复barcode关系表的skc图片
     * (后续需要改造成定时任务修复, 通过flat空图片的数据, when 平台回填图片)
     */
    fun setBarcodeImage(productId: Long, shopId: Long, country: String) {
        // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
        val existSaleGoods = saleGoodsRepository.getByProductIdAndShopIdAndCountry(productId, shopId, country)
        if (existSaleGoods != null) {
            // 批量获取所有SKC和SKU信息
            val saleSkcList = saleSkcRepository.findBySaleGoodsId(existSaleGoods.saleGoodsId!!)
            val skcMap = saleSkcList.associateBy { it.saleSkcId }
            val skcIds = saleSkcList.mapNotNull { it.saleSkcId }.toSet()
            val saleSkuList = saleSkuRepository.findBySaleSkcIds(skcIds)

            // 生成 flatPictureMap
            val flatPictureMap = saleSkuList.asSequence()
                .mapNotNull { saleSku ->
                    val flatId = saleSku.sellerSkuFlatId
                    val saleSkc = skcMap[saleSku.saleSkcId]
                    val picture = saleSkc?.pictures?.split(",")?.firstOrNull()
                    if (saleSkc != null && flatId != null && picture != null) {
                        flatId to picture
                    } else {
                        null
                    }
                }
                .toMap()

            // 批量获取 flatInfo 列表
            val flatInfoList = sellerSkuFlatInfoRepository.listByIds(flatPictureMap.keys)

            // 批量更新 flatInfo 信息
            val updatedFlatInfoList = flatInfoList.mapNotNull { flatInfo ->
                val picture = flatPictureMap[flatInfo.sellerSkuFlatId]
                if (picture != null) {
                    flatInfo.apply { image = picture }
                } else {
                    null
                }
            }
            if (updatedFlatInfoList.isNotEmpty()) {
                sellerSkuFlatInfoRepository.updateBatchById(updatedFlatInfoList)
            }
        }
    }

    /**
     * 将视频文件上传到lazada
     */
    fun uploadVideoToLazada(fileUrl: String, token: String, countryCode: String, coverUrl: String): String? {
        val chunkSize = 3 * 1024 * 1024 // 不知道要分多小 文档示例里是3M 那就用3M吧
        val tempFile: File?

        try {
            tempFile = downloadFileFromUrl(fileUrl)
            val fileSize = tempFile.length()

            // 1.调用初始化接口
            val uploadId = lazadaApiService.initVideoUpload(tempFile.name, fileSize, token, countryCode)
            val chunkCount = Math.ceil(fileSize.toDouble() / chunkSize).toInt()

            println("开始分片上传大文件: ${tempFile.name}, 大小: $fileSize bytes, 分片数: $chunkCount")

            // 逐片上传
            val partResult: MutableList<LazadaVideoUploadPartDTO> = mutableListOf()
            FileInputStream(tempFile).use { fis ->
                for (i in 0 until chunkCount) {
                    // 创建临时文件存储当前分片
                    val chunkFile = File.createTempFile("chunk_${i}_", ".tmp")
                    try {
                        // 计算当前分片大小
                        val currentChunkSize = if (i == chunkCount - 1) {
                            // 最后一个分片可能小于chunkSize
                            fileSize - (i * chunkSize)
                        } else {
                            chunkSize.toLong()
                        }

                        // 读取分片数据
                        val buffer = ByteArray(currentChunkSize.toInt())
                        val bytesRead = fis.read(buffer)

                        if (bytesRead > 0) {
                            // 将分片数据写入临时文件
                            Files.write(chunkFile.toPath(), buffer.copyOf(bytesRead))
                            // 调用分片上传接口
                            val partResp = lazadaApiService.uploadVideoBlock(uploadId, i, chunkCount, token, FileItem(chunkFile), countryCode)
                            partResult.add(partResp)
                            log.info { "分片上传成功，partNum: $i, result: ${partResp.toJson()}" }
                        }
                    } finally {
                        // 删除临时分片文件
                        chunkFile.delete()
                    }
                }
            }
            // 分片上传完成 调用获取结果接口
            // 我去哪给你搞封面图啊？
            return if (partResult.isNotEmpty()) {
                lazadaApiService.completeCreateVideo(uploadId, partResult.toJson(), "video", token, coverUrl, countryCode, "pro_main_video")
            }else {
                null
            }
        }catch (e: Exception) {
            log.error(e) { "uploadVideoToLazada error: ${e.message}, fileUrl: $fileUrl" }
        }
        return null
    }

    private fun downloadFileFromUrl(fileUrl: String): File {
        val tempFile = Files.createTempFile(UUID.randomUUID().toString(), ".tmp").toFile()
        try {
            val url = java.net.URL(fileUrl)
            val connection = url.openConnection().apply {
                connectTimeout = 10000 // 10秒连接超时
                readTimeout = 30000    // 30秒读取超时
                setRequestProperty("User-Agent", "Mozilla/5.0")
            }
            connection.getInputStream().use { input ->
                tempFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
        } catch (e: Exception) {
            tempFile.delete() // 出错时删除临时文件
            throw RuntimeException("下载文件失败: ${e.message}", e)
        }

        return tempFile
    }

}
