package tech.tiangong.pop.component.export

import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.repository.BarcodeUnitSnapshotRepository
import tech.tiangong.pop.dao.repository.SellerSkuBarcodeRefRepository
import tech.tiangong.pop.dto.ComboBarcodeExportDTO
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.combo.ComboBarcodePageReq
import tech.tiangong.pop.service.product.ProductComboService
import tech.tiangong.pop.utils.FileExportUtils
import java.io.File
import java.io.IOException

/**
 * 导出组合商品条码
 * <AUTHOR>
 * @date 2025-3-20 16:48:42
 */
@Component
@Slf4j
class ComboBarcodeExportComponent(
    private val barcodeUnitSnapshotRepository: BarcodeUnitSnapshotRepository,
    private val sellerSkuBarcodeRefRepository: SellerSkuBarcodeRefRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val productComboService: ProductComboService,
) : DownloadTaskInterface() {

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ComboBarcodePageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 获取数据
        req.pageNum = 1
        req.pageSize = 500
        val allData: MutableList<ComboBarcodeExportDTO> = mutableListOf()
        while (true) {
            val page = productComboService.barcodePage(req)
            if (page.isNull() || page.list.isEmpty()) {
                break
            }
            req.pageNum += 1

            // 获取所有条码
            val sellerSkuFlatIdList = page.list.mapNotNull { it.sellerSkuFlatId }
            val barcodeRefList = sellerSkuBarcodeRefRepository.getListBySellerSkuFlatIds(sellerSkuFlatIdList)
            val barcodeRefMap = barcodeRefList.groupBy { it.sellerSkuFlatId }
            // 获取每个条码的最新记录
            val barcodeRefIdList = barcodeRefList.mapNotNull { it.barcodeRefId }.distinct()
            val barcodeUnitSnapshotList = barcodeUnitSnapshotRepository.getListByBarcodeRefIdList(barcodeRefIdList)
            val barcodeUnitSnapshotMap = barcodeUnitSnapshotList.groupBy { it.barcodeRefId }

            // 循环sellerSku
            page.list.forEach {
                val barcodeRefList = barcodeRefMap[it.sellerSkuFlatId]
                // 循环barcode
                barcodeRefList?.forEach { barcodeRef ->
                    // 获取最新unit
                    val unit = barcodeUnitSnapshotMap[barcodeRef.barcodeRefId]
                    val barcode = unit?.maxBy { u -> u.createdTime!! }
                    val dto = ComboBarcodeExportDTO().apply {
                        this.image = it.image
                        this.platformName = it.platformName
                        this.shopName = it.shopName
                        this.colorName = it.colorName
                        this.sellerSku = it.sellerSku
                        this.categoryName = it.categoryName
                        this.spuCode = it.spuCode
                        this.barcode = barcodeRef.barcode
                        this.unit = barcode?.unit
                        this.creatorName = it.creatorName
                        this.createdTime = it.createdTime
                        this.reviserName = it.reviserName
                        this.revisedTime = it.revisedTime
                    }
                    allData.add(dto)
                }
            }
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, ComboBarcodeExportDTO::class.java, allData)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}