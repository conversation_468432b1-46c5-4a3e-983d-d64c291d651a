package tech.tiangong.pop.component.lazada

import org.springframework.stereotype.Component
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.dao.entity.ProductSkc
import tech.tiangong.pop.dao.entity.SaleGoods
import tech.tiangong.pop.dao.entity.SaleSkc
import tech.tiangong.pop.dao.repository.ProductSkcRepository
import tech.tiangong.pop.dao.repository.SaleGoodsRepository
import tech.tiangong.pop.dao.repository.SaleSkcRepository

@Slf4j
@Component
class SaleSkcSyncComponent(
    private val productSkcRepository: ProductSkcRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkcRepository: SaleSkcRepository,
) {
    /**
     * 批量同步处理SaleSkc数据
     */
    fun batchSyncProductSkcToSaleSkc(productId: Long, skcList: List<String>? = null) {
        // 获取产品SKC数据
        val productSkcs = if (skcList.isNullOrEmpty()) {
            productSkcRepository.getByProductId(productId)
        } else {
            productSkcRepository.getByProductIdAndSkcCodeIn(productId, skcList)
        }

        if (productSkcs.isEmpty()) {
            log.info { "无需同步的ProductSkc数据, productId: $productId" }
            return
        }

        // 获取所有相关的sale_goods
        val saleGoodsList = saleGoodsRepository.getByProduct(productId)
        if (saleGoodsList.isEmpty()) {
            log.info { "未找到相关的SaleGoods记录, productId: $productId" }
            return
        }

        // 批量处理每个sale_goods
        var createdCount = 0
        var updatedCount = 0

        // 批量处理每个sale_goods
        for (saleGoods in saleGoodsList) {
            // 查询该sale_goods下所有已存在的sale_skc
            val existingSaleSkcs = saleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
            val existingSkcMap = existingSaleSkcs.associateBy { it.skc!! }
            val existingSkcMapByProductSkcId = existingSaleSkcs.filter { it.productSkcId.isNotNull() }.associateBy { it.productSkcId }

            val toUpdate = mutableListOf<SaleSkc>()
            val toCreate = mutableListOf<SaleSkc>()

            // 对每个productSkc进行处理
            for (productSkc in productSkcs) {
                val skc = productSkc.skc!!
                val productSkcId = productSkc.productSkcId!!

                // 优先通过productSkcId查找，找不到再通过skc查找
                val existingSaleSkc = existingSkcMapByProductSkcId[productSkcId] ?: existingSkcMap[skc]

                if (existingSaleSkc != null) {
                    val updateSaleSkc = updateSaleSkc(existingSaleSkc, productSkc)
                    toUpdate.add(updateSaleSkc)
                } else {
                    val newSaleSkc = createSaleSkc(saleGoods, productSkc)
                    toCreate.add(newSaleSkc)
                }
            }

            // 批量保存/更新
            if (toUpdate.isNotEmpty()) {
                saleSkcRepository.updateBatchById(toUpdate)
                updatedCount += toUpdate.size
            }
            if (toCreate.isNotEmpty()) {
                saleSkcRepository.saveBatch(toCreate)
                createdCount += toCreate.size
            }
        }

        if (createdCount > 0 || updatedCount > 0) {
            log.info { "同步完成: productId=$productId, 新增=${createdCount}条, 更新=${updatedCount}条" }
        }
    }

    // 创建新的sale_skc
    private fun createSaleSkc(saleGoods: SaleGoods, productSkc: ProductSkc): SaleSkc {
        val saleSkc = SaleSkc()

        saleSkc.saleGoodsId = saleGoods.saleGoodsId
        saleSkc.productSkcId = productSkc.productSkcId
        saleSkc.skc = productSkc.skc
        saleSkc.color = productSkc.color
        saleSkc.platformColor = productSkc.platformColor
        saleSkc.colorCode = productSkc.colorCode
        saleSkc.colorAbbrCode = productSkc.colorAbbrCode
        saleSkc.pictures = productSkc.pictures
        saleSkc.state = productSkc.state
        saleSkc.cbPrice = productSkc.cbPrice
        saleSkc.localPrice = productSkc.localPrice
        saleSkc.purchasePrice = productSkc.purchasePrice
        saleSkc.costPrice = productSkc.costPrice

        return saleSkc
    }

    /**
     * 更新现有sale_skc
     */
    private fun updateSaleSkc(saleSkc: SaleSkc, productSkc: ProductSkc): SaleSkc {
        val updateSaleSkc = SaleSkc()
        updateSaleSkc.saleSkcId = saleSkc.saleSkcId

        // 只有在值不同时才更新字段
        if (productSkc.skc != saleSkc.skc) {
            updateSaleSkc.skc = productSkc.skc
        }

        if (productSkc.color != saleSkc.color) {
            updateSaleSkc.color = productSkc.color
        }

        if (productSkc.platformColor != saleSkc.platformColor) {
            updateSaleSkc.platformColor = productSkc.platformColor
        }

        if (productSkc.colorCode != saleSkc.colorCode) {
            updateSaleSkc.colorCode = productSkc.colorCode
        }

        if (productSkc.colorAbbrCode != saleSkc.colorAbbrCode) {
            updateSaleSkc.colorAbbrCode = productSkc.colorAbbrCode
        }

        // 图片更新：只在有新图片且不同时更新
        if (!productSkc.pictures.isNullOrBlank() && productSkc.pictures != saleSkc.pictures) {
            updateSaleSkc.pictures = productSkc.pictures
        }

        // 价格相关字段：只在不为空且不同时更新
        if (productSkc.cbPrice != null && productSkc.cbPrice != saleSkc.cbPrice) {
            updateSaleSkc.cbPrice = productSkc.cbPrice
        }

        if (productSkc.localPrice != null && productSkc.localPrice != saleSkc.localPrice) {
            updateSaleSkc.localPrice = productSkc.localPrice
        }

        if (productSkc.purchasePrice != null && productSkc.purchasePrice != saleSkc.purchasePrice) {
            updateSaleSkc.purchasePrice = productSkc.purchasePrice
        }

        if (productSkc.costPrice != null && productSkc.costPrice != saleSkc.costPrice) {
            updateSaleSkc.costPrice = productSkc.costPrice
        }

        // 状态字段：只在不为空且不同时更新
        if (productSkc.state != null && productSkc.state != saleSkc.state) {
            updateSaleSkc.state = productSkc.state
        }

        // 确保更新productSkcId的关联
        if (productSkc.productSkcId != saleSkc.productSkcId) {
            updateSaleSkc.productSkcId = productSkc.productSkcId
        }

        return updateSaleSkc
    }
}
