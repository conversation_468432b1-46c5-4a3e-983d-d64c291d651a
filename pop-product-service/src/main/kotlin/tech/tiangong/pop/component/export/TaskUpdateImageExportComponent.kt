package tech.tiangong.pop.component.export

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.TaskInfo
import tech.tiangong.pop.dao.repository.ProductBatchUpdateImageTaskRepository
import tech.tiangong.pop.dao.repository.TaskInfoRepository
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.ProductBatchUpdateImageFailExcelDTO
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskTypeEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.task.TaskExportByBatchUpdateReq
import tech.tiangong.pop.utils.FileExportUtils
import java.io.File
import java.io.IOException

/**
 *  导出任务逻辑-批量更新-图片
 *
 * <AUTHOR>
 * @date 2025/3/11
 */

@Component
@Slf4j
class TaskUpdateImageExportComponent(
    private val productBatchUpdateImageTaskRepository: ProductBatchUpdateImageTaskRepository,
    private val taskInfoRepository: TaskInfoRepository,
    private val uploaderOssHelper: UploaderOssHelper,
) : DownloadTaskInterface() {

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {
        val req = reqStr?.parseJson<TaskExportByBatchUpdateReq>()
            ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")
        val taskTypeEnum = PlatformProductPullTaskTypeEnum.getByCode(req.taskType)
            ?: throw IllegalArgumentException("类型错误")

        // 验证任务类型
        if (taskTypeEnum != PlatformProductPullTaskTypeEnum.BATCH_UPDATE_IMAGE) {
            throw BusinessException("任务类型错误，非图片更新任务")
        }

        // 获取数据
        val taskList = taskInfoRepository.list(
            KtQueryWrapper(TaskInfo::class.java)
                .between(TaskInfo::createdTime, req.createdStartTime, req.createdEndTime)
                .eq(TaskInfo::taskType, taskTypeEnum.code)
                .eq(TaskInfo::platformId, req.platform!!.platformId)
                .eq(req.taskStatus!=null,TaskInfo::taskStatus, req.taskStatus)
                .orderByDesc(TaskInfo::createdTime)
        )

        val taskIds = taskList.map { it.taskId }
        val dataList = productBatchUpdateImageTaskRepository.listByIds(taskIds)
        // 转为map, key=taskId
        val dataMap = dataList.associateBy { it.taskId }
        // 组装excel dto集合
        val dataDtoList = mutableListOf<ProductBatchUpdateImageFailExcelDTO>()
        taskList.forEach {
            val info = dataMap[it.taskId]
            val vo = ProductBatchUpdateImageFailExcelDTO(
                shopName = info?.shopName,
                shopId = info?.shopId,
                spuCode = info?.spuCode,
                productId = info?.productId,
                reviserName = it.creatorName,
                revisedTime = it.createdTime,
                failReason = it.errorReason,
                taskStatus = PlatformProductPullTaskStatusEnum.getByCode(it.taskStatus!!)?.desc?:"未知状态",
            )
            dataDtoList.add(vo)
        }

        try {
            // 创建Excel文件
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            FileExportUtils.exportToExcel(excelFile, ProductBatchUpdateImageFailExcelDTO::class.java, dataDtoList)
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            log.error(e) { "导出失败:" }
            throw BusinessException("导出失败")
        }
    }
}
