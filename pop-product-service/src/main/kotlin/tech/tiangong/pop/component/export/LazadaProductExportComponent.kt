package tech.tiangong.pop.component.export

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.support.ExcelTypeEnum
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.net.MediaType
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.SpotTypeOpsEnum
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.DownloadTask
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSku
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSpu
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.FileUploadDTO
import tech.tiangong.pop.dto.product.ImportBaseProductDTO
import tech.tiangong.pop.dto.product.ImportProductDTO
import tech.tiangong.pop.enums.DownloadTaskTypeEnum
import tech.tiangong.pop.enums.ProductPricingTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.enums.settings.ProductTitleRuleFieldEnum
import tech.tiangong.pop.helper.UploaderOssHelper
import tech.tiangong.pop.req.product.AttributePairReq
import tech.tiangong.pop.req.product.ProductTitleGenerateReq
import tech.tiangong.pop.req.product.lazada.ProductPendingLazadaPageReq
import tech.tiangong.pop.service.product.ProductTitleGenerateService
import tech.tiangong.pop.service.settings.DownloadTaskService
import tech.tiangong.pop.utils.ExcelUtils
import tech.tiangong.pop.utils.getRootMessage
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 导出-待上架-Lazada商品
 * <AUTHOR>
 * @date 2025-4-16 17:57:52
 */
@Component
@Slf4j
class LazadaProductExportComponent(
    private val productRepository: ProductRepository,
    private val uploaderOssHelper: UploaderOssHelper,
    private val productTemplateLazadaSpuRepository: ProductTemplateLazadaSpuRepository,
    private val productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository,
    private val productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository,
    private val productAttributesRepository: ProductAttributesRepository,
    private val productTitleGenerateService: ProductTitleGenerateService,
    private val downloadTaskService: DownloadTaskService,
    private val shopRepository: ShopRepository,
) : DownloadTaskInterface() {

    /**
     * 创建导出任务
     */
    fun createExportTask(req: ProductPendingLazadaPageReq) {
        try {
            val pageNum: Long = 1
            val pageSize: Long = 1000
            val page = productTemplateLazadaSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (page.records.isEmpty()) {
                throw BusinessException("暂无数据")
            }
            // 创建下载任务
            val dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss", Locale.getDefault()).withZone(ZoneId.systemDefault()))
            val taskName = "导出待上架Lazada商品_$dateStr"
            downloadTaskService.createTask(
                taskName,
                DownloadTaskTypeEnum.PENDING_LISTING_LAZADA_TASK,
                req.toJson()
            );
            log.info { "导出上架失败日志-Lazada待上架-创建下载任务，任务名称：${taskName}" }
        } catch (e: Exception) {
            log.error(e) { "导出上架失败日志-Lazada待上架-创建下载任务, 异常，请求参数：${req.toJson()}" }
            throw BusinessException("提交导出待上架发布失败任务异常：" + e.getRootMessage());
        }
    }

    /**
     * 生成Excel文件
     */
    @Throws(IOException::class)
    override fun export(reqStr: String?, tempDir: File, task: DownloadTask): FileUploadDTO {

        val req = reqStr?.parseJson<ProductPendingLazadaPageReq>() ?: throw BusinessException("导出任务逻辑-参数为空 task=${task.toJson()}")

        // 默认站点
        val defaultCountry = LazadaCountryEnum.MY.code

        // 分页所有数据
        var pageNum: Long = 1
        val pageSize: Long = 1000
        val allLazadaData = mutableListOf<ImportProductDTO>()
        while (true) {
            val pageVo = productTemplateLazadaSpuRepository.pendingPage(Page(pageNum, pageSize), req)
            if (pageVo.records.isEmpty()) {
                break
            }
            pageVo.records.forEach { templateSpu ->
                exportProductByLazada(defaultCountry, templateSpu, allLazadaData)
            }
            pageNum++
        }
        if (allLazadaData.isEmpty()) {
            throw BusinessException("导出数据为空")
        }

        // lazada属性转为动态列
        val resultLazadaMap = ImportBaseProductDTO.convertToMapList(allLazadaData)
        val totalLazada = resultLazadaMap.map { map -> map.values.toList() }

        try {
            // 创建 Excel 文件对象
            val excelFile = File(tempDir, task.taskName + ".xlsx")
            try {
                // 使用 EasyExcel 写入文件
                EasyExcel.write(excelFile)
                    .excelType(ExcelTypeEnum.XLSX)
                    .head(ExcelUtils.extractHeaders(resultLazadaMap))
                    .sheet("sheet1")
                    .doWrite(totalLazada)
            } catch (e: Exception) {
                log.error(e) { "使用 EasyExcel 写入文件时出错" }
                throw BusinessException("导出文件失败，使用 EasyExcel 写入文件时出错", e)
            }
            return uploaderOssHelper.createFileUploadDTO(excelFile, MediaType.MICROSOFT_EXCEL.type())
        } catch (e: IOException) {
            throw BusinessException("导出失败", e)
        }
    }

    /**
     * 处理Lazada数据导出
     */
    private fun exportProductByLazada(defaultCountry: String, templateSpu: ProductTemplateLazadaSpu, allLazadaData: MutableList<ImportProductDTO>) {
        // 获取product
        val product = productRepository.getById(templateSpu.productId)
        if (product == null) {
            return
        }
        // 获取上架shop
        val listingShop = shopRepository.getById(templateSpu.shopId) ?: return

        // 获取Lazada模板SPU
        val spuLazada = productTemplateLazadaSpuRepository.getByProductId(templateSpu.productId!!)
        if (spuLazada == null) {
            return
        }
        // 获取Lazada模板SKC
        val skcLazadaList = productTemplateLazadaSkcRepository.listByLazadaSpuId(spuLazada.lazadaSpuId!!)
            .filter { it.state == Bool.YES.code }
            .filter { it.combo == Bool.NO.code }
        if (skcLazadaList.isEmpty()) {
            return
        }
        // 获取Lazada模板SKU
        val skuLazadaList = productTemplateLazadaSkuRepository.listByLazadaSkcIdList(skcLazadaList.mapNotNull { it.lazadaSkcId })
            .filter { it.enableState == Bool.YES.code }
        if (skuLazadaList.isEmpty()) {
            return
        }

        if (spuLazada.productTitle.isBlank()) {
            val generatedTitleResp = productTitleGenerateService.previewTitle(ProductTitleGenerateReq().apply {
                this.productId = product.productId
                this.product = product
            })
            if (generatedTitleResp.productTitleRuleId.isNotNull()) {
                spuLazada.productTitle = generatedTitleResp.title
                    .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(PlatformEnum.LAZADA) }
                    ?: spuLazada.productTitle
                spuLazada.generatedTitleOverLengthFlag = if (generatedTitleResp.isOverLength) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                spuLazada.generatedTitleMissingFieldsJson = generatedTitleResp.missingFields.toJson()
            }
        }

        skcLazadaList.forEach { skcLazada ->
            val importDto = ImportProductDTO().apply {
                this.supplyMode = SupplyModeEnum.getByCode(product.supplyMode)?.desc
                this.productTitle = spuLazada.productTitle
                this.generatedTitleMissingFields = spuLazada.getParsedGeneratedTitleMissingFields().joinToString(", ") { ProductTitleRuleFieldEnum.getByCode(it)?.desc ?: it }
                this.generatedTitleOverLengthFlag = spuLazada.generatedTitleOverLengthFlag?.let { if (it == YesOrNoEnum.YES.code) "是" else "否" }
                this.spuCode = templateSpu.spuCode
                this.categoryName = product.categoryName
                this.shopName = product.shopName
                this.listingShopName = listingShop.shopName
                this.brandName = spuLazada.brandName
                this.localPrice = skcLazada.localPrice?.toEngineeringString()
                this.purchasePrice = skcLazada.purchasePrice?.toEngineeringString()
                product.spotTypeCode?.let { this.spotType = SpotTypeOpsEnum.getByCode(it)?.desc }
                product.pricingType?.let { this.pricingType = ProductPricingTypeEnum.getByCode(it)?.desc }
                this.stockQuantity = skuLazadaList.firstOrNull { it.country == defaultCountry }?.stockQuantity?.toString()
                this.cmpSalePrice = product.cmpSalePrice?.toEngineeringString()
                this.cmpRetailPrice = product.cmpRetailPrice?.toEngineeringString()
                this.sizeGroupName = product.sizeGroupName
                this.sizeNames = skuLazadaList.mapNotNull { it.sizeName }.distinct().joinToString(",")
                this.skcCode = skcLazada.skc
                this.packageWeight = spuLazada.packageWeight
                this.color = skcLazada.color
            }
            // 设置属性
            val attributes = productAttributesRepository.listByProductId(templateSpu.productId!!, PlatformEnum.LAZADA.platformId)
            if (attributes.isNotEmpty()) {
                val attributePairResps = productRepository.selectAttributesById(
                    attributes.map { att ->
                        AttributePairReq().apply {
                            this.attributeId = att.attributeId
                            this.attributeValueId = att.attributeValueId
                        }
                    }
                )
                if (attributePairResps.isNotEmpty()) {
                    importDto.attributePairResps = attributePairResps
                }
            }
            // 设置站点价格
            if (skuLazadaList.isNotEmpty()) {
                setAllCountryPrice(
                    skcLazada.lazadaSkcId!!,
                    skuLazadaList,
                    importDto
                )
            }
            allLazadaData.add(importDto)
        }
    }

    /**
     * Lazada导出-站点价格设置
     * @param lazadaSkcId
     * @param skuList
     * @param exportProductDTO
     */
    fun setAllCountryPrice(lazadaSkcId: Long, skuList: List<ProductTemplateLazadaSku>, exportProductDTO: ImportProductDTO) {
        for (country in LazadaCountryEnum.getCountryList()) {
            val priceSku = skuList.firstOrNull {
                Objects.equals(it.lazadaSkcId, lazadaSkcId)
                        && Objects.equals(it.country, country)
                        && (it.salePrice != null || it.retailPrice != null)
            }
            if (Objects.nonNull(priceSku)) {
                if (country == LazadaCountryEnum.ID.code) {
                    exportProductDTO.salePriceId = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPriceId = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
                if (country == LazadaCountryEnum.VN.code) {
                    exportProductDTO.salePriceVnd = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPriceVnd = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
                if (country == LazadaCountryEnum.TH.code) {
                    exportProductDTO.salePriceThb = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPriceThb = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
                if (country == LazadaCountryEnum.PH.code) {
                    exportProductDTO.salePricePhp = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPricePhp = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
                if (country == LazadaCountryEnum.MY.code) {
                    exportProductDTO.salePriceMyr = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPriceMyr = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
                if (country == LazadaCountryEnum.SG.code) {
                    exportProductDTO.salePriceSgd = priceSku?.salePrice?.let { priceSku.salePrice?.toEngineeringString() } ?: ""
                    exportProductDTO.retailPriceSgd = priceSku?.retailPrice?.let { priceSku.retailPrice?.toEngineeringString() } ?: ""
                }
            }
        }
    }
}