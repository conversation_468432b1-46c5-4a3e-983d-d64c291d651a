package tech.tiangong.pop.component.ae

import org.apache.commons.lang3.BooleanUtils
import org.springframework.stereotype.Component
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.bo.AeNationalQuoteSkuAdapter
import tech.tiangong.pop.bo.AeProductPriceAlertSkcAdapter
import tech.tiangong.pop.bo.AeProductPriceAlertSkuAdapter
import tech.tiangong.pop.bo.ProductPriceAlertSkuData
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.component.price.PriceAlertValidationParam
import tech.tiangong.pop.component.price.PriceAlertValidationResult
import tech.tiangong.pop.component.price.ProductPriceAlertBaseComponent
import tech.tiangong.pop.config.PigeonMessageProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.enums.ProductTagTargetTypeEnum
import tech.tiangong.pop.external.PigeonMessageClientExternal
import tech.tiangong.pop.resp.product.ProductPriceAlertCheckResp
import tech.tiangong.pop.service.settings.PriceAlertConfigurationService
import tech.tiangong.pop.utils.TransactionUtils

/**
 * AE价格兜底组件
 */
@Slf4j
@Component
class AeProductPriceAlertComponent(
    priceAlertConfigurationService: PriceAlertConfigurationService,
    pigeonMessageClientExternal: PigeonMessageClientExternal,
    productTagRepository: ProductTagRepository,
    pigeonMessageProperties: PigeonMessageProperties,
    imageRepositoryRepository: ImageRepositoryRepository,
    private val saleSkcRepository: AeSaleSkcRepository,
    private val saleSkuRepository: AeSaleSkuRepository,
    private val productTemplateAeSpuRepository: ProductTemplateAeSpuRepository,
) : ProductPriceAlertBaseComponent(
    priceAlertConfigurationService,
    pigeonMessageClientExternal,
    productTagRepository,
    pigeonMessageProperties,
    imageRepositoryRepository,
) {
    /**
     * 上架前校验方法
     */
    fun validateBeforePublish(
        product: Product,
        saleGoods: AeSaleGoods,
        saleSkcList: List<AeSaleSkc>?,
        saleSkuList: List<AeSaleSku>?,
        skipValidation: Int = Bool.NO.code,
        sendNotification: Int = Bool.YES.code,
    ): PriceAlertValidationResult {
        // 检查基本条件
        val spuCode = product.spuCode.takeIf { it.isNotBlank() } ?: throw BusinessException("productId: ${product.productId}-商品SPU编码不能为空")
        val categoryId = product.categoryId ?: throw BusinessException("productId: ${product.productId}-商品分类ID不能为空")
        val countryCode = CountryEnum.US.code // AE默认使用美国站点
        val platformId = PlatformEnum.AE.platformId

        val aeSpu = productTemplateAeSpuRepository.ktQuery()
            .select(ProductTemplateAeSpu::aeSpuId, ProductTemplateAeSpu::spuCode)
            .eq(ProductTemplateAeSpu::spuCode, spuCode)
            .orderByDesc(ProductTemplateAeSpu::createdTime)
            .last("Limit 1").one()

        TransactionUtils.runWithoutTransaction { removeSpuTags(aeSpu) }

        val effectiveSaleSkcList = getOrFetchSaleSkcList(saleSkcList, saleGoods.saleGoodsId!!)
        // 过滤非组合SKC并构建数据模型
        val skcDataList = effectiveSaleSkcList
            .filter { it.combo != Bool.YES.code }
            .mapNotNull { saleSkc ->
                if (saleSkc.skc == null) return@mapNotNull null
                AeProductPriceAlertSkcAdapter(saleSkc, product.productId!!)
            }

        if (skcDataList.isEmpty()) {
            return PriceAlertValidationResult(valid = true, message = "没有有效的非组合SKC")
        }

        val effectiveSaleSkuList = getOrFetchSaleSkuList(saleSkuList, effectiveSaleSkcList)
        // 构建SKU数据映射 - 包含区域定价数据的平铺
        val skuDataMap = buildSkuDataMapWithNationalQuotes(skcDataList, effectiveSaleSkuList)

        // 执行基础价格兜底（包含区域定价校验）
        val baseResult = super.validateBeforePublish(
            PriceAlertValidationParam(
                product = product,
                platformId = platformId,
                categoryId = categoryId,
                countryCode = countryCode,
                skcDataList = skcDataList,
                skuDataMap = skuDataMap,
                skipValidation = skipValidation,
                sendNotification = sendNotification,
            )
        )
        
        // 如果校验不通过，应用标签到TemplateSpu
        if (BooleanUtils.isFalse(baseResult.valid)) {
            // 打标，独立事务，防止外部调用时事务未提交导致标签未生效
            TransactionUtils.runWithoutTransaction { baseResult.checkResult?.let { applySpuTags(aeSpu, it) }}
        }

        return baseResult
    }

    /**
     * 上架后校验方法 - 针对已上架商品
     */
    fun validateAfterPublish(
        product: Product,
        saleGoods: AeSaleGoods,
        saleSkcList: List<AeSaleSkc>?,
        saleSkuList: List<AeSaleSku>?,
        skipValidation: Int = Bool.NO.code,
        sendNotification: Int = Bool.YES.code,
    ): PriceAlertValidationResult {
        val platformId = PlatformEnum.AE.platformId
        val countryCode = CountryEnum.US.code // AE默认使用美国站点
        val categoryId = product.categoryId ?: throw BusinessException("${product.spuCode}-商品分类ID不能为空")

        TransactionUtils.runWithoutTransaction { removeSaleGoodsTag(saleGoods) }

        val effectiveSaleSkcList = getOrFetchSaleSkcList(saleSkcList, saleGoods.saleGoodsId!!)

        // 过滤非组合SKC并构建数据模型
        val skcDataList = effectiveSaleSkcList
            .filter { it.combo != Bool.YES.code }
            .mapNotNull { saleSkc ->
                if (saleSkc.skc == null) return@mapNotNull null
                AeProductPriceAlertSkcAdapter(saleSkc, product.productId!!)
            }

        if (skcDataList.isEmpty()) {
            return PriceAlertValidationResult(valid = true, message = "没有有效的非组合SKC")
        }

        val effectiveSaleSkuList = getOrFetchSaleSkuList(saleSkuList, effectiveSaleSkcList)
        // 构建SKU数据映射 - 包含区域定价数据的平铺
        val skuDataMap = buildSkuDataMapWithNationalQuotes(skcDataList, effectiveSaleSkuList)

        // 执行基础校验（包含区域定价校验）
        val baseResult = super.validateAfterPublish(
            PriceAlertValidationParam(
                product = product,
                platformId = platformId,
                categoryId = categoryId,
                countryCode = countryCode,
                skcDataList = skcDataList,
                skuDataMap = skuDataMap,
                skipValidation = skipValidation,
                sendNotification = sendNotification
            )
        )

        // 如果校验不通过，应用标签到TemplateSpu
        if (BooleanUtils.isFalse(baseResult.valid)) {
            TransactionUtils.runWithoutTransaction { baseResult.checkResult?.let { applySaleGoodsTags(saleGoods, it) } }
        }

        return baseResult
    }

    fun getOrFetchSaleSkcList(
        saleSkcList: List<AeSaleSkc>?,
        saleGoodsId: Long,
    ): List<AeSaleSkc> {
        return saleSkcList?.takeIf { it.isNotEmpty() }
            ?: saleSkcRepository.findBySaleGoodsId(saleGoodsId)
    }

    fun getOrFetchSaleSkuList(
        saleSkuList: List<AeSaleSku>?,
        skcList: List<AeSaleSkc>,
    ): List<AeSaleSku> {
        return saleSkuList?.takeIf { it.isNotEmpty() }
            ?: saleSkuRepository.ktQuery()
                .`in`(AeSaleSku::saleSkcId, skcList.map { it.saleSkcId }.toSet())
                .eq(AeSaleSku::enableState, Bool.YES.code)
                .list()
    }

    /**
     * 应用商品价格异常标签
     */
    fun removeSaleGoodsTag(
        saleGoods: AeSaleGoods,
    ) {
        val saleGoodsId = saleGoods.saleGoodsId!!
        // 删除商品级别的标签
        productTagRepository.removeTag(saleGoodsId, ProductTagTargetTypeEnum.AE_SALE_GOODS_ID, ProductTagEnum.TAG_POP_PRICE_ERROR.code)
        productTagRepository.removeTag(saleGoodsId, ProductTagTargetTypeEnum.AE_SALE_GOODS_ID, ProductTagEnum.TAG_POP_PRICE_INTERCEPT.code)
        log.info { "已移除销售商品[${saleGoodsId}, ${saleGoods.spuCode}]的价格异常标签" }
    }

    /**
     * 应用商品价格异常标签
     */
    fun removeSpuTags(
        lazadaSpu: ProductTemplateAeSpu,
    ) {
        val aeSpuId = lazadaSpu.aeSpuId!!
        // 删除商品级别的标签
        productTagRepository.removeTag(aeSpuId, ProductTagTargetTypeEnum.AE_SPU_ID, ProductTagEnum.TAG_POP_PRICE_ERROR.code)
        productTagRepository.removeTag(aeSpuId, ProductTagTargetTypeEnum.AE_SPU_ID, ProductTagEnum.TAG_POP_PRICE_INTERCEPT.code)
        log.info { "已移除商品aeSpuId: [${aeSpuId}], spuCode: ${lazadaSpu.spuCode}的价格异常标签" }
    }

    /**
     * 上架前价格兜底 - 标签应用到aeSpuId
     */
    fun applySpuTags(
        aeSpu: ProductTemplateAeSpu,
        checkResult: ProductPriceAlertCheckResp,
    ) {
        val aeSpuId = aeSpu.aeSpuId!!
        val allTags = collectAllTags(checkResult)

        // 应用标签到product_id
        log.info { "为商品aeSpuId[${aeSpuId}], spuCode: ${aeSpu.spuCode}应用价格兜底标签，上架前环境" }
        allTags.forEach { (tagKey, tagValues) ->
            tagValues.forEach { tagValue ->
                productTagRepository.addTagByAeSpuId(aeSpuId, tagKey, tagValue)
                log.debug { "为商品aeSpuId[${aeSpuId}]添加标签: $tagKey=$tagValue" }
            }
        }
    }

    /**
     * 标签应用到sale_goods_id
     */
    fun applySaleGoodsTags(
        saleGoods: AeSaleGoods,
        checkResult: ProductPriceAlertCheckResp,
    ) {
        val saleGoodsId = saleGoods.saleGoodsId!!
        val allTags = collectAllTags(checkResult)

        // 应用标签到sale_goods_id
        log.info { "为销售商品saleGoodsId[${saleGoodsId}]应用价格兜底标签，上架后环境" }
        allTags.forEach { (tagKey, tagValues) ->
            tagValues.forEach { tagValue ->
                productTagRepository.addTagByAeSaleGoodsId(saleGoodsId, tagKey, tagValue)
                log.debug { "为销售商品saleGoodsId: ${saleGoodsId}添加标签: $tagKey=$tagValue" }
            }
        }
    }
    
    /**
     * 构建包含区域定价数据的SKU数据映射
     * 将区域价格平铺到skuDataMap中，让基类统一校验
     */
    private fun buildSkuDataMapWithNationalQuotes(
        skcDataList: List<AeProductPriceAlertSkcAdapter>,
        effectiveSaleSkuList: List<AeSaleSku>
    ): Map<Long, List<ProductPriceAlertSkuData>> {
        
        return skcDataList.associate { skcData ->
            val originalSkuList = effectiveSaleSkuList
                .filter { it.saleSkcId == skcData.skcId }
                .map { AeProductPriceAlertSkuAdapter(it) }
            
            // 收集所有SKU数据：原始SKU + 区域定价虚拟SKU
            val allSkuList = mutableListOf<ProductPriceAlertSkuData>()
            
            originalSkuList.forEach { originalSku ->
                // 添加原始SKU（使用默认价格）
                allSkuList.add(originalSku)
                
                // 为每个区域定价创建虚拟SKU
                if (originalSku.hasNationalQuoting) {
                    originalSku.allNationalQuotes.forEach { quote ->
                        val shipToCountry = quote.shipToCountry
                        val price = quote.price
                        
                        // 只为有效的区域价格创建虚拟SKU
                        if (!shipToCountry.isNullOrBlank() && price != null) {
                            val virtualSku = AeNationalQuoteSkuAdapter(
                                originalSku = originalSku,
                                nationalQuote = quote,
                                shipToCountry = shipToCountry
                            )
                            allSkuList.add(virtualSku)
                            log.debug { "为SKU ${originalSku.skuId} 创建区域定价虚拟SKU: $shipToCountry, 价格: $price" }
                        }
                    }
                }
            }
            
            skcData.skcId to allSkuList
        }
    }
}