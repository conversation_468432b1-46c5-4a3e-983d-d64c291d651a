package tech.tiangong.pop.external

import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.design.client.fallback.DigitalPrintingClientFallback
import tech.tiangong.sdp.design.vo.req.digital.DpPushStateUpdateReq

@InnerFeign
@FeignClient(
    value = "sdp-design-service",
    contextId = "digitalPrintingClient",
    fallback = DigitalPrintingClientFallback::class,
    path = "/sdp-design/inner/v1",
    url = "\${domain.ola-api}"
)
interface DigitalPrintingClient {
    @PostMapping("/digital-printing/update/push-state")
    fun updatePushState(
        @RequestBody @Validated req: DpPushStateUpdateReq
    ): DataResponse<Void>?
}