package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.uacs.sdk.client.UserClient
import team.aikero.blade.uacs.sdk.vo.CurrentUserVo
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.exception.BaseBizException

/**
 * 用户中心外部调用
 */
@Slf4j
@Component
class UserClientExternal(
    private val userClient: UserClient,
) {
    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    fun getUserInfo(userId: Long): CurrentUserVo? {
        log.info { "查询用户信息 请求 userId=$userId" }
        val response = userClient.getCurrentUserInfo(userId)
        log.info { "查询用户信息 响应 resp=${response.toJson()}" }
        if (!response.successful) {
            throw BaseBizException("查询用户信息, 异常:" + response.message)
        }
        return response.data
    }
}