package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.common.req.ProductOnlineNoticeReq
import tech.tiangong.sdp.common.resp.GetInspirationOrPickingIdResp
import tech.tiangong.sdp.sdk.client.InspirationClient

/**
 * 灵感
 * <AUTHOR>
 * @date 2025-3-29 23:18:59
 */
@Slf4j
@Component
class InspirationClientExternal(
    private val inspirationClient: InspirationClient,
) {

    /**
     * 获取灵感
     * @param inspirationPickingId
     * @return
     */
    fun getByInspirationOrPickingId(inspirationPickingId: Long): GetInspirationOrPickingIdResp? {
        try {
            log.info { "请求获取灵感，参数inspirationPickingId: $inspirationPickingId" }
            val dataResponse = inspirationClient.getByInspirationOrPickingId(inspirationPickingId)
            log.info { "获取灵感响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("获取灵感失败: ${dataResponse.message}")
            }
            dataResponse.data?.let {
                return it
            }
        } catch (e: Exception) {
            log.error(e) { "获取灵感失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }

    /**
     * 商品上架推送通知灵感
     * @param req
     * @return
     */
    fun productOnlineNotice(req: ProductOnlineNoticeReq) {
        try {
            log.info { "商品上架推送通知灵感, 参数: ${req.toJson()}" }
            val dataResponse = inspirationClient.productOnlineNotice(req)
            log.info { "商品上架推送通知灵感, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("商品上架推送通知灵感, 失败: ${dataResponse.message}")
            }
        } catch (e: Exception) {
            log.error(e) { "商品上架推送通知灵感, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }
}