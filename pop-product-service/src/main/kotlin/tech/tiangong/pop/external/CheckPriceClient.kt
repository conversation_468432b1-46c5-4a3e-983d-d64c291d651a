package tech.tiangong.pop.external

import cn.yibuyun.framework.net.DataResponse
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import tech.tiangong.sdp.clothes.client.fallback.CheckPriceClientFallback
import tech.tiangong.sdp.clothes.vo.req.DesignCodeSpuQuery
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignCodeAllVersionPricingInnerVo

@InnerFeign
@FeignClient(
    value = "sdp-sample-clothes-service",
    contextId = "checkPriceClient",
    fallback = CheckPriceClientFallback::class,
    url = "\${domain.ola-api}"
)
interface CheckPriceClient {

    @PostMapping("/check-price/find-all-version-pricing-by-skc-batch")
    fun findAllVersionPricingBySkcBatch(@RequestBody @Validated var1: DesignCodeSpuQuery): DataResponse<List<DesignCodeAllVersionPricingInnerVo>?>?

}