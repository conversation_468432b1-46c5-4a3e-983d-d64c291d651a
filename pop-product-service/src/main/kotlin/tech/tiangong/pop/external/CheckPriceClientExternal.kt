package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.sdp.clothes.vo.req.DesignCodeSpuQuery
import tech.tiangong.sdp.clothes.vo.resp.checkprice.DesignCodeAllVersionPricingInnerVo

/**
 * 核价查询
 */
@Slf4j
@Component
class CheckPriceClientExternal(
    private val checkPriceClient: CheckPriceClient,
) {

    /**
     * 获取所有版本已核价
     */
    fun findAllVersionPricingBySkcBatch(spuCode: String, skc: String): List<DesignCodeAllVersionPricingInnerVo>? {
        try {
            val req = DesignCodeSpuQuery().apply {
                designCodeSpuReqs = listOf(DesignCodeSpuQuery.DesignCodeSpuReq().apply {
                    this.designCode = skc
                    this.styleCode = spuCode
                })
            }
            log.info { "获取所有版本已核价, 请求参数: ${req.toJson()}" }
            val response = checkPriceClient.findAllVersionPricingBySkcBatch(req)
            if (response == null) {
                log.error { "获取所有版本已核价 失败: 响应为空, req: ${req.toJson()}" }
                throw BusinessException("获取所有版本已核价 失败: 响应为空")
            }
            log.info { "获取所有版本已核价, 响应: code=${response.code}, successful=${response.isSuccessful}, message=${response.message}" }
            if (!response.isSuccessful) {
                log.error { "获取所有版本已核价 失败: ${response.message}, req: ${req.toJson()}" }
                throw BusinessException("获取所有版本已核价 失败: ${response.message}")
            }
            return response.data
        } catch (e: Exception) {
            throw e
        }
    }
}