package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.client.ComfyuiTaskClient
import tech.tiangong.butted.common.req.ComfyuiTaskReq
import tech.tiangong.butted.common.vo.ComfyuiTaskCreateVo
import tech.tiangong.butted.common.vo.ComfyuiTaskVo

/**
 * ComfyUI任务
 */
@Slf4j
@Component
class ComfyuiTaskClientExternal(
    private val comfyuiTaskClient: ComfyuiTaskClient,
) {

    /**
     * 批量新增任务
     * - 商品图：refImgUrl（多个逗号拼接）
     * - 工作流类型：workflowType  传710
     * - busId、busCode 可以传一样的
     */
    fun batchCreate(req: List<ComfyuiTaskReq>): List<ComfyuiTaskCreateVo> {
        try {
            log.info { "[ComfyUI任务]批量新增任务，参数: ${req.toJson()}" }
            val resp = comfyuiTaskClient.batchCreate(req)
            log.info { "[ComfyUI任务]批量新增任务，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data ?: emptyList()
            }
        } catch (e: Exception) {
            log.error(e) { "[ComfyUI任务]批量新增任务 失败，错误信息: ${e.message}" }
            throw e
        }
        return emptyList()
    }

    /**
     * 批量ID查询
     */
    fun listById(taskIds: List<Long>): List<ComfyuiTaskVo> {
        if (taskIds.isEmpty()) {
            log.warn { "[ComfyUI任务]批量ID查询，参数为空" }
            return emptyList()
        }
        try {
            log.info { "[ComfyUI任务]批量ID查询，参数: ${taskIds.toJson()}" }
            val resp = comfyuiTaskClient.listById(taskIds)
            log.info { "[ComfyUI任务]批量ID查询，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data ?: emptyList()
            }
        } catch (e: Exception) {
            log.error(e) { "[ComfyUI任务]批量ID查询 失败，错误信息: ${e.message}" }
            throw e
        }
        return emptyList()
    }

    /**
     * 批量业务ID查询
     */
    fun listByBusId(busIds: List<Long>): List<ComfyuiTaskVo> {
        if (busIds.isEmpty()) {
            log.warn { "[ComfyUI任务]批量业务ID查询，参数为空" }
            return emptyList()
        }
        try {
            log.info { "[ComfyUI任务]批量业务ID查询，参数: ${busIds.toJson()}" }
            val resp = comfyuiTaskClient.listByBusId(busIds)
            log.info { "[ComfyUI任务]批量业务ID查询，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data ?: emptyList()
            }
        } catch (e: Exception) {
            log.error(e) { "[ComfyUI任务]批量业务ID查询 失败，错误信息: ${e.message}" }
            throw e
        }
        return emptyList()
    }
}