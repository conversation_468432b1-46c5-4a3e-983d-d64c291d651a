package tech.tiangong.pop.external

import cn.yibuyun.framework.net.DataResponse
import cn.yibuyun.framework.net.PageRespVo
import cn.yibuyun.framework.web.base.UrlVersionConstant
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import tech.tiangong.sdp.design.constant.DesignPathConstant
import tech.tiangong.sdp.design.vo.req.style.PopCreateInfoReq
import tech.tiangong.sdp.design.vo.req.style.SpuBatchReq
import tech.tiangong.sdp.design.vo.req.style.SpuInnerQuery
import tech.tiangong.sdp.design.vo.resp.style.*

@FeignClient(
    value = DesignPathConstant.APPLICATION_NAME,
    path = DesignPathConstant.CONTEXT_PATH + UrlVersionConstant.INNER + UrlVersionConstant.VERSION_V1,
    contextId = "sdpSpuClient"
)
@InnerFeign
interface SdpSpuClient {

    /**
     * 分页查询
     *
     * @param query 分页对象
     * @return PageRespVo<SpuInnerQueryVo>
    </SpuInnerQueryVo> */
    @PostMapping("/spu/page")
    fun page(@RequestBody @Validated query: SpuInnerQuery): DataResponse<PageRespVo<SpuInnerQueryVo>>

    /**
     * 根据spu编号获取spu信息
     *
     * @param spuCode spu编码
     * @return 响应结果
     */
    @GetMapping("/spu/{spuCode}")
    fun getSpuInfo(@PathVariable(value = "spuCode") spuCode: String): DataResponse<DesignStyleInnerVo>

    /**
     * 根据spu批量获取spu信息
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/spu/batch-info")
    fun batchSpuInfo(@RequestBody @Validated req: SpuBatchReq): DataResponse<MutableList<DesignStyleInnerVo>>


    /**
     * 根据spu,返回spu是否已取消
     *
     * @param spuCodeList spu编码
     * @return 响应结果
     */
    @PostMapping("/spu/isCancel")
    fun isCancelSpu(@RequestBody spuCodeList: MutableList<String>): DataResponse<MutableList<SpuCancelResp>>

    /**
     * 根据spu查询pop创建商品的信息
     *
     * @param req 入参
     * @return 响应结果
     */
    @PostMapping("/spu/pop-style-info")
    fun queryStyleInfo4Pop(@RequestBody @Validated req: PopCreateInfoReq): DataResponse<MutableList<PopCreateProductVo>>

    /**
     * 查询spu类型
     * Params: spuCodeList – spu编码集合, 最大1000
     * Returns: spu类型
     */
    @PostMapping("/spu/query-style-type")
    fun queryStyleType(@RequestBody spuCodeList: List<String>): DataResponse<List<StyleTypeInnerVo>>
}