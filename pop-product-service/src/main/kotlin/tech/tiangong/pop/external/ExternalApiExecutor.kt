package tech.tiangong.pop.external

import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.common.exception.AliexpressApiException

@Slf4j
object ExternalApiExecutor {
    /**
     * 执行速卖通API，包装try catch 输出AliexpressApiException
     */
    inline fun <T> executeAliexpressApi(apiName: String, block: () -> T): T {
        try {
            return block()
        } catch (e: Exception) {
            log.error(e) { "调用AE接口 [$apiName] 失败: ${e.message}" }
            throw AliexpressApiException("调用AE接口 [$apiName] 失败", e)
        }
    }
}