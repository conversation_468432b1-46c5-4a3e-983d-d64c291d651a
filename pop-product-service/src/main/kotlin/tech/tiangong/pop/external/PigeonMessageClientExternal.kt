package tech.tiangong.pop.external

import org.apache.commons.lang3.BooleanUtils
import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.pigeon.common.dto.MessagePayload
import team.aikero.pigeon.sdk.PigeonMessageClient
import tech.tiangong.pop.config.PigeonMessageProperties

/**
 * 飞鸽消息客户端封装
 * @date 2025-05-24
 */
@Slf4j
@Component
class PigeonMessageClientExternal(
    private val pigeonMessageClient: PigeonMessageClient,
    private val messageProperties: PigeonMessageProperties,
) {
    /**
     * 发送消息
     *
     * @param templateCode 模板编码
     * @param receivers 接收人ID列表
     * @param params 消息参数
     * @param queryKey 查询关键字（可选）
     * @return 消息唯一标识
     */
    fun sendMessage(
        templateCode: String,
        receivers: List<String>,
        params: Map<String, String>,
        queryKey: String? = null
    ): String? {
        // 如果消息发送功能被禁用，则直接返回null
        if (BooleanUtils.isFalse(messageProperties.enabled)) {
            log.info { "消息发送功能已禁用，跳过发送" }
            return null
        }

        try {
            val payload = MessagePayload(
                queryKey = queryKey,
                templateCode = templateCode,
                receivers = receivers.ifEmpty { messageProperties.defaultReceivers },
                params = params.toMutableMap()
            )
            log.info { "发送消息，参数: ${payload.toJson()}" }

            // 尝试发送消息，支持重试
            var response = pigeonMessageClient.send(payload)
            var retryCount = 0

            // 失败重试逻辑
            while (!response.successful && retryCount < messageProperties.sendRetryCount) {
                retryCount++
                log.warn { "发送消息失败，正在进行第${retryCount}次重试..." }
                response = pigeonMessageClient.send(payload)
            }

            log.info { "发送消息响应: code=${response.code}, successful=${response.successful}" }

            if (!response.successful) {
                log.error { "发送消息失败: ${response.message}" }
                throw RuntimeException("发送消息失败: ${response.message}")
            }

            return response.data
        } catch (e: Exception) {
            log.error(e) { "发送消息异常，错误信息: ${e.message}" }
            throw e
        }
    }

    /**
     * 发送价格兜底通知
     *
     * @param receivers 接收人ID列表
     * @param title 通知标题
     * @param content 通知内容（markdown格式）
     * @param queryKey 查询关键字（可选）
     * @return 消息唯一标识
     */
    fun sendPriceFloorNotify(
        receivers: List<String>,
        title: String,
        content: String,
        queryKey: String? = null
    ): String? {
        val params = mapOf(
            "title" to title,
            "content" to content
        )
        return sendMessage(
            messageProperties.priceFloorNotifyTemplate,
            receivers,
            params,
            queryKey
        )
    }

}