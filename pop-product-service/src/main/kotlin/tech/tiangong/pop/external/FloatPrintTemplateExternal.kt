package tech.tiangong.pop.external

import com.zjkj.aigc.client.FloatPrintTemplateClient
import com.zjkj.aigc.common.req.FloatPrintTemplateInnerPageReq
import com.zjkj.aigc.common.resp.FloatPrintTemplateInnerDetailVO
import com.zjkj.aigc.common.resp.FloatPrintTemplateInnerPageVO
import org.springframework.stereotype.Component
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson

@Slf4j
@Component
class FloatPrintTemplateExternal(
    private val floatPrintTemplateClient: FloatPrintTemplateClient
) {


    fun page(req: FloatPrintTemplateInnerPageReq): PageVo<FloatPrintTemplateInnerPageVO>? {
        try {
            log.info { "版型号查询列表, 参数: ${req.toJson()}" }
            val dataResponse = floatPrintTemplateClient.page(req)
            log.info { "版型号查询列表, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("版型号查询列表, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "版型号查询列表, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }



    fun getByCode(code: String): FloatPrintTemplateInnerDetailVO? {
        try {
            log.info { "版型号查询详情, 参数: $code" }
            val dataResponse = floatPrintTemplateClient.getByPatternCode(code)
            log.info { "版型号查询详情, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("版型号查询详情, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "版型号查询详情, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


}