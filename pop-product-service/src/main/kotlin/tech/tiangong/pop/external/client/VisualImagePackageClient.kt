package tech.tiangong.pop.external.client
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.design.client.fallback.VisualImagePackageClientFallback
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageResp

@InnerFeign
@FeignClient(
    value = "sdp-design-service",
    contextId = "visualImagePackageClient",
    fallback = VisualImagePackageClientFallback::class,
    path = "/sdp-design/inner/v1",
    url = "\${domain.ola-api}"
)
interface VisualImagePackageClient {

    /**
     * 根据款式编码获取图片包信息
     *
     * @param styleCode 款式编码
     * @return 视觉图包响应
     */
    @GetMapping("/visual-image-package/get-image-package-by-style-code/{styleCode}")
    fun getImagePackageByStyleCode(@PathVariable("styleCode") styleCode: String): DataResponse<VisualImagePackageResp>?
}