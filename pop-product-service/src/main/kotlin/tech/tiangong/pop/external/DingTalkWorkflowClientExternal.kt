package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import team.aikero.pigeon.common.dto.WorkflowInstance
import team.aikero.pigeon.common.dto.WorkflowInstanceDetail
import team.aikero.pigeon.sdk.DingtalkWorkflowClient

/**
 * 钉钉审批流
 * @date 2025-5-27 14:42:31
 */
@Slf4j
@Component
class DingTalkWorkflowClientExternal(
    private val dingtalkWorkflowClient: DingtalkWorkflowClient,
) {

    /**
     * 创建工作流
     *
     *
     * @param req 流程实例
     * @return 消息唯一标识
     */
    fun create(req: WorkflowInstance): String {
        try {
            log.info { "请求创建审批流，参数: ${req.toJson()}" }
            val dataResponse = dingtalkWorkflowClient.create(req)
            log.info { "创建审批流响应: code=${dataResponse.code}, successful=${dataResponse.successful}" }

            if (!dataResponse.successful) {
                log.error { "创建审批流失败 resp=${dataResponse.toJson()}" }
                throw RuntimeException("创建审批流失败: ${dataResponse.message}")
            }
            return dataResponse.data ?: ""
        } catch (e: Exception) {
            log.error(e) { "创建审批流失败，错误信息: ${e.message}" }
            throw e
        }
    }

    /**
     * 获取工作流状态
     *
     * @param instanceId 流程唯一标识
     * @return 状态信息
     */
    fun status(instanceId: String): WorkflowInstanceDetail? {
        try {
            log.info { "请求获取审批流状态，参数: $instanceId" }
            val dataResponse = dingtalkWorkflowClient.status(instanceId)
            log.info { "获取审批流状态响应: code=${dataResponse.code}, successful=${dataResponse.successful}" }

            if (!dataResponse.successful) {
                log.error { "获取审批流失败 resp=${dataResponse.toJson()}" }
                throw RuntimeException("获取审批流状态失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "获取审批流状态失败，错误信息: ${e.message}" }
            throw e
        }
    }

    /**
     * 撤回
     *
     * @param instanceId 流程唯一标识
     * @param userId 操作人id
     * @return 撤回结果
     */
    fun withdraw(instanceId: String, userId: Long, remark: String): Boolean {
        try {
            log.info { "请求撤回审批流，参数: $instanceId" }
            val dataResponse = dingtalkWorkflowClient.withdraw(instanceId, userId, remark)
            log.info { "撤回审批流响应: code=${dataResponse.code}, successful=${dataResponse.successful}" }

            if (!dataResponse.successful) {
                log.error { "撤回审批流失败 resp=${dataResponse.toJson()}" }
                throw RuntimeException("撤回审批流失败: ${dataResponse.message}")
            }
            return dataResponse.data ?: false
        } catch (e: Exception) {
            log.error(e) { "撤回审批流失败，错误信息: ${e.message}" }
            throw e
        }
    }
}