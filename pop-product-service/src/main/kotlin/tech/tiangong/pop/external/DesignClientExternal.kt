package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.ProductImagePackageStateEnum
import tech.tiangong.pop.common.enums.SdpStyleTypeEnum
import tech.tiangong.pop.external.client.VisualImagePackageClient
import tech.tiangong.pop.external.client.VisualTaskClient
import tech.tiangong.sdp.design.enums.visual.VisualTaskStateEnum
import tech.tiangong.sdp.design.vo.req.digital.DpPushStateUpdateReq
import tech.tiangong.sdp.design.vo.resp.style.SpuCancelResp
import tech.tiangong.sdp.design.vo.resp.visual.VisualImagePackageResp

/**
 * 推送给SDP design
 *
 * @date 2025-05-15
 */
@Slf4j
@Component
class DesignClientExternal(
    private val digitalPrintingClient: DigitalPrintingClient,
    private val visualImagePackageClient: VisualImagePackageClient,
    private val visualTaskClient: VisualTaskClient,
    private val sdpSpuClient: SdpSpuClient
) {
    /**
     * 更新商品推送POP状态
     * @param req DpPushStateUpdateReq
     * @throws RuntimeException 失败时抛出异常
     */
    fun updatePushState(req: DpPushStateUpdateReq) {
        try {
            log.info { "更新商品推送POP状态, 请求参数: ${req.toJson()}" }
            val response = digitalPrintingClient.updatePushState(req)
            if (response == null) {
                log.error { "更新商品推送POP状态失败: 响应为空, req: ${req.toJson()}" }
                throw BusinessException("更新商品推送POP状态失败: 响应为空")
            }
            log.info { "更新商品推送POP状态, 响应: code=${response.code}, successful=${response.successful}, message=${response.message}" }
            if (!response.successful) {
                log.error { "更新商品推送POP状态失败: ${response.message}, req: ${req.toJson()}" }
                throw BusinessException("更新商品推送POP状态失败: ${response.message}")
            }
        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * 查询视觉中心图片包
     * @param styleCode SPU编码
     * @return 视觉图包对象
     * @throws BusinessException 查询失败时抛出异常
     */
    fun getImagePackageByStyleCode(styleCode: String): VisualImagePackageResp? {
        try {
            val response = visualImagePackageClient.getImagePackageByStyleCode(styleCode)

            if (response == null) {
                log.error { "查询视觉中心图片包失败: 响应为空, styleCode: $styleCode" }
                throw BusinessException("查询视觉中心图片包失败: 响应为空")
            }

            if (!response.successful) {
                log.error { "查询视觉中心图片包失败: ${response.message}, styleCode: $styleCode, response=${response.toJson()}" }
                throw BusinessException("查询视觉中心图片包失败: ${response.message}")
            }

            return response.data
        } catch (e: Exception) {
            log.error(e) { "查询视觉中心图片包异常: styleCode=$styleCode" }
            throw e
        }
    }

    fun getStyleTypeBySpuCodes(spuCodes: List<String>): Map<String,Int> {
        val result = mutableMapOf<String,Int>()
        try{
            withSystemUser {
                log.info { "查询款式类型,参数:${spuCodes.toJson()}" }
                val response = sdpSpuClient.queryStyleType(spuCodes)
                log.info { "查询款式类型,结果:${response.toJson()}" }

                if (!response.isSuccessful) {
                    log.error { "查询款式类型失败: ${response.message}, styleCodes: ${spuCodes.toJson()}, response=${response.toJson()}" }
                    throw BusinessException("查询款式类型失败: ${response.message}")
                }
                val spuStyleTypeList = response.data
                val respData = mutableMapOf<String, Int>()
                if (spuStyleTypeList.isNotEmpty()) {
                    //spu对应的款式类型
                    val styleCodeToStyleTypeMap = spuStyleTypeList.associateBy { it.styleCode }
                    styleCodeToStyleTypeMap.forEach { (styleCode, spuTostyleType) ->
                        respData[styleCode] = spuTostyleType.styleType
                    }
                }
                spuCodes.forEach { spuCode ->
                    result.put(spuCode, respData.getOrDefault(spuCode, SdpStyleTypeEnum.UNKNOWN.code))
                }
            }
        }catch (e:Exception){
            log.error(e) { "查询款式类型异常:${e.message}" }
        }
        return result
    }

    fun getImagePackageStateBySpuCodes(spuCodes: List<String>): Map<String,Int> {
        val result = mutableMapOf<String,Int>()
        try{
            withSystemUser {
                log.info { "查询视觉任务,参数:${spuCodes.toJson()}" }
                val response = visualTaskClient.listLatestBySpu(spuCodes)
                log.info { "查询视觉任务,结题:${response.toJson()}" }

                if (!response.successful) {
                    log.error { "查询视觉任务失败: ${response.message}, styleCodes: ${spuCodes.toJson()}, response=${response.toJson()}" }
                    throw BusinessException("查询视觉任务失败: ${response.message}")
                }
                val visualTaskList = response.data!!
                val respData = mutableMapOf<String, Int>()
                if (visualTaskList.isNotEmpty()) {
                    //按spu分组，判断如果有待分配或者进行中的任务，则图包状态为更新中
                    val styleCodeToVisualTaskListMap = visualTaskList.groupBy { it.styleCode }
                    styleCodeToVisualTaskListMap.forEach { (styleCode, visualTaskList) ->
                        if (visualTaskList.any { task -> task.isCancel == 0 && (task.state == VisualTaskStateEnum.WAITING.code || task.state == VisualTaskStateEnum.DOING.code) }) {
                            respData[styleCode] = ProductImagePackageStateEnum.UPDATING.code
                        }
                    }
                }
                spuCodes.forEach { spuCode ->
                    result.put(spuCode, respData.getOrDefault(spuCode, SdpStyleTypeEnum.UNKNOWN.code))
                }
            }
        }catch (e:Exception){
            log.error(e) { "查询SPU视觉任务状态异常:${e.message}" }
        }
        return result
    }

    fun isCancelSpu(styleCode: String): kotlin.Boolean? {
        var spuCancelResp = SpuCancelResp()
        try {
            log.info{"【判断SPU是否已被取消】请求参数 styleCode:${styleCode}"}
            val response = sdpSpuClient.isCancelSpu(mutableListOf(styleCode))
            log.info{"【判断SPU是否已被取消】响应结果 response:${response.toJson()}"}
            if (!response.isSuccessful()) {
                log.error {"【判断SPU是否已被取消】失败:${response.message}"}
            }
            spuCancelResp = response.getData()!!.get(0)
        } catch (e: Exception) {
            e.printStackTrace()
            log.error{"【判断SPU是否已被取消】异常:${e.message}"}
        }
        return spuCancelResp.statue
    }
}
