package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.butted.client.MarketStyleTaskInnerClient
import tech.tiangong.butted.common.req.label.MarketStyleTaskBatchCreateReq
import tech.tiangong.butted.common.req.label.MarketStyleTaskQueryReq
import tech.tiangong.butted.common.req.label.MarketStyleTaskReq
import tech.tiangong.butted.common.vo.label.MarketStyleTaskBatchCreateVo
import tech.tiangong.butted.common.vo.label.MarketStyleTaskVo

/**
 * 识别任务
 */
@Slf4j
@Component
class MarketTaskClientExternal(
    private val marketStyleTaskInnerClient: MarketStyleTaskInnerClient,
) {

    /**
     * 创建识别风格-批量
     */
    fun batchCreateStyle(req: MarketStyleTaskBatchCreateReq): List<MarketStyleTaskBatchCreateVo> {
        try {
            log.info { "[识别风格]批量新增任务，参数: ${req.toJson()}" }
            val resp = marketStyleTaskInnerClient.batchCreate(req)
            log.info { "[识别风格]批量新增任务，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data ?: emptyList()
            }
        } catch (e: Exception) {
            log.error(e) { "[识别风格]批量新增任务 失败，错误信息: ${e.message}" }
            throw e
        }
        return emptyList()
    }

    /**
     * 创建识别风格-单个
     */
    fun createStyle(inputImageUrl: String): Long? {
        try {
            val req = MarketStyleTaskReq(
                inputImage = inputImageUrl,
            )
            log.info { "[识别风格]新增任务，参数: ${req.toJson()}" }
            val resp = marketStyleTaskInnerClient.create(req)
            log.info { "[识别风格]新增任务，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data
            }
        } catch (e: Exception) {
            log.error(e) { "[识别风格]新增任务 失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }

    /**
     * 详情
     */
    fun detailStyle(taskId: Long): MarketStyleTaskVo? {
        try {
            log.info { "[识别风格]查询任务详情，参数: $taskId" }
            val resp = marketStyleTaskInnerClient.detail(taskId)
            log.info { "[识别风格]查询任务详情，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data
            }
        } catch (e: Exception) {
            log.error(e) { "[识别风格]查询任务详情 失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }

    /**
     * 批量查询
     */
    fun qetByIdsStyle(taskIds: List<Long>): List<MarketStyleTaskVo>? {
        try {
            val req = MarketStyleTaskQueryReq(
                taskIds = taskIds
            )
            log.info { "[识别风格]批量查询任务详情，参数: ${req.toJson()}" }
            val resp = marketStyleTaskInnerClient.qeryByIds(req)
            log.info { "[识别风格]批量查询任务详情，结果: ${resp.toJson()}" }
            if (resp.successful) {
                return resp.data
            }
        } catch (e: Exception) {
            log.error(e) { "[识别风格]批量查询任务详情 失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }
}