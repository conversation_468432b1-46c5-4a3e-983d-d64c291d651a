package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.ofp.sdk.client.EtaConfigClient
import tech.tiangong.ofp.sdk.client.ItemStatusClient
import tech.tiangong.ofp.open.common.req.SpuItemStatusQueryReq
import tech.tiangong.ofp.open.common.resp.EtaConfigResp
import tech.tiangong.ofp.open.common.resp.SpuItemStatusQueryResp

/**
 * ETA相关接口
 * <AUTHOR>
 * @date 2025-4-8 16:01:00
 */
@Slf4j
@Component
class EtaClientExternal(
    private val etaConfigClient: EtaConfigClient,
    private val itemStatusClient: ItemStatusClient,
) {
    /**
     * 获取ETA配置
     * @return
     */
    fun getAllEtaConfig(): List<EtaConfigResp>? {
        try {
            log.info { "获取ETA配置, 请求参数: 无需参数" }
            val dataResponse = etaConfigClient.list()
            log.info { "获取ETA配置, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("获取ETA配置, 失败: ${dataResponse.message}")
            }
            dataResponse.data?.let {
                return it
            }
        } catch (e: Exception) {
            log.error(e) { "获取ETA配置, 失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }

    /**
     * 根据SPU和站点查询商品状态
     * @return
     */
    fun querySpuItemStatus(req: List<SpuItemStatusQueryReq>): List<SpuItemStatusQueryResp>? {
        try {
            log.info { "根据SPU和站点查询商品状态, 请求参数: ${req.toJson()}" }
            val dataResponse = itemStatusClient.querySpuItemStatus(req)
            log.info { "根据SPU和站点查询商品状态, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("根据SPU和站点查询商品状态, 失败: ${dataResponse.message}")
            }
            dataResponse.data?.let {
                return it
            }
        } catch (e: Exception) {
            log.error(e) { "根据SPU和站点查询商品状态, 失败，错误信息: ${e.message}" }
            throw e
        }
        return null
    }
}