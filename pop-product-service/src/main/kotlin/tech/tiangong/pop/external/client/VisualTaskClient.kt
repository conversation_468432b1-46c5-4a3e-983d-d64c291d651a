package tech.tiangong.pop.external.client
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import team.aikero.blade.core.annotation.feign.InnerFeign
import team.aikero.blade.core.protocol.DataResponse
import tech.tiangong.sdp.design.client.fallback.VisualImagePackageClientFallback
import tech.tiangong.sdp.design.vo.req.visual.VisualTaskInnerVo

@InnerFeign
@FeignClient(
    value = "sdp-design-service",
    contextId = "visualImagePackageClient",
    fallback = VisualImagePackageClientFallback::class,
    path = "/sdp-design/inner/v1",
    url = "\${domain.ola-api}"
)
interface VisualTaskClient {

    /**
     * spu最新视觉任务查询
     *
     * @param spuCodeList spu编码集合, 最大1000
     * @return 视觉任务信息
     */
    @PostMapping("/visual-task/latest-by-spu")
    fun listLatestBySpu(@RequestBody spuCodeList: List<String>): DataResponse<List<VisualTaskInnerVo>>
}