package tech.tiangong.pop.external

import com.alibaba.fastjson2.toJSONString
import org.springframework.stereotype.Component
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.client.TemuClient
import tech.tiangong.eis.temu.req.*
import tech.tiangong.eis.temu.resp.*


@Slf4j
@Component
class EisCenterClientExternal(
    private val temuClient: TemuClient,
) {


    /**
     * 上传商品
     */
    fun temuGoodsAdd(req: TemuGoodsAddReq): TemuGoodsAddResp? {
        try {
            log.info { "TEMU商品上架推送, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsAdd(req)
            log.info { "TEMU商品上架, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU商品上架, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU商品上架, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 上传货品图片
     */
    fun goodsImageUpload(req: TemuGoodsImageUploadReq): TemuGoodsImageUploadResp? {
        try {
            log.info { "TEMU上传货品图片推送" }
            val dataResponse = temuClient.goodsImageUpload(req)
            log.info { "TEMU上传货品图片, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU上传货品图片, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU上传货品图片, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 生成规格
     */
    fun goodsSpecCreate(req: TemuGoodsSpecCreateReq): TemuGoodsSpecCreateResp? {
        try {
            log.info { "TEMU生成规格, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsSpecCreate(req)
            log.info { "TEMU生成规格, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU生成规格, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU生成规格, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 生成尺码表模板
     */
    fun goodsSizeChartsTemplateCreate(req: TemuSizeChartsTemplateCreateReq): TemuSizeChartsTemplateCreateResp? {
        try {
            log.info { "TEMU生成尺码表模板, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsSizeChartsTemplateCreate(req)
            log.info { "TEMU生成尺码表模板, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU生成尺码表模板, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU生成尺码表模板, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 虚拟库存编辑接口
     */
    fun goodsQuantityUpdate(req: TemuGoodsQuantityUpdateReq) {
        try {
            log.info { "TEMU虚拟库存编辑接口, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsQuantityUpdate(req)
            log.info { "TEMU虚拟库存编辑接口, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU虚拟库存编辑接口, 失败: ${dataResponse.message}")
            }
        } catch (e: Exception) {
            log.error(e) { "TEMU虚拟库存编辑接口, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 查询货品生命周期状态
     */
    fun productSearch(req: TemuProductSearchReq): TemuProductSearchResp? {
        try {
            log.info { "TEMU查询货品生命周期状态接口, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.productSearch(req)
            log.info { "TEMU查询货品生命周期状态接口, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU查询货品生命周期状态接口, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU查询货品生命周期状态接口, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }

    /**
     * 发起图片更新任务
     */
    fun goodsEditPicturesSubmit(req: TemuGoodsEditPicturesSubmitReq){
        try {
            log.info { "TEMU发起图片更新任务, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsEditPicturesSubmit(req)
            log.info { "TEMU发起图片更新任务, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU发起图片更新任务, 失败: ${dataResponse.message}")
            }
        } catch (e: Exception) {
            log.error(e) { "TEMU发起图片更新任务, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 压缩货品图片
     */
    fun pictureCompressionGet(req: TemuPictureCompressionGetReq): TemuPictureCompressionGetResp? {
        try {
            log.info { "TEMU压缩货品图片推送, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.pictureCompressionGet(req)
            log.info { "TEMU压缩货品图片, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("TEMU压缩货品图片, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "TEMU压缩货品图片, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


    /**
     * 虚拟库存查询接口
     */
    fun goodsQuantityGet(req: TemuGoodsQuantityGetReq): TemuGoodsQuantityGetResp? {
        try {
            log.info { "虚拟库存查询接口, 参数: ${req.toJson()}" }
            val dataResponse = temuClient.goodsQuantityGet(req)
            log.info { "虚拟库存查询接口, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("虚拟库存查询接口, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        } catch (e: Exception) {
            log.error(e) { "虚拟库存查询接口, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }

    /**
     * 上传视频接口
     */
    fun videoUpload(fileUrl: String, shortCode: String, region: String) :String?{
        try {
            log.info { "上传视频接口, 请求: fileUrl:$fileUrl shortCode:$shortCode region:$region" }
            val dataResponse = temuClient.videoUpload(fileUrl, shortCode, region)
            log.info { "上传视频接口, 响应: code=${dataResponse.code}, successful=${dataResponse.successful}, data=${dataResponse.toJson()}" }
            if (!dataResponse.successful) {
                throw RuntimeException("上传视频接口, 失败: ${dataResponse.message}")
            }
            return dataResponse.data
        }catch (e: Exception) {
            log.error(e) { "上传视频接口, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }

    fun getVideoUploadResult(req: TemuGoodsVideoUploadResultGetReq) : DataResponse<TemuVideoUploadResultGetResp?> {
        try {
            log.info { "获取视频上传结果接口, 请求: req:${req.toJson()}" }
            val dataResponse = temuClient.videoUploadResultGet(req)
            log.info { "获取视频上传结果接口, 响应: ${dataResponse.toJSONString()}" }
            // 由于需要业务逻辑判断重试 所以这里返回原始的结果
            return dataResponse
        }catch (e: Exception) {
            log.error(e) { "获取视频上传结果接口, 失败, 错误信息: ${e.message}" }
            throw e
        }
    }


}