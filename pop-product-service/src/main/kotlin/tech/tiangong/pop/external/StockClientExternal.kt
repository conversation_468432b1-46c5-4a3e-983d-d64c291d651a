package tech.tiangong.pop.external

import org.springframework.stereotype.Component
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.ofp.open.common.req.StockSpuQuantityQueryReq
import tech.tiangong.ofp.open.common.resp.StockSpuQuantityQueryResp
import tech.tiangong.ofp.sdk.client.StockClient
import tech.tiangong.pop.common.exception.BaseBizException

/**
 * 库存客户端
 * <AUTHOR>
 * @date 2025-2-24 15:33:19
 */
@Slf4j
@Component
class StockClientExternal(
    private val stockClient: StockClient,
) {

    /**
     * 查询实际库存
     * @param req
     * @return
     */
    fun querySpuQuantity(req: StockSpuQuantityQueryReq): StockSpuQuantityQueryResp? {
        log.info { "库存查询,req=${req.toJson()}" }
        val response = stockClient.querySpuQuantity(req)
        log.info { "库存查询querySpuQuantity, resp=${response.toJson()}" }
        if (!response.successful) {
            throw BaseBizException("查询库存失败, 异常:" + response.message)
        }
        return response.data
    }
}