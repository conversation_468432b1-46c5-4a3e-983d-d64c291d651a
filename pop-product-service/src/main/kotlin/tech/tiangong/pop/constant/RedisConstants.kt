package tech.tiangong.pop.constant

/**
 * <AUTHOR>
 * @since 2024/11/21 18:56
 */
object RedisConstants {
    const val REDIS_KEY_PREFIX = "tiangong:pop:"
    private const val REDIS_KEY_LOCK_PREFIX = "${REDIS_KEY_PREFIX}lock:"
    /** 条码最大SPU编号分布式锁 */
    const val BARCODE_MAX_SPU_CODE_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}barcode:max_spu_code:"
    /** 条码最大SKC编号分布式锁 */
    const val BARCODE_MAX_SKC_CODE_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}barcode:max_skc_code:"
    /** 条码SKU生成锁 */
    const val BARCODE_SKU_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}barcode:sku:"
    /** 导入商品SPU索引生成锁 */
    const val IMPORT_PRODUCT_SPU_INDEX_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}import:product_spu_index:"
    /** 待上架-导入商品任务锁 */
    const val IMPORT_AWAIT_PRODUCT_TASK_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}import:await_product_task:"
    /** 商品中心-导入商品任务锁 */
    const val IMPORT_PRODUCT_CENTER_TASK_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}import:product_center_task:"
    /** 类目属性映射更新锁 */
    const val CATEGORY_ATTR_MAPPING_UPDATE_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}category:attr_mapping_update:"
    /** Lazada平台拉取商品任务锁 */
    const val LAZADA_PULL_PRODUCT_TASK_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}lazada:pull_product_task:"
    /** POP商品任务全局锁（如分发等） */
    const val PRODUCT_TASK_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}product_task:"
    /** AliExpress类目同步锁 */
    const val AE_CATEGORY_SYNC_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}aliexpress:category_sync:"
    /** AliExpress商品信息同步/更新锁 */
    const val AE_UPDATE_PRODUCT_SYNC_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}aliexpress:update_product_sync:"
    /** POP推送到AliExpress创建商品锁 */
    const val AE_CREATE_PRODUCT_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}aliexpress:create_product:"
    /** POP推送到Lazada创建商品锁 */
    const val LAZADA_CREATE_PRODUCT_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}lazada:create_product:"
    /** 上游推送创建商品入口锁 */
    const val UPSTREAM_CREATE_PRODUCT_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}upstream:create_product:"
    /** Alibaba类目同步锁 */
    const val ALIBABA_CATEGORY_SYNC_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}alibaba:category_sync:"
    /** 下载管理锁 */
    const val DOWNLOAD_TASK_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}download_task:"
    /** mq消息锁 */
    const val FACTORY_MQ_LOCK = "${REDIS_KEY_LOCK_PREFIX}factory_mq:"
    /** 类目属性复制锁 */
    const val CATEGORY_ATTR_COPY_LOCK = "${REDIS_KEY_LOCK_PREFIX}category_attr_copy:"
    /** 价格预警配置更新全局锁 */
    const val PRICE_ALERT_CONFIG_GLOBAL_UPDATE_LOCK = "${REDIS_KEY_LOCK_PREFIX}global:price_alert_config:update"
    /** 商品标题热词轮询 */
    const val HOT_WORD_INDEX_PREFIX = "${REDIS_KEY_PREFIX}product:title:hot_word:index:"
    /** POP推送到TEMU创建商品锁 */
    const val TEMU_CREATE_PRODUCT_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}temu:create_product:"
    /** POP更新图片到TEMU创建锁 */
    const val TEMU_UPDATE_IMAGE_LOCK: String = "${REDIS_KEY_LOCK_PREFIX}temu:update_product_image:"
    /** 视觉中心图片同步锁 */
    const val VISUAL_IMAGE_SYNC_LOCK = "${REDIS_KEY_LOCK_PREFIX}image_sync:"
    /** 视觉中心SPU任务同步锁 */
    const val VISUAL_TASK_STATE_SYNC_LOCK = "${REDIS_KEY_LOCK_PREFIX}visual_task_state_sync:"
}
