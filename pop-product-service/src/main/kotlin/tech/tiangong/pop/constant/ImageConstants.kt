package tech.tiangong.pop.constant

import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.image.ImagePackCollectionDTO
import tech.tiangong.pop.enums.ImagePackTypeEnum
import tech.tiangong.pop.enums.ImageTypeEnum
import tech.tiangong.pop.resp.image.ImageAniVo
import kotlin.reflect.KProperty1


/**
 * 图片相关常量定义
 */
object ImageConstants {

    /**
     * 必要的图片类型
     */
    object Required {
        val COMMON = setOf(
            ImageTypeEnum.SIZE_CHART,
            ImageTypeEnum.CATEGORY_CHART,
            ImageTypeEnum.PRODUCT_MAIN,
            ImageTypeEnum.SIZING_TABLE,
            ImageTypeEnum.PRODUCT_DETAIL,
            ImageTypeEnum.FABRIC,
        )

        val COMMON_CODES = COMMON.map { it.code + "1" }
    }

    /**
     * 文件名相关常量
     */
    object FileName {
        const val PICTURE_KIT = "picture_kit"  // 图套前缀
        const val SEP = "-"             // 分隔符
        const val UNKNOWN = "Unknown"        // 未知
        const val MATERIAL = "Material"        // 生产资料文件名
        const val DEFAULT_MAIN_IMAGE_SUFFIX = "301"  // 默认主图文件名后缀标识 xxx-301/301
    }

    object Extension {
        const val JPG = "jpg"
        const val PNG = "png"
        val SUPPORTED_FORMATS = listOf("jpg", "jpeg", "png", "gif", "bmp")
    }

    object Url {
        /** 多个图片链接拼接的分隔符，英文逗号 */
        const val DELIM = ","
    }

    /**
     * 字段映射常量（原有ImageTypeEnum映射）
     */
    object FieldMapping {
        /**
         * 字段名称与图片类型的映射
         */
        val IMAGE_FIELD_MAP: Map<String, ImageTypeEnum> = mapOf(
            "img101" to ImageTypeEnum.SIZE_CHART,
            "img201" to ImageTypeEnum.CATEGORY_CHART,
            "img301" to ImageTypeEnum.PRODUCT_MAIN,
            "img401" to ImageTypeEnum.SIZING_TABLE,
            "img501" to ImageTypeEnum.PRODUCT_DETAIL,
            "img601" to ImageTypeEnum.FABRIC,
            "img701" to ImageTypeEnum.SELLING_POINT,
            "img801" to ImageTypeEnum.FABRIC_COLOR,
            "img901" to ImageTypeEnum.CUSTOMER_SNAP,
        )

        /**
         * 图片类型到字段名称的映射
         */
        val TYPE_TO_FIELD: Map<ImageTypeEnum, String> by lazy {
            IMAGE_FIELD_MAP.entries.associate { (key, value) -> value to key }
        }

        /**
         * 图片类型和ImageCollectionDTO字段的映射关系
         */
        val TYPE_TO_IMAGES: Map<ImageTypeEnum, KProperty1<ImageCollectionDTO, List<ImageAniVo>>> = mapOf(
            ImageTypeEnum.SIZE_CHART     to ImageCollectionDTO::sizeChartImages,
            ImageTypeEnum.CATEGORY_CHART to ImageCollectionDTO::categoryChartImages,
            ImageTypeEnum.PRODUCT_MAIN   to ImageCollectionDTO::productMainImages,
            ImageTypeEnum.SIZING_TABLE   to ImageCollectionDTO::sizingTableImages,
            ImageTypeEnum.PRODUCT_DETAIL to ImageCollectionDTO::productDetailImages,
            ImageTypeEnum.FABRIC         to ImageCollectionDTO::fabricImages,
            ImageTypeEnum.SELLING_POINT  to ImageCollectionDTO::sellingPointImages,
            ImageTypeEnum.FABRIC_COLOR   to ImageCollectionDTO::fabricColorImages,
            ImageTypeEnum.CUSTOMER_SNAP  to ImageCollectionDTO::customerShowcaseImages,
        )
    }

    /**
     * 图包规则映射常量（新增ImagePackTypeEnum映射）
     */
    object PackMapping {
        /**
         * 字段名称与图包类型的映射
         */
        val PACK_FIELD_MAP: Map<String, ImagePackTypeEnum> = mapOf(
            "img101" to ImagePackTypeEnum.VERSION_CHART,
            "img201" to ImagePackTypeEnum.CATEGORY_CHART,
            "img301" to ImagePackTypeEnum.PRODUCT_MAIN,
            "img401" to ImagePackTypeEnum.SIZE_CHART,
            "img501" to ImagePackTypeEnum.PRODUCT_DETAIL,
            "img601" to ImagePackTypeEnum.FABRIC,
            "img701" to ImagePackTypeEnum.SELLING_POINT,
            "img801" to ImagePackTypeEnum.FABRIC_COLOR,
            "img901" to ImagePackTypeEnum.CUSTOMER_SHOW,
        )

        /**
         * 图包类型到字段名称的映射
         */
        val PACK_TYPE_TO_FIELD: Map<ImagePackTypeEnum, String> by lazy {
            PACK_FIELD_MAP.entries.associate { (key, value) -> value to key }
        }

        /**
         * 图包类型和ImagePackCollectionDTO字段的映射关系
         */
        val TYPE_TO_PACK_IMAGES: Map<ImagePackTypeEnum, KProperty1<ImagePackCollectionDTO, List<ImageAniVo>>> = mapOf(
            ImagePackTypeEnum.VERSION_CHART  to ImagePackCollectionDTO::sizeChartImages,     // 101 版型表
            ImagePackTypeEnum.CATEGORY_CHART to ImagePackCollectionDTO::categoryChartImages, // 201 类目表
            ImagePackTypeEnum.PRODUCT_MAIN   to ImagePackCollectionDTO::productMainImages,  // 301 商品首图
            ImagePackTypeEnum.SIZE_CHART     to ImagePackCollectionDTO::sizingTableImages,  // 401 尺码表
            ImagePackTypeEnum.PRODUCT_DETAIL to ImagePackCollectionDTO::productDetailImages,// 501~506 商品详情图
            ImagePackTypeEnum.FABRIC         to ImagePackCollectionDTO::fabricImages,       // 601~60X 面料图
            ImagePackTypeEnum.SELLING_POINT  to ImagePackCollectionDTO::sellingPointImages, // 701 卖点细节
            ImagePackTypeEnum.FABRIC_COLOR   to ImagePackCollectionDTO::fabricColorImages,  // 801 面料颜色
            ImagePackTypeEnum.CUSTOMER_SHOW  to ImagePackCollectionDTO::customerShowcaseImages, // 901 买家秀
        )
    }
}