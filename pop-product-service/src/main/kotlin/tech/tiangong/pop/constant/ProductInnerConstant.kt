package tech.tiangong.pop.constant

import tech.tiangong.pop.common.enums.PlatformEnum

/**
 * 商品内部常量
 */
object ProductInnerConstant {

    /** 商品标题扩展长度最大值 */
    const val TITLE_MAX_EXTEND_LEN: Int = 50

    /** 平台商品标题最大长度 */
    private val PLATFORM_TITLE_MAX_LENGTH_MAP = mapOf(
        PlatformEnum.LAZADA to 255,
        PlatformEnum.AE to 128,
        PlatformEnum.TEMU to 500
    )

    /** 默认商品标题最大长度 取最小*/
    private val DEFAULT_TITLE_MAX_LENGTH = PLATFORM_TITLE_MAX_LENGTH_MAP.values.min()

    /** 获取平台对应的商品标题最大长度 为空则用默认最大长度*/
    fun getTitleMaxLength(platform: PlatformEnum?): Int =
        platform?.let { PLATFORM_TITLE_MAX_LENGTH_MAP[it] } ?: DEFAULT_TITLE_MAX_LENGTH

    /** 商品标题分隔符 */
    const val TITLE_SEP: String = " "

    /**
     * 款式风格名称 有两级，分隔
     */
    const val CLOTHING_STYLE_NAME_SPE: String = "-"

    /** product表字段设置的商品标题最大长度 */
    const val DB_MAX_TITLE_LENGTH: Int = 500

    /** 错误消息键 */
    const val ERROR_MESSAGE_KEY: String = "errorMessage"
}