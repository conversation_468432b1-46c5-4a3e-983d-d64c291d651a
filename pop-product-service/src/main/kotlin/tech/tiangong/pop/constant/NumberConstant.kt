package tech.tiangong.pop.constant

import java.math.BigDecimal

object NumberConstants {
    // 基础整数
    const val ZERO = 0
    const val ONE = 1
    const val TEN = 10
    const val HUNDRED = 100
    const val THOUSAND = 1000

    // BigDecimal 版本（避免重复创建对象）
    val BD_ZERO: BigDecimal = BigDecimal.ZERO
    val BD_ONE: BigDecimal = BigDecimal.ONE
    val BD_TEN: BigDecimal = BigDecimal.TEN
    val BD_HUNDRED: BigDecimal = BigDecimal("100")

    // 时间相关
    const val SECONDS_IN_MINUTE = 60
    const val MINUTES_IN_HOUR = 60
    const val HOURS_IN_DAY = 24
}