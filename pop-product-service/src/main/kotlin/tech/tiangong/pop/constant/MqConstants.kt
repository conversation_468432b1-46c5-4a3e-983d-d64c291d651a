package tech.tiangong.pop.constant

/**
 * MQ 常量类
 * <AUTHOR>
 * @since 2024/11/21 18:56
 */
object MqConstants {
    /**
     * 创建商品
     */
    const val EXCHANGE_POP_PRODUCT_CREATE: String = "e.tiangong.pop.create_product"
    const val KEY_POP_PRODUCT_CREATE: String = "r.tiangong.pop.create_product"
    const val QUEUE_POP_PRODUCT_CREATE: String = "q.tiangong.pop.create_product"

    /**
     * 批量创建商品
     */
    const val BATCH_EXCHANGE_POP_PRODUCT_CREATE: String = "e.tiangong.pop.batch_create_product"
    const val BATCH_KEY_POP_PRODUCT_CREATE: String = "r.tiangong.pop.batch_create_product"
    const val BATCH_QUEUE_POP_PRODUCT_CREATE: String = "q.tiangong.pop.batch_create_product"

    /**
     * 数码印花图片变更推送
     */
    const val EXCHANGE_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE: String = "e.tiangong.pop.product.login_num_img_change"
    const val KEY_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE: String = "r.tiangong.pop.login_num_img_change"
    const val QUEUE_POP_PRODUCT_LOGIN_NUM_IMG_CHANGE: String = "q.tiangong.pop.login_num_img_change"

    /**
     * 发布lazada平台
     */
    const val EXCHANGE_POP_PRODUCT_PUBLISH: String = "e.tiangong.pop.pulish_productb12"
    const val KEY_POP_PRODUCT_PUBLISH: String = "r.tiangong.pop.pulish_productb12"
    const val QUEUE_POP_PRODUCT_PUBLISH: String = "q.tiangong.pop.pulish_productb12"

    /**
     * 下架
     */
    const val EXCHANGE_POP_PRODUCT_OFFLINE: String = "e.tiangong.pop.offline_product"
    const val KEY_POP_PRODUCT_OFFLINE: String = "r.tiangong.pop.offline_product"
    const val QUEUE_POP_PRODUCT_OFFLINE: String = "q.tiangong.pop.offline_product"

    /**
     * 更新SKC和取消SKC 接收MQ
     */
    const val EXCHANGE_POP_PRODUCT_UPDATE_SKC_CANCLE: String = "e.sdp_design.prototype_manage_cancel_skc"
    const val KEY_POP_PRODUCT_UPDATE_SKC_CANCLE: String = "r.sdp_design.prototype_manage_cancel_skc"
    const val QUEUE_POP_PRODUCT_UPDATE_SKC_CANCLE: String = "q.tiangong.pop.update_skc_cancle_product"

    const val EXCHANGE_POP_ERROR_ORDER_SAVE: String = "e.tiangong.pop.save_error_order"
    const val KEY_POP_ERROR_ORDER_SAVE: String = "r.tiangong.pop.save_error_order"
    const val QUEUE_POP_ERROR_ORDER_SAVE: String = "q.tiangong.pop.save_error_order"

    const val EXCHANGE_POP_LAZADA_PRICE_UPDATE: String = "e.tiangong.pop.update_lazada_price"
    const val KEY_POP_LAZADA_PRICE_UPDATE: String = "r.tiangong.pop.update_lazada_price"
    const val QUEUE_POP_LAZADA_PRICE_UPDATE: String = "q.tiangong.pop.update_lazada_price"

    const val EXCHANGE_POP_LAZADA_QUANTITY_UPDATE: String = "e.tiangong.pop.update_lazada_quantity"
    const val KEY_POP_LAZADA_QUANTITY_UPDATE: String = "r.tiangong.pop.update_lazada_quantity"
    const val QUEUE_POP_LAZADA_QUANTITY_UPDATE: String = "q.tiangong.pop.update_lazada_quantity"

    /**
     * 商品条码创建
     */
    const val EXCHANGE_GENERATION_BARCODE: String = "e.sdp_design.prototype_operate_create_bar_code"
    const val KEY_GENERATION_BARCODE: String = "r.sdp_design.prototype_operate_create_bar_code"
    const val QUEUE_GENERATION_BARCODE: String = "q.pop_sku_code_generation.barcode"

    /**
     * 商品条码配置
     */
    const val EXCHANGE_GENERATION_SKU_CODE: String = "techtiangong.pop.generation.sku.code"
    const val EVENT_GENERATION_SKU_CODE: String = "pop_sku_code_generation"

    /**
     * 下架 V2
     */
    const val EXCHANGE_POP_PRODUCT_OFFLINE_V2: String = "e.tiangong.pop.offline_product_v2"
    const val KEY_POP_PRODUCT_OFFLINE_V2: String = "r.tiangong.pop.offline_product_v2"
    const val QUEUE_POP_PRODUCT_OFFLINE_V2: String = "q.tiangong.pop.offline_product_v2"

    /**
     * 库存变动
     */
    const val EXCHANGE_POP_PRODUCT_INV_CHANGE: String = "e.tiangong.pop.inv_change"
    const val KEY_POP_PRODUCT_INV_CHANGE: String = "r.tiangong.pop.inv_change"
    const val QUEUE_POP_PRODUCT_INV_CHANGE: String = "q.tiangong.pop.inv_change"

    /**
     * 标签变动
     */
    const val EXCHANGE_POP_PRODUCT_TAG_CHANGE: String = "e.tiangong.pop.tag_change"
    const val KEY_POP_PRODUCT_TAG_CHANGE: String = "r.tiangong.pop.tag_change"
    const val QUEUE_POP_PRODUCT_TAG_CHANGE: String = "q.tiangong.pop.tag_change"

    /**
     * 图片创建/更新
     */
    const val EXCHANGE_POP_PRODUCT_IMAGE_UPSERT: String = "e.tiangong.pop.image_upsert"
    const val KEY_POP_PRODUCT_IMAGE_UPSERT: String = "r.tiangong.pop.image_upsert"
    const val QUEUE_POP_PRODUCT_IMAGE_UPSERT: String = "q.tiangong.pop.image_upsert"

    /**
     * SPU视觉任务状态
     */
    const val EXCHANGE_VISUAL_TASK_STATE_NOTICE: String = "e.sdp_design.visual_task_state_notice"
    const val KEY_VISUAL_TASK_STATE_NOTICE: String = "r.sdp_design.visual_task_state_notice"
    const val QUEUE_VISUAL_TASK_STATE_NOTICE: String = "q.sdp_design.visual_task_state_notice"

    /**
     * spu视频变更
     */
    const val EXCHANGE_POP_PRODUCT_VIDEO_UPSERT: String = "e.tiangong.pop.video_upsert"
    const val KEY_POP_PRODUCT_VIDEO_UPSERT: String = "r.tiangong.pop.video_upsert"
    const val QUEUE_POP_PRODUCT_VIDEO_UPSERT: String = "q.tiangong.pop.video_upsert"
}
