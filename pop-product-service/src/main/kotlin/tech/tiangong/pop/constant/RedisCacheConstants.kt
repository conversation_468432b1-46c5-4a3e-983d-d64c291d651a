package tech.tiangong.pop.constant

import tech.tiangong.pop.constant.RedisConstants.REDIS_KEY_PREFIX

/**
 * Redis缓存常量
 */
object RedisCacheConstants {
    private const val REDIS_KEY_CACHE_PREFIX = "${REDIS_KEY_PREFIX}cache:"
    const val AE_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:category_attributes:"
    const val AE_FREIGHT_TEMPLATE_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:freight:template:"
    const val AE_PROMISE_TEMPLATE_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:promise:template:"
    const val AE_MSR_LIST_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:msr:list:"
    const val AE_MANUFACTURE_LIST_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:manufacture:list:"
    const val AE_PRODUCT_GROUPS_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:product:groups:"
    const val ALIBABA_CATEGORY_TREE_KEY = "${REDIS_KEY_CACHE_PREFIX}alibaba:categoryTree"
    const val ALIBABA_PLATFORM_CATEGORY_TREE_KEY = "${REDIS_KEY_CACHE_PREFIX}alibaba:platformCategoryTree"
    const val AE_SELLER_RELATION_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}aliexpress:seller_relation:"

    const val TEMU_CATEGORY_ATTRIBUTES_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}temu:category_attributes:"
    const val TEMU_CATEGORY_TREE_CACHE_KEY = "${REDIS_KEY_CACHE_PREFIX}temu:category_tree"
    const val TEMU_PLATFORM_CATEGORY_TREE_CACHE_KEY = "${REDIS_KEY_CACHE_PREFIX}temu:platform_category_tree"

    const val IMAGE_PACK_RULE_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}image_pack_rule:"
    const val IMAGE_PACK_RULE_BY_SHOP_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}image_pack_rule:shop:"
    const val IMAGE_PACK_RULE_BY_PLATFORM_CACHE_KEY_PREFIX = "${REDIS_KEY_CACHE_PREFIX}image_pack_rule:platform:"
}
