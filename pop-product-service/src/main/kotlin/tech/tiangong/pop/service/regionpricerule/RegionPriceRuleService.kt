package tech.tiangong.pop.service.regionpricerule

import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dto.regionpricerule.ProductRegionPriceRuleDto
import tech.tiangong.pop.req.product.v2.AePricingCalculateReq
import tech.tiangong.pop.resp.product.manage.CalAeRetailPriceForPageResp
import tech.tiangong.pop.resp.regionpricerule.*
import java.math.BigDecimal

interface RegionPriceRuleService {
    /**
     * 查询商品的区域定价相关配置
     */
    fun getProductRegionPriceRule(product: Product):ProductRegionPriceRuleDto
    /**
     * 查询商品的区域定价配置-毛利率配置
     */
    fun getGrossMarginByProduct(product:Product):BigDecimal?

    fun listLogisticsRuleResp():List<RegionPriceRuleLogisticsResp>

    fun listStorageRuleResp():List<RegionPriceRuleStorageResp>

    fun getTaxRateRule(): RegionPriceRuleTaxRateResp?

    fun listRejectRateRule(): List<RegionPriceRuleRejectRateResp>

    fun listAdRule(): List<RegionPriceRuleAdResp>

    fun listCommissionRule(): List<RegionPriceRuleCommissionResp>

    fun listWithdrawRule(): List<RegionPriceRuleWithdrawResp>

    fun listGrossMarginRule(): List<RegionPriceRuleGrossMarginResp>

    fun listMarketingRule(): List<RegionPriceRuleMarketingResp>

    fun listFreightRateRule(): List<RegionPriceRuleFreightRateResp>

    fun listDiscountRateRule(): List<RegionPriceRuleDiscountRateResp>

    fun prepareCalculateReq(product:Product):AePricingCalculateReq

    fun calAeSaleAndRetailPriceForPage(productIds: List<Long>): List<CalAeRetailPriceForPageResp>

    fun invalidateAllCache()
}
