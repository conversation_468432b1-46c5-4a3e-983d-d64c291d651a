package tech.tiangong.pop.service


import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.dto.mq.ErrorOrderSaveMqDto
import tech.tiangong.pop.req.order.ErrorOrderBatchProcessReq
import tech.tiangong.pop.req.order.ErrorOrderPageReq
import tech.tiangong.pop.resp.order.ErrorOrderPageResp
import jakarta.servlet.http.HttpServletResponse

/**
 * 异常订单
 * <AUTHOR>
 * @date 2024/11/18 11:06
 */
interface ErrorOrderService {

    /**
     * 保存订单数据
     * @param req
     */
    fun saveOrder(req: ErrorOrderSaveMqDto)
    /**
     * 列表分页
     * @param req 请求对象
     * @return
     */
    fun page(req: ErrorOrderPageReq): PageVo<ErrorOrderPageResp>

    /**
     * 批量处理
     * @param req 请求对象
     * @return
     */
    fun batchProcess(req: ErrorOrderBatchProcessReq)

    /**
     * 导出
     * @param req 请求对象
     * @return
     */
    fun export(response: HttpServletResponse, req: ErrorOrderPageReq)
}
