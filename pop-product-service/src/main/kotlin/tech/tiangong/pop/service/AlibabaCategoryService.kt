package tech.tiangong.pop.service

import tech.tiangong.pop.common.resp.AlibabaCategoryResp
import tech.tiangong.pop.dto.AlibabaCategoryTreeNodeDTO
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo

/**
 * 阿里 1688 品类服务
 */
interface AlibabaCategoryService {
    /**
     * 同步1688类目数据
     */
    fun syncAlibabaCategories()

    /**
     * 获取1688类目树
     */
    fun getCategoryTree(): List<AlibabaCategoryTreeNodeDTO>

    /**
     * 根据1688类目ID查询类目信息
     */
    fun listByPlatformCategoryIds(categoryIds: MutableList<Long>?): List<AlibabaCategoryResp>
    /**
     * 获取平台通用格式的类目树
     */
    fun getPlatformCategoryTree(): List<PublishPlatformCategoryVo>
}