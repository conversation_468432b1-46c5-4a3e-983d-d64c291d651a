package tech.tiangong.pop.service.settings

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import com.aliyun.alimt20181012.Client
import com.aliyun.alimt20181012.models.TranslateGeneralRequest
import com.aliyun.tea.TeaException
import com.aliyun.teaopenapi.models.Config
import com.aliyun.teautil.models.RuntimeOptions
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.util.concurrent.RateLimiter
import jakarta.annotation.Resource
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.core.env.Environment
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.enums.settings.ProductTitleDictionarySysFieldEnum
import java.io.File
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern

/**
 * 标题字典/标签数据初始化服务
 * 1. 调用API获取字典数据
 * 2. 将中文字典名翻译为英文
 * 3. 输出Excel表格
 */
@Slf4j
@Service
class ProductTitleDictionaryTranslateExcelService(
    private val okHttpClient: OkHttpClient,
    private val environment: Environment,
    private val objectMapper: ObjectMapper,
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService
) {
    companion object {
        private const val DICT_API_URL = "https://prod-nest-api.tiangong.tech/sys-admin/web/dict/tree-list"
        private const val AUTH_TOKEN = ""
        private val CHINESE_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]")
        private val ENGLISH_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\-]+$")
        private val DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
        private const val CATEGORY_DICT_CODE = "clothing_category"
        private const val STYLE_DICT_CODE = "jv-style"
    }

    /**
     * 用于缓存翻译结果，避免重复翻译
     */
    private val translationCache = ConcurrentHashMap<String, String>()

    /**
     * 字典API请求体
     */
    data class DictApiRequest(
        val dictCodes: List<String>,
        // 传不同的系统代码可能权限不同，POP会少了fit和season，这里因为基本都是用SDP推过来的字段作为翻译值，所以直接传SDP
        val consumerCode: String = "SDP"
    )

    /**
     * 字典API响应
     */
    data class ApiResponse<T>(
        val successful: Boolean,
        val code: Int,
        val message: String,
        val data: T
    )

    /**
     * 字典项
     */
    data class DictItem(
        val id: String,
        val dictName: String,
        val dictCode: String,
        val sorted: Int,
        val state: Int,
        val level: Int,
        val remark: String?,
        val createdTime: Long,
        val revisedTime: Long,
        val reviserName: String,
        val labels: List<Any> = emptyList(),
        val attributes: List<Any> = emptyList(),
        val children: List<DictItem>? = null
    )

    /**
     * Excel输出数据结构
     */
    data class DictExcelData(
        @ExcelProperty("字段")
        var field: String = "",

        @ExcelProperty("关联字段")
        var relatedField: String = "",

        @ExcelProperty("字段值")
        var dictValue: String = "",

        @ExcelProperty("拓展1")
        var ext1: String = "",

        @ExcelProperty("拓展2")
        var ext2: String = "",

        @ExcelProperty("拓展3")
        var ext3: String = ""
    )

    /**
     * 简单值字典项
     */
    data class SimpleDictItem(
        val code: String,
        val value: String
    )

    /**
     * 新方法: 从JSON数据直接生成字典翻译Excel文件
     *
     * @param jsonDataMap 字段名称与JSON数据的映射，格式为 {"版型": "[{\"code\": \"V240402093\", \"value\": \"合体\"}...]", ...}
     * @return 生成的Excel文件路径
     */
    fun generateDictionaryExcelFromJson(jsonDataMap: Map<String, List<SimpleDictItem>>): String {
        log.info { "开始从JSON数据生成字典翻译Excel文件" }
        val startTime = Instant.now()

        // 创建RateLimiter，限制QPS为50
        val rateLimiter = RateLimiter.create(50.0)

        // 存储Excel行数据
        val excelDataList = mutableListOf<DictExcelData>()

        // 处理每个字段的JSON数据
        jsonDataMap.forEach { (fieldName, jsonData) ->
            try {
                // 解析JSON数据为SimpleDictItem列表

                // 获取系统字段枚举
                val sysField = getSysFieldByName(fieldName)

                if (sysField != null) {
                    // 处理每个值项
                    jsonData.forEach { item ->
                        val translation = if (needsTranslation(item.value)) {
                            capitalizeFirstLetter(translateTextWithRateLimit(item.value, rateLimiter))
                        } else {
                            capitalizeFirstLetter(item.value)
                        }

                        // 构建Excel数据
                        val excelData = DictExcelData(
                            field = sysField.desc,
                            relatedField = sysField.desc,
                            dictValue = item.value,
                            ext1 = translation,
                            ext2 = "",
                            ext3 = ""
                        )

                        excelDataList.add(excelData)
                    }
                } else {
                    log.warn { "未找到字段 '$fieldName' 对应的系统字段枚举" }
                }
            } catch (e: Exception) {
                log.error { "处理字段 '$fieldName' 的JSON数据时出错: ${e.message}" }
            }
        }

        log.info { "处理完成，共生成${excelDataList.size}条Excel数据" }

        // 生成Excel文件
        val outputFilePath = generateOutputFilePath()
        EasyExcel.write(outputFilePath, DictExcelData::class.java)
            .sheet("字典翻译")
            .doWrite(excelDataList)

        log.info { "从JSON数据生成字典翻译Excel文件完成，耗时: ${Duration.between(startTime, Instant.now()).toMillis() / 1000}秒，文件路径: $outputFilePath" }

        return outputFilePath
    }

    /**
     * 主方法：生成字典翻译Excel文件
     *
     * @return 生成的Excel文件路径
     */
    fun generateDictionaryExcel(): String {
        log.info { "开始生成字典翻译Excel文件" }
        val startTime = Instant.now()

        // 1. 获取字典数据
        val dictData = fetchDictionaryData()
        log.info { "获取到${dictData.size}个字典数据" }

        // 2. 处理字典数据，生成扁平化的Excel数据
        val excelDataList = processDictionaryData(dictData)
        log.info { "处理完成，共生成${excelDataList.size}条Excel数据" }

        // 3. 生成Excel文件
        val outputFilePath = generateOutputFilePath()
        EasyExcel.write(outputFilePath, DictExcelData::class.java)
            .sheet("字典翻译")
            .doWrite(excelDataList)

        log.info { "字典翻译Excel文件生成完成，耗时: ${Duration.between(startTime, Instant.now()).toMillis() / 1000}秒，文件路径: $outputFilePath" }

        return outputFilePath
    }

    /**
     * 获取字典数据
     *
     * @return 字典数据列表
     */
    private fun fetchDictionaryData(): List<DictItem> {
        log.info { "开始获取字典数据" }

        val json = "application/json; charset=utf-8".toMediaType()

        // 定义需要获取的字典代码
        val dictCodes = listOf(
            "style_elements",      // 元素
            "jv-style",            // 款式风格
            "fit",                 // 版型
            "plm_reference_season", // 季节
            "clothing_category",    // 品类
        )

        val requestObj = DictApiRequest(dictCodes = dictCodes)
        val requestBody = objectMapper.writeValueAsString(requestObj).toRequestBody(json)

        // 构建请求
        val request = Request.Builder()
            .url(DICT_API_URL)
            .post(requestBody)
            .header("accept", "application/json, text/plain, */*")
            .header("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
            .header("authorization", "Bearer $AUTH_TOKEN")
            .header("cache-control", "no-cache")
            .header("client-code", "POP")
            .header("content-type", "application/json")
            .header("origin", "https://pop.tiangong.tech")
            .header("pragma", "no-cache")
            .header("referer", "https://pop.tiangong.tech/")
            .header(
                "user-agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0"
            )
            .build()

        // 执行请求
        val response = okHttpClient.newCall(request).execute()

        if (!response.isSuccessful) {
            val errorBody = response.body?.string() ?: "Unknown error"
            log.error { "获取字典数据失败: ${response.code}, $errorBody" }
            throw RuntimeException("获取字典数据失败: ${response.code}, $errorBody")
        }

        val responseBody = response.body?.string() ?: throw RuntimeException("获取字典数据失败: 响应体为空")

        // 解析响应
        val apiResponse = objectMapper.readValue(responseBody, object : TypeReference<ApiResponse<List<DictItem>>>() {})

        if (!apiResponse.successful || apiResponse.data.isEmpty()) {
            log.error { "获取字典数据失败: ${apiResponse.message}" }
            throw RuntimeException("获取字典数据失败: ${apiResponse.message}")
        }

        return apiResponse.data
    }

    /**
     * 处理字典数据，生成扁平化的Excel数据
     *
     * @param dictItems 字典数据列表
     * @return Excel数据列表
     */
    private fun processDictionaryData(dictItems: List<DictItem>): List<DictExcelData> {
        log.info { "开始处理字典数据" }

        val result = mutableListOf<DictExcelData>()

        // 创建RateLimiter，限制QPS为50
        val rateLimiter = RateLimiter.create(50.0)

        // 限制并发处理的顶级字典数量，避免过多并发请求
        val maxConcurrentDicts = minOf(10, dictItems.size)

        // 分批处理字典项，避免一次创建过多任务
        val batchedDictItems = dictItems.chunked(maxConcurrentDicts)

        for (batch in batchedDictItems) {
            // 为当前批次的每个顶级字典创建一个异步任务
            val batchFutures = batch.map { dictItem ->
                CompletableFuture.supplyAsync({
                    val dictResults = mutableListOf<DictExcelData>()
                    // 传入RateLimiter进行QPS控制
                    processDictItemWithRateLimit(dictItem, dictResults, rateLimiter)
                    dictResults
                }, asyncExecutor)
            }

            // 等待当前批次的所有异步任务完成并合并结果
            val batchResults = CompletableFuture.allOf(*batchFutures.toTypedArray())
                .thenApply { batchFutures.flatMap { it.join() } }
                .get(5, TimeUnit.MINUTES)

            result.addAll(batchResults)

            log.info { "完成处理字典数据批次，当前结果条数: ${result.size}" }
        }

        return result
    }

    /**
     * 带有限流的字典项处理方法
     */
    private fun processDictItemWithRateLimit(
        dictItem: DictItem,
        results: MutableList<DictExcelData>,
        rateLimiter: RateLimiter,
        parentPath: String = "",
        parentCodes: List<String> = emptyList(),
        categoryPaths: List<String> = emptyList(),
        stylePaths: List<String> = emptyList(),
        rootDictCode: String? = null // 新增: 追踪根字典代码
    ) {
        // 确定当前根字典代码
        val currentRootDictCode = rootDictCode ?: dictItem.dictCode

        // 获取系统字段信息 - 使用根字典代码而非当前项的代码
        val sysField = if (dictItem.level == 1) {
            getSysFieldByDictCode(dictItem.dictCode)
        } else {
            getSysFieldByDictCode(currentRootDictCode)
        }

        val fieldDesc = sysField?.desc ?: ""

        val isCategory = currentRootDictCode == CATEGORY_DICT_CODE
        val isStyle = currentRootDictCode == STYLE_DICT_CODE
        val isLeafNode = dictItem.children.isNullOrEmpty()

        // 当前品类路径
        val currentCategoryPaths = if (isCategory && dictItem.level > 1) {
            categoryPaths + dictItem.dictName
        } else {
            categoryPaths
        }

        // 当前风格路径
        val currentStylePaths = if (isStyle && dictItem.level > 1) {
            stylePaths + dictItem.dictName
        } else {
            stylePaths
        }

        // 如果当前节点不是顶级节点，并且满足处理条件，添加到结果集
        if (dictItem.level > 1 && sysField != null && (!isCategory || isLeafNode)) {
            // 翻译字典名称，使用限流器控制速率
            val translation = if (needsTranslation(dictItem.dictName)) {
                capitalizeFirstLetter(translateTextWithRateLimit(dictItem.dictName, rateLimiter))
            } else {
                capitalizeFirstLetter(dictItem.dictName)
            }

            // 处理字段值，对于风格，要特殊处理为"父级-子级"的格式
            val displayDictValue = when {
                isStyle && currentStylePaths.size > 1 -> {
                    // 风格特殊处理: 父级-子级
                    val parent = currentStylePaths.first()
                    val child = dictItem.dictName
                    "$parent-$child"
                }
                isCategory -> {
                    // 品类保持层级结构
                    currentCategoryPaths.joinToString(">")
                }
                else -> {
                    // 其他直接使用名称
                    dictItem.dictName
                }
            }

            // 构建输出数据
            val excelData = DictExcelData(
                field = fieldDesc,  // 字段 - 使用系统字段描述
                relatedField = fieldDesc, // 关联字段
                dictValue = displayDictValue,
                ext1 = translation, // 英文翻译放在拓展1
                ext2 = "",  // 拓展2置空
                ext3 = ""   // 拓展3置空
            )

            results.add(excelData)
        }

        // 递归处理子项
        dictItem.children?.forEach { child ->
            processDictItemWithRateLimit(
                dictItem = child,
                results = results,
                rateLimiter = rateLimiter,
                parentPath = if (parentPath.isEmpty()) dictItem.dictName else "$parentPath > ${dictItem.dictName}",
                parentCodes = if (dictItem.level > 0) parentCodes + dictItem.dictCode else parentCodes,
                categoryPaths = currentCategoryPaths,
                stylePaths = currentStylePaths,
                rootDictCode = currentRootDictCode // 传递根字典代码给子项
            )
        }
    }

    /**
     * 将文本首字母大写
     */
    private fun capitalizeFirstLetter(text: String): String {
        if (text.isEmpty()) return text
        return text.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
    }

    /**
     * 使用限流器的翻译方法
     */
    private fun translateTextWithRateLimit(text: String, rateLimiter: RateLimiter): String {
        // 检查缓存
        translationCache[text]?.let { return it }

        // 空文本直接返回空字符串
        if (text.isBlank()) {
            return ""
        }

        // 等待限流器允许执行
        rateLimiter.acquire()

        return try {
            val translated = translateText(text)
            // 缓存结果
            translationCache[text] = translated
            translated
        } catch (e: Exception) {
            log.error { "翻译失败: $text, 错误: ${e.message}" }
            text // 失败时返回原文
        }
    }

    /**
     * 根据字典代码获取系统字段
     */
    private fun getSysFieldByDictCode(dictCode: String): ProductTitleDictionarySysFieldEnum? {
        return when (dictCode) {
            "style_elements" -> ProductTitleDictionarySysFieldEnum.PRINT_ATTRIBUTE
            "fit" -> ProductTitleDictionarySysFieldEnum.PROTOTYPE
            "plm_reference_season" -> ProductTitleDictionarySysFieldEnum.SEASON
            "jv-style" -> ProductTitleDictionarySysFieldEnum.STYLE_WORD
            "clothing_category" -> ProductTitleDictionarySysFieldEnum.CATEGORY
            else -> null
        }
    }

    /**
     * 通过字段名称获取系统字段枚举
     */
    private fun getSysFieldByName(fieldName: String): ProductTitleDictionarySysFieldEnum? {
        return when (fieldName) {
            "版型" -> ProductTitleDictionarySysFieldEnum.PROTOTYPE
            "季节" -> ProductTitleDictionarySysFieldEnum.SEASON
            "风格" -> ProductTitleDictionarySysFieldEnum.STYLE_WORD
            "领型" -> ProductTitleDictionarySysFieldEnum.COLLAR
            "袖型" -> ProductTitleDictionarySysFieldEnum.SLEEVE
            "袖长" -> ProductTitleDictionarySysFieldEnum.OUTSIDE_SLEEVE
            "印花属性/元素", "元素" -> ProductTitleDictionarySysFieldEnum.PRINT_ATTRIBUTE
            "品类" -> ProductTitleDictionarySysFieldEnum.CATEGORY
            else -> null
        }
    }


    /**
     * 判断文本是否需要翻译（中文需要翻译，英文不需要）
     */
    private fun needsTranslation(text: String): Boolean {
        // 如果包含中文字符，需要翻译
        if (CHINESE_PATTERN.matcher(text).find()) {
            return true
        }

        // 如果全部是英文字符、数字、下划线或连字符，不需要翻译
        return !ENGLISH_PATTERN.matcher(text).matches()
    }

    /**
     * 使用阿里云翻译服务将文本从中文翻译为英文
     */
    private fun translateText(text: String): String {
        // 空文本直接返回空字符串
        if (text.isBlank()) {
            return ""
        }

        // 初始化阿里云翻译配置
        val config = Config()
            .setAccessKeyId(environment.getProperty("aliyun.translate.accessKey"))
            .setAccessKeySecret(environment.getProperty("aliyun.translate.accessKeySecret"))
            .setEndpoint("mt.cn-hangzhou.aliyuncs.com")

        val client = Client(config)
        val request = TranslateGeneralRequest()
            .setFormatType("text")
            .setSourceLanguage("zh")
            .setTargetLanguage("en")
            .setSourceText(text)
            .setScene("general")

        // 设置运行时参数
        val runtimeOptions = RuntimeOptions().apply {
            autoretry = true
            maxAttempts = 3
        }

        try {
            val response = client.translateGeneralWithOptions(request, runtimeOptions)
            val translated = response.body.data.translated

            log.info { "翻译成功: $text -> $translated" }
            return translated
        } catch (e: TeaException) {
            log.error { "翻译失败: $text, 错误: ${e.message}" }
            // 翻译失败时返回原文
            return text
        } catch (e: Exception) {
            log.error { "翻译异常: $text, 异常: ${e.message}" }
            // 翻译异常时返回原文
            return text
        }
    }

    /**
     * 生成输出文件路径
     */
    private fun generateOutputFilePath(): String {
        val timestamp = DATE_FORMAT.format(LocalDateTime.now())
        val fileName = "dictionary_translate_$timestamp.xlsx"

        // 确保输出目录存在
        val outputDir = File("./output")
        if (!outputDir.exists()) {
            outputDir.mkdirs()
        }

        return "./output/$fileName"
    }
}