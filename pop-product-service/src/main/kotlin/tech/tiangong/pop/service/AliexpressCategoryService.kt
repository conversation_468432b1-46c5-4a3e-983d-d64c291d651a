package tech.tiangong.pop.service

import tech.tiangong.pop.common.resp.AliexpressCategoryResp
import tech.tiangong.pop.dto.AliexpressCategoryTreeNodeDTO
import tech.tiangong.pop.req.sdk.ae.CategoryItem
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo

interface AliexpressCategoryService {
    /**
     * 同步速卖通类目数据
     */
    fun syncAliexpressCategories(token: String?)

    /**
     * 获取速卖通类目树
     */
    fun getCategoryTree(): List<AliexpressCategoryTreeNodeDTO>

    /**
     * 根据速卖通类目ID查询类目信息
     */
    fun listByPlatformCategoryIds(categoryIds: MutableList<Long>?): List<AliexpressCategoryResp>

    /**
     * 获取平台通用格式的类目树
     */
    fun getPlatformCategoryTree(): List<PublishPlatformCategoryVo>

    /**
     * 更新所有类目的路径信息
     */
    fun updateCategoryPathLists()

    /**
     * 根据类目ID获取完整类目路径
     */
    fun getCategoryPathById(categoryId: Long): List<CategoryItem>?
}