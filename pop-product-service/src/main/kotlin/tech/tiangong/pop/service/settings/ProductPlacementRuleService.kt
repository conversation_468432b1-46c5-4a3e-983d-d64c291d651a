package tech.tiangong.pop.service.settings

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.settings.ProductPlacementRulePageReq
import tech.tiangong.pop.req.settings.ProductPlacementRuleSaveReq
import tech.tiangong.pop.req.settings.ProductPlacementRuleUpdateReq
import tech.tiangong.pop.resp.settings.ProductPlacementRuleDetailWebResp
import tech.tiangong.pop.resp.settings.ProductPlacementRulePageResp

/**
 * 铺货规则服务
 */
interface ProductPlacementRuleService {

    /**
     * 分页查询
     *
     * @param req 查询请求
     * @return 分页结果
     */
    fun pageRule(req: ProductPlacementRulePageReq): PageVo<ProductPlacementRulePageResp>

    /**
     * 保存
     *
     * @param req 保存请求
     * @return 是否成功
     */
    fun saveRule(req: ProductPlacementRuleSaveReq): Boolean

    /**
     * 更新
     *
     * @param req 更新请求
     * @return 是否成功
     */
    fun updateRule(req: ProductPlacementRuleUpdateReq): Boolean

    /**
     * 导入更新
     */
    fun importUpdate(file: MultipartFile)

    /**
     * 查询详情
     *
     * @param ruleId 详情ID
     * @return 详情
     */
    fun queryRuleDetail(ruleId: Long): ProductPlacementRuleDetailWebResp

    /**
     * 停用
     *
     * @param ruleId 规则ID
     * @return 是否成功
     */
    fun disableRule(ruleId: Long): Boolean

    /**
     * 启用
     *
     * @param ruleId 规则ID
     * @return 是否成功
     */
    fun enableRule(ruleId: Long): Boolean


}