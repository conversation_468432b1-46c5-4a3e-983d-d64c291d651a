package tech.tiangong.pop.service.regionpricerule.impl

import com.alibaba.fastjson2.parseArray
import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.CurrencyEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleFreightRateConfigDto
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleLogisticsConfigDto
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleStorageConfigDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.regionpricerule.ProductRegionPriceRuleDto
import tech.tiangong.pop.dto.regionpricerule.ProductRegionPriceRuleDto.*
import tech.tiangong.pop.dto.v2.pricing.PricingVariablesOverrideConfigDto
import tech.tiangong.pop.enums.RegionPriceRuleTaxRateTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.req.product.v2.AePricingCalculateReq
import tech.tiangong.pop.resp.product.manage.CalAeRetailPriceForPageResp
import tech.tiangong.pop.resp.regionpricerule.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleFreightRateResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleLogisticsResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleStorageResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleTaxRateResp.TaxRateRuleResp
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleService
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService
import tech.tiangong.pop.service.v2.pricing.AeTemplatePricingService
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.concurrent.TimeUnit
import kotlin.random.Random

@Slf4j
@Service
class RegionPriceRuleServiceImpl(
    private val regionPriceRuleAdRepository:RegionPriceRuleAdRepository,
    private val regionPriceRuleCommissionRepository:RegionPriceRuleCommissionRepository,
    private val regionPriceRuleDiscountRateRepository:RegionPriceRuleDiscountRateRepository,
    private val regionPriceRuleFreightRateRepository:RegionPriceRuleFreightRateRepository,
    private val regionPriceRuleGrossMarginRepository: RegionPriceRuleGrossMarginRepository,
    private val regionPriceRuleLogisticsRepository: RegionPriceRuleLogisticsRepository,
    private val regionPriceRuleMarketingRepository: RegionPriceRuleMarketingRepository,
    private val regionPriceRuleRejectRateRepository: RegionPriceRuleRejectRateRepository,
    private val regionPriceRuleStorageRepository: RegionPriceRuleStorageRepository,
    private val regionPriceRuleTaxRateRepository: RegionPriceRuleTaxRateRepository,
    private val regionPriceRuleWithdrawRepository: RegionPriceRuleWithdrawRepository,
    private val shopRepository: ShopRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val productRepository: ProductRepository,
    private val commonProperties: CommonProperties,
    private val aeTemplatePricingService: AeTemplatePricingService,
    private val currencyExchangeRateService:CurrencyExchangeRateService,
) : RegionPriceRuleService {
    companion object {
        // === 缓存配置常量 ===
        private const val CACHE_KEY_SEPARATOR = "|"
        private const val CACHE_MAX_SIZE = 1000L
        private const val CACHE_MAX_SIZE_MEDIUM = 500L
        private const val CACHE_MAX_SIZE_SMALL = 10L
        //本地缓存时间，秒
        private const val CACHE_TTL_SECOND_MIN = 3L
        private const val CACHE_TTL_SECOND_MAX = 5L
        private const val DISCOUNT_RATE_LIST = "DiscountRateList"
        private const val LOGISTICS_RULE_LIST = "LogisticsRuleList"
        private const val STORAGE_RULE_LIST = "StorageRuleList"
        private const val TAX_RATE_RULE = "TaxRateRule"
        private const val REJECT_RATE_RULE_LIST = "RejectRateRuleList"
        private const val AD_RULE_LIST = "AdRuleList"
        private const val COMMISSION_RULE_LIST = "commissionRuleList"
        private const val WITHDRAW_RULE_LIST = "withdrawRuleList"
        private const val GROSS_MARGIN_RULE_LIST = "grossMarginRuleList"
        private const val MARKETING_RULE_LIST = "marketingRuleList"
        private const val FREIGHT_RATE_RULE_LIST = "freightRateRuleList"
    }
    /**
     * 商品售价和划线价缓存
     */
    private val productSaleAndRetailPriceCache: LoadingCache<String, CalAeRetailPriceForPageResp> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, CalAeRetailPriceForPageResp>() {
                override fun load(key: String) = CalAeRetailPriceForPageResp(-1L)
            })

    /**
     * 物流成本配置缓存
     */
    private val logisticsRuleCache: LoadingCache<String, List<RegionPriceRuleLogisticsResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleLogisticsResp>>() {
                override fun load(key: String) = listLogisticsRuleResp()
            })
    /**
     * 仓储成本配置缓存
     */
    private val storageRuleCache: LoadingCache<String, List<RegionPriceRuleStorageResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleStorageResp>>() {
                override fun load(key: String) = listStorageRuleResp()
            })
    /**
     * 综合税率配置缓存
     */
    private val taxRuleCache: LoadingCache<String, RegionPriceRuleTaxRateResp> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, RegionPriceRuleTaxRateResp>() {
                override fun load(key: String) = getTaxRateRule()
            })
    /**
     * 退货率配置缓存
     */
    private val rejectRateRuleCache: LoadingCache<String, List<RegionPriceRuleRejectRateResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleRejectRateResp>>() {
                override fun load(key: String) = listRejectRateRule()
            })
    /**
     * 广告成本配置缓存
     */
    private val adRuleCache: LoadingCache<String, List<RegionPriceRuleAdResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleAdResp>>() {
                override fun load(key: String) = listAdRule()
            })
    /**
     * 交易佣金配置缓存
     */
    private val commissionRuleCache: LoadingCache<String, List<RegionPriceRuleCommissionResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleCommissionResp>>() {
                override fun load(key: String) = listCommissionRule()
            })
    /**
     * 提现手续费配置缓存
     */
    private val withdrawRuleCache: LoadingCache<String, List<RegionPriceRuleWithdrawResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleWithdrawResp>>() {
                override fun load(key: String) = listWithdrawRule()
            })

    /**
     * 毛利率配置缓存
     */
    private val grossMarginRuleCache: LoadingCache<String, List<RegionPriceRuleGrossMarginResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleGrossMarginResp>>() {
                override fun load(key: String) = listGrossMarginRule()
            })
    /**
     * 营销费用配置缓存
     */
    private val marketingRuleCache: LoadingCache<String, List<RegionPriceRuleMarketingResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleMarketingResp>>() {
                override fun load(key: String) = listMarketingRule()
            })
    /**
     * 物流支出配置缓存
     */
    private val freightRuleCache: LoadingCache<String, List<RegionPriceRuleFreightRateResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleFreightRateResp>>() {
                override fun load(key: String) = listFreightRateRule()
            })
    /**
     * 折扣率配置缓存
     */
    private val discountRateCache: LoadingCache<String, List<RegionPriceRuleDiscountRateResp>> =
        CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE_SMALL)
            .expireAfterWrite(Random.nextLong(CACHE_TTL_SECOND_MIN,CACHE_TTL_SECOND_MAX), TimeUnit.SECONDS)
            .build(object : CacheLoader<String, List<RegionPriceRuleDiscountRateResp>>() {
                override fun load(key: String) = listDiscountRateRule()
            })


    override fun getProductRegionPriceRule(product: Product): ProductRegionPriceRuleDto {
        val dto = ProductRegionPriceRuleDto().apply { this.productId = product.productId }
        val shop: Shop? = product.shopId?.let { shopRepository.getById(it) }

        // 使用 val，且只在这里计算一次默认店铺，避免后续闭包捕获可变引用
        val marketDefaultShop: Shop? =
            product.marketCode?.takeIf { it.isNotBlank() }?.let { mc ->
                commonProperties.marketDefaultShop
                    .firstOrNull { it.shopId != null && it.marketCode == mc }
                    ?.let { mdShop -> shopRepository.getById(mdShop.shopId) }
            }

        // 供给方式 / 商品自身毛利率
        val supplyMode = product.supplyMode
        val grossMargin = product.grossMargin

        // 商品成本价(SKC定价成本最大值)
        val productCost: BigDecimal? = productSkcRepository
            .getByProductId(product.productId!!)
            .filter { it.costPrice != null }
            .maxByOrNull { it.costPrice!! }
            ?.costPrice
        dto.productCost = productCost

        // 物流成本
        val logisticsRules = logisticsRuleCache.get(LOGISTICS_RULE_LIST)//listLogisticsRuleResp()
        dto.logisticsCost = getLogisticsCostDto(shop,marketDefaultShop,logisticsRules)

        // 仓储成本
        val storageRules = storageRuleCache.get(STORAGE_RULE_LIST)//listStorageRuleResp()
        dto.storageCost = getStorageCost(shop,marketDefaultShop,storageRules)
        // 综合税率
        val taxRateRule = taxRuleCache.get(TAX_RATE_RULE)//getTaxRateRule()
        dto.taxRate = getTaxRate(shop,marketDefaultShop,supplyMode,taxRateRule)

        //退货率
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的退货率，如配置表无店铺信息，则该店铺对应的平台配置的退货率
        //2.如无选款店铺，则中东市场查Rafiah的退货率，无则查 AE 平台的退货率；欧美市场查 Miamuse 的退货率，无则查 AE 平台的退货率
        val rejectRateRules = rejectRateRuleCache.get(REJECT_RATE_RULE_LIST)//listRejectRateRule()
        dto.rejectRate = getRejectRate(shop,marketDefaultShop,rejectRateRules)

        //广告成本
        //与退货率取值逻辑一致
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的广告成本，如配置表无店铺信息，则该店铺对应的平台配置的广告成本
        //2.如无选款店铺，则中东市场查Rafiah的广告成本，无则查 AE 平台的广告成本；欧美市场查 Miamuse 的广告成本，无则查 AE 平台的广告成本
        val adRules = adRuleCache.get(AD_RULE_LIST)//listAdRule()
        dto.adCost = getAdCost(shop,marketDefaultShop,adRules)
        //交易佣金
        //与退货率取值逻辑一致
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的交易佣金，如配置表无店铺信息，则该店铺对应的店铺运营模式的交易佣金
        //2.如无选款店铺，则中东市场查Rafiah的交易佣金，无则查 AE 平台Rariah的运营模式的交易佣金；欧美市场查 Miamuse 的交易佣金，无则查 AE 平台的运营模式的交易佣金
        val commissionRules = commissionRuleCache.get(COMMISSION_RULE_LIST)//listCommissionRule()
        dto.commission = getCommission(shop,marketDefaultShop,commissionRules)
        //提现手续费
        //与交易佣金取值逻辑一致
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的提现手续费，如配置表无店铺信息，则该店铺对应的店铺运营模式的提现手续费
        //2.如无选款店铺，则中东市场查Rafiah的提现手续费，无则查 AE 平台Rariah的运营模式的提现手续费；欧美市场查 Miamuse 的提现手续费，无则查 AE 平台的运营模式的提现手续费
        val withdrawRules = withdrawRuleCache.get(WITHDRAW_RULE_LIST)//listWithdrawRule()
        dto.withdraw = getWithdraw(shop,marketDefaultShop,withdrawRules)
        //毛利率
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的毛利率，如配置表无店铺信息，则该店铺对应的店铺运营模式的毛利率
        //2.如无选款店铺，则中东市场查Rafiah的毛利率，无则查 AE 平台Rariah的运营模式的毛利率；欧美市场查 Miamuse 的毛利率，无则查 AE 平台的运营模式的毛利率
        //3.如用户修改毛利率，则回显最新毛利率
        //4.交互：点击「编辑」按钮，下拉显示编辑框，编辑按钮做权限控制
        if(grossMargin!=null){
            dto.grossMargin = grossMargin
        }else{
            val grossMarginRules = grossMarginRuleCache.get(GROSS_MARGIN_RULE_LIST)//listGrossMarginRule()
            dto.grossMargin = getGrossMargin(shop,marketDefaultShop,SupplyModeEnum.getByCode(product.supplyMode),grossMarginRules)
        }

        //营销费用
        //与交易佣金取值逻辑一致
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的营销费用，如配置表无店铺信息，则该店铺对应的店铺运营模式的营销费用
        //2.如无选款店铺，则中东市场查Rafiah的营销费用，无则查 AE 平台Rariah的运营模式的营销费用；欧美市场查 Miamuse 的营销费用，无则查 AE 平台的运营模式的营销费用
        val marketingRules = marketingRuleCache.get(MARKETING_RULE_LIST)//listMarketingRule()
        dto.marketing = getMarketingCost(shop,marketDefaultShop,marketingRules)

        //物流支出
        //1.如有选款店铺，则查“定价参数配置” 内该店铺配置的物流支出，多个值显示最小值与最大值
        //2.如无选款店铺，则中东市场查Rafiah的店铺配置的物流支出，欧美市场查 Miamuse 的店铺配置的物流支出
        val freightRateRules = freightRuleCache.get(FREIGHT_RATE_RULE_LIST)//listFreightRateRule()
        dto.freightRate = getFreightRate(shop,marketDefaultShop,freightRateRules)

        //折扣率
        //与交易佣金取值逻辑类似
        //1.如有选款店铺，则查“定价参数配置” 内该店铺与该商品供给方式配置的折扣率，如配置表无店铺信息与供给方式，则取该店铺对应的店铺运营模式的折扣率
        //2.如无选款店铺，则中东市场查Rafiah的及其供给方式配置的折扣率，无则查 AE 平台Rariah的运营模式的折扣率；欧美市场查 Miamuse 的及其供给方式配置的折扣率，无则查 AE 平台的运营模式的折扣率
        val discountRateRules = discountRateCache.get(DISCOUNT_RATE_LIST)//listDiscountRateRule()
        dto.discountRate = getDiscountRate(shop,marketDefaultShop,supplyMode,discountRateRules)

        // 售价，划线价,前端通过接口异步获取
//        val prices = getAeRetailPriceV3ForPage(product)
//        dto.salePrice = prices.first
//        dto.retailPrice = prices.second
        return dto
    }

    private fun getDiscountRate(shop:Shop?,marketDefaultShop:Shop?,supplyMode:String?,discountRateRules:List<RegionPriceRuleDiscountRateResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop = shop?:marketDefaultShop
        if (tempShop != null) {
            //全匹配
            result = discountRateRules.firstOrNull { rule ->
                //运营模式匹配
                tempShop.businessType != null && rule.businessType != null && tempShop.businessType == rule.businessType
                        //店铺匹配
                        && rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains(tempShop.shopId)
                        //供给方式匹配
                        && supplyMode != null && supplyMode == rule.supplyType
            }?.rate
            //运营模式+店铺匹配
            if (result == null) {
                result = discountRateRules.firstOrNull { rule ->
                    //运营模式匹配
                    tempShop.businessType != null && rule.businessType != null && tempShop.businessType == rule.businessType
                            //店铺匹配
                            && rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains(tempShop.shopId)
                            && rule.supplyType == null
                }?.rate
            }
            //运营模式+供给方式匹配
            if (result == null) {
                result = discountRateRules.firstOrNull { rule ->
                    //运营模式
                    tempShop.businessType != null && rule.businessType != null && tempShop.businessType == rule.businessType
                            && rule.shopIds.isNullOrEmpty()
                            //供给方式匹配
                            && supplyMode != null && supplyMode == rule.supplyType
                }?.rate
            }
            //运营模式匹配
            if (result == null) {
                result = discountRateRules.firstOrNull { rule ->
                    //运营模式匹配
                    tempShop.businessType != null && rule.businessType != null && tempShop.businessType == rule.businessType
                            && rule.shopIds.isNullOrEmpty()
                            && rule.supplyType == null
                }?.rate
            }
        }
        return result
    }

    private fun getFreightRate(shop:Shop?,marketDefaultShop:Shop?,freightRateRules:List<RegionPriceRuleFreightRateResp>):FreightRateDto{
        val maxFreightRateList: MutableList<BigDecimal> = mutableListOf()
        val minFreightRateList: MutableList<BigDecimal> = mutableListOf()
        val platformIdToFreightRuleMap = freightRateRules.associateBy { it.platformId }
        val tempShop = shop?:marketDefaultShop
        tempShop?.let {
            val platformId = tempShop.platformId
            val rule = platformIdToFreightRuleMap[platformId]
            when (platformId) {
                PlatformEnum.LAZADA.platformId -> {
                    if (rule!=null && rule.lazadaFreightRateRespList != null && rule.lazadaFreightRateRespList.isNotEmpty()) {
                        val freightRateList = rule.lazadaFreightRateRespList!!.firstOrNull {
                            tempShop.shopId == it.shopId && it.feightRateRespList != null && it.feightRateRespList.isNotEmpty()
                        }?.feightRateRespList
                        //有配置到对应的店铺,则取最大值和最小值
                        if(freightRateList!=null && freightRateList.isNotEmpty()){
                            freightRateList.maxByOrNull { it.rate!! }?.rate?.let { maxFreightRateList.add(it) }
                            freightRateList.minByOrNull { it.rate!! }?.rate?.let { minFreightRateList.add(it) }
                        }
                        //没有配置到对应店铺,则取平台最大值和最小值
                        else{
                            val freightRateList = rule.lazadaFreightRateRespList!!.filter {it.feightRateRespList != null && it.feightRateRespList.isNotEmpty() }.flatMap { ps -> ps.feightRateRespList?:emptyList() }.toList()
                            freightRateList.maxByOrNull { it.rate!! }?.rate?.let { maxFreightRateList.add(it) }
                            freightRateList.minByOrNull { it.rate!! }?.rate?.let { minFreightRateList.add(it) }
                        }
                    }
                }

                PlatformEnum.TEMU.platformId -> {
                    if (rule!=null && rule.temuFreightRateRespList != null && rule.temuFreightRateRespList.isNotEmpty()) {
                        val freightRateList = rule.temuFreightRateRespList!!.firstOrNull {
                            tempShop.shopId == it.shopId && it.feightRateRespList != null && it.feightRateRespList.isNotEmpty()
                        }?.feightRateRespList
                        //有配置到对应的店铺,则取最大值和最小值
                        if(freightRateList!=null && freightRateList.isNotEmpty()){
                            freightRateList.maxByOrNull { it.rate!! }?.rate?.let { maxFreightRateList.add(it) }
                            freightRateList.minByOrNull { it.rate!! }?.rate?.let { minFreightRateList.add(it) }
                        }
                        //没有配置到对应店铺,则取平台最大值和最小值
                        else{
                            val freightRateList = rule.temuFreightRateRespList!!.filter {it.feightRateRespList != null && it.feightRateRespList.isNotEmpty() }.flatMap { ps -> ps.feightRateRespList?:emptyList() }.toList()
                            freightRateList.maxByOrNull { it.rate!! }?.rate?.let { maxFreightRateList.add(it) }
                            freightRateList.minByOrNull { it.rate!! }?.rate?.let { minFreightRateList.add(it) }
                        }
                    }
                }

                PlatformEnum.AE.platformId -> {
                    if (rule!=null && rule.aeFreightRateRespList != null && rule.aeFreightRateRespList.isNotEmpty()) {
                        rule.aeFreightRateRespList!!.firstOrNull {
                            tempShop.shopId == it.shopId && it.receivingPlaceFreightRateList != null && it.receivingPlaceFreightRateList.isNotEmpty()
                        }?.receivingPlaceFreightRateList?.forEach { receivingPlaceFreightRate ->
                            receivingPlaceFreightRate.freightRateList?.maxByOrNull { it.rate!! }?.rate?.let { maxFreightRateList.add(it) }
                            receivingPlaceFreightRate.freightRateList?.minByOrNull { it.rate!! }?.rate?.let { minFreightRateList.add(it) }
                        }
                    }
                }
            }
        }
        return FreightRateDto().apply {
            this.freightRateMax = maxFreightRateList.filter { it.isNotNull() }.maxByOrNull { it }
            // 修复：最小值应使用 minByOrNull
            this.freightRateMin = minFreightRateList.filter { it.isNotNull() }.minByOrNull { it }
        }
    }

    private fun getMarketingCost(shop:Shop?,marketDefaultShop:Shop?,marketingRules:List<RegionPriceRuleMarketingResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop:Shop? = shop?:marketDefaultShop
        if (tempShop != null) {
            result = marketingRules.firstOrNull { rule -> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains(tempShop.shopId) }?.rate
            if (result == null) {
                result = marketingRules.firstOrNull { rule ->
                    tempShop.platformId == rule.platformId && tempShop.businessType == rule.businessType &&
                            rule.shopIds.isNullOrEmpty()
                }?.rate
            }
        }
        return result
    }

    private fun getWithdraw(shop:Shop?,marketDefaultShop:Shop?,withdrawRules:List<RegionPriceRuleWithdrawResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop:Shop? = shop?:marketDefaultShop
        if (tempShop != null) {
            result = withdrawRules.firstOrNull { rule -> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains( tempShop.shopId) }?.rate
            if (result == null) {
                result = withdrawRules.firstOrNull { rule ->
                    tempShop.platformId == rule.platformId && tempShop.businessType == rule.businessType &&
                            rule.shopIds.isNullOrEmpty()
                }?.rate
            }
        }
        return result
    }

    private fun getCommission(shop:Shop?,marketDefaultShop:Shop?,commissionRules:List<RegionPriceRuleCommissionResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop:Shop? = shop?:marketDefaultShop
        if (tempShop != null) {
            result = commissionRules.firstOrNull { rule -> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains(tempShop.shopId) }?.rate
            if (result == null) {
                result = commissionRules.firstOrNull { rule ->
                    tempShop.platformId == rule.platformId && tempShop.businessType == rule.businessType &&
                            rule.shopIds.isNullOrEmpty()
                }?.rate
            }
        }
        return result
    }

    private fun getAdCost(shop:Shop?,marketDefaultShop:Shop?,adRules:List<RegionPriceRuleAdResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop:Shop? = shop?:marketDefaultShop
        if (tempShop != null) {
            result = adRules.firstOrNull { rule -> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains(tempShop.shopId) }?.rate
            if (result == null) {
                result = adRules.firstOrNull { rule ->
                    tempShop.platformId == rule.platformId && rule.shopIds.isNullOrEmpty()
                }?.rate
            }
        }
        return result
    }

    private fun getRejectRate(shop:Shop?,marketDefaultShop:Shop?,rejectRateRules:List<RegionPriceRuleRejectRateResp>):BigDecimal?{
        var result:BigDecimal? = null
        val tempShop:Shop? = shop?:marketDefaultShop
        if (tempShop != null) {
            result = rejectRateRules.firstOrNull { rule -> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains( tempShop.shopId) }?.rate
            if (result == null) {
                result = rejectRateRules.firstOrNull { rule ->
                    tempShop.platformId == rule.platformId && rule.shopIds.isNullOrEmpty()
                }?.rate
            }
        }
        return result
    }

    private fun getTaxRate(shop:Shop?,marketDefaultShop:Shop?,supplyMode:String?,taxRateRule:RegionPriceRuleTaxRateResp?): BigDecimal?{
        val tempShop = shop?:marketDefaultShop
        var result: BigDecimal? = null
        when (taxRateRule?.ruleType) {
            RegionPriceRuleTaxRateTypeEnum.SHOP.code -> {
                if (tempShop != null) {
                    taxRateRule.ruleConfig?.firstOrNull { it.shopId != null && tempShop.shopId == it.shopId }
                        ?.let { result = it.rate!! }
                }
            }
            RegionPriceRuleTaxRateTypeEnum.SUPPLY_TYPE.code -> {
                taxRateRule.ruleConfig?.firstOrNull {
                    it.supplyTypeCode != null && !supplyMode.isNullOrBlank() && it.supplyTypeCode == supplyMode
                }?.let { result = it.rate!! }
            }
            RegionPriceRuleTaxRateTypeEnum.ENTITY.code -> {
                if (tempShop != null) {
                    // 避免名称遮蔽（shadowing），不再使用 `let { shop -> ... }`
                    taxRateRule.ruleConfig?.firstOrNull {
                        it.entityCode != null && tempShop.entityCode != null && it.entityCode == tempShop.entityCode
                    }?.let { result = it.rate!! }
                }
            }
            RegionPriceRuleTaxRateTypeEnum.ENTITY_SUPPLY_TYPE.code -> {
                if (tempShop != null) {
                    taxRateRule.ruleConfig?.firstOrNull {
                        it.entityCode != null && tempShop.entityCode != null && it.entityCode == tempShop.entityCode &&
                                !supplyMode.isNullOrBlank() && it.supplyTypeCode != null && it.supplyTypeCode == supplyMode
                    }?.let { result = it.rate!! }
                }
            }
            RegionPriceRuleTaxRateTypeEnum.SHOP_SUPPLY_TYPE.code -> {
                taxRateRule.ruleConfig?.firstOrNull {
                    tempShop != null && it.shopId != null && tempShop.shopId == it.shopId &&
                            !supplyMode.isNullOrBlank() && it.supplyTypeCode != null && it.supplyTypeCode == supplyMode
                }?.let { result = it.rate!! }
            }
        }
        return result
    }

    private fun getStorageCost(shop:Shop?,marketDefaultShop:Shop?,storageRules:List<RegionPriceRuleStorageResp>):StorageCostDto{
        val platformIdToStorageRuleMap = storageRules.associateBy { it.platformId }
        val maxStorageList: MutableList<BigDecimal> = mutableListOf()
        val minStorageList: MutableList<BigDecimal> = mutableListOf()
        val tempShop = shop?:marketDefaultShop
        tempShop?.let {
            val platformId = tempShop.platformId
            val rule = platformIdToStorageRuleMap[platformId]
            when (platformId) {
                PlatformEnum.LAZADA.platformId -> {
                    if (rule!=null && rule.lazadaStorageConfigList != null && rule.lazadaStorageConfigList.isNotEmpty()) {
                        rule.lazadaStorageConfigList?.maxByOrNull { it.cost!! }?.cost?.let { maxStorageList.add(it) }
                        rule.lazadaStorageConfigList?.minByOrNull { it.cost!! }?.cost?.let { minStorageList.add(it) }
                    }
                }

                PlatformEnum.TEMU.platformId -> {
                    if (rule!=null && rule.temuStorageConfigList != null && rule.temuStorageConfigList.isNotEmpty()) {
                        rule.temuStorageConfigList?.maxByOrNull { it.cost!! }?.cost?.let { maxStorageList.add(it) }
                        rule.temuStorageConfigList?.minByOrNull { it.cost!! }?.cost?.let { minStorageList.add(it) }
                    }
                }

                PlatformEnum.AE.platformId -> {
                    if (rule!=null && rule.aeStorageConfigList != null && rule.aeStorageConfigList.isNotEmpty()) {
                        rule.aeStorageConfigList?.maxByOrNull { it.cost!! }?.cost?.let { maxStorageList.add(it) }
                        rule.aeStorageConfigList?.minByOrNull { it.cost!! }?.cost?.let { minStorageList.add(it) }
                    }
                }
            }
        }
        return StorageCostDto().apply {
            this.storageCostMax = maxStorageList.filter { it.isNotNull() }.maxByOrNull { it }
            // 修复：Min 应取最小值，不是 max()
            this.storageCostMin = minStorageList.filter { it.isNotNull() }.minByOrNull { it }
        }
    }

    private fun getLogisticsCostDto(shop:Shop?,marketDefaultShop:Shop?,logisticsRules:List<RegionPriceRuleLogisticsResp>):LogisticsCostDto{
        val platformIdToLogisticsRuleMap = logisticsRules.associateBy { it.platformId }
        val maxLogisticsList: MutableList<BigDecimal> = mutableListOf()
        val minLogisticsList: MutableList<BigDecimal> = mutableListOf()
        val tempShop = shop?:marketDefaultShop
        tempShop?.let{
            val platformId = it.platformId
            val rule = platformIdToLogisticsRuleMap[platformId]
            when(platformId) {
                PlatformEnum.LAZADA.platformId -> {
                    if (rule != null && rule.lazadaConfig != null && rule.lazadaConfig.isNotEmpty()) {
                        rule.lazadaConfig?.maxByOrNull { it.cost!! }?.cost?.let { maxLogisticsList.add(it) }
                        rule.lazadaConfig?.minByOrNull { it.cost!! }?.cost?.let { minLogisticsList.add(it) }
                    }
                }

                PlatformEnum.TEMU.platformId -> {
                    if (rule != null && rule.temuConfig != null && rule.temuConfig.isNotEmpty()) {
                        rule.temuConfig!!.maxByOrNull { it.cost!! }?.cost?.let { maxLogisticsList.add(it) }
                        rule.temuConfig!!.minByOrNull { it.cost!! }?.cost?.let { minLogisticsList.add(it) }
                    }
                }

                PlatformEnum.AE.platformId -> {
                    if (rule?.aeConfig != null && rule.aeConfig.isNotEmpty()) {
                        rule.aeConfig!!.forEach { logisticsRule ->
                            logisticsRule.receivingPlaceLogisticsCostList
                                .maxByOrNull { it.cost!! }?.cost?.let { maxLogisticsList.add(it) }
                            logisticsRule.receivingPlaceLogisticsCostList
                                .minByOrNull { it.cost!! }?.cost?.let { minLogisticsList.add(it) }
                        }
                    }
                }
            }
        }

        return LogisticsCostDto().apply {
            this.logisticsCostMax = maxLogisticsList.filter { it.isNotNull() }.maxByOrNull { it }
            this.logisticsCostMin = minLogisticsList.filter { it.isNotNull() }.minByOrNull { it }
        }
    }

    override fun prepareCalculateReq(product:Product):AePricingCalculateReq{
        val marketCode = product.marketCode
        val shop: Shop? = product.shopId?.let { shopRepository.getById(it) }

        // 使用 val，且只在这里计算一次默认店铺，避免后续闭包捕获可变引用
        val marketDefaultShop: Shop? =
            product.marketCode?.takeIf { it.isNotBlank() }?.let { mc ->
                commonProperties.marketDefaultShop
                    .firstOrNull { it.shopId != null && it.marketCode == mc }
                    ?.let { mdShop -> shopRepository.getById(mdShop.shopId) }
            }

        val req = AePricingCalculateReq(product.productId!!)
        //如果选款店铺是AE平台的，则使用这个店铺ID进行计算
        if(shop!=null && PlatformEnum.AE.platformId==shop.platformId){
            req.shopIds = mutableListOf(shop.shopId!!)
        }
        //没有指定选款店铺，但商品市场默认店铺为AE平台的店铺
        else if(shop==null && marketDefaultShop!=null && PlatformEnum.AE.platformId==marketDefaultShop.platformId){
            req.shopIds = mutableListOf(marketDefaultShop.shopId!!)
        }
        //根据市场判断使用指定发货地和目的地进行计算
        marketCode?.let{mc->
            val shipFromList:MutableList<String> = mutableListOf()
            val shipToList:MutableList<String> = mutableListOf()
            commonProperties.marketDefaultShop.firstOrNull{it.marketCode==mc}
                ?.shippingAndReceivingPlaceList?.filter{it.shippingPlaceId!=null}?.map{
                    shipFromList.add(it.shippingPlaceId!!)
                    if(it.receivingPlaceList.isNotEmpty()){
                        shipToList.addAll(it.receivingPlaceList)
                    }
                }
            req.shipFromList = shipFromList
            req.shipToList = shipToList
        }
        return req
    }

    override fun calAeSaleAndRetailPriceForPage(productIds: List<Long>): List<CalAeRetailPriceForPageResp>{
        if(productIds.isEmpty()){
            return mutableListOf()
        }
        val products = productRepository.listByIds(productIds)
        if(products.isEmpty()){
            return mutableListOf()
        }
        return products.mapNotNull {
            var price = productSaleAndRetailPriceCache.get(it.productId!!.toString())
            if(price == null || price.productId==-1L){
                price = CalAeRetailPriceForPageResp(it.productId!!).apply {
                    var result = getAeRetailPriceV3ForPage(it)
                    this.salePrice =  result.first
                    this.retailPrice = result.second
                }
                productSaleAndRetailPriceCache.put(it.productId.toString(), price)
            }
            price
        }
    }

    private fun getAeRetailPriceV3ForPage(product: Product): Pair<SalePriceDto,RetailPriceDto> {
        val salePrice = SalePriceDto()
        val retailPrice = RetailPriceDto()
        //没有选款店铺，并且市场为空，或者不是中东和欧美，则不计算
        if(product.shopId==null &&
            (product.marketCode.isNullOrBlank()
            || (product.marketCode!="zd" && product.marketCode!="om"))){
            return Pair(salePrice,retailPrice)
        }
        val req = prepareCalculateReq(product)
        try {
            val resp = aeTemplatePricingService.calculate(req)
            if (!resp.success) {
                log.error { "getAeRetailPriceV3ForPage ${product.productId}计算售价，划线价异常:${resp.errorMessage}" }
                return Pair(salePrice,retailPrice)
            } else {
                // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
                //售价
                salePrice.apply {
                    this.salePriceMax = resp.results.mapNotNull { it.salePrice }.maxOrNull()?: BigDecimal.ZERO
                    this.salePriceMin = resp.results.mapNotNull { it.salePrice }.minOrNull()?: BigDecimal.ZERO
                }
                salePrice.salePriceUSDMax = salePrice.salePriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                salePrice.salePriceUSDMin = salePrice.salePriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                //划线价
                retailPrice.apply {
                    this.retailPriceMax = resp.results.mapNotNull { it.retailPrice }.maxOrNull()?: BigDecimal.ZERO
                    this.retailPriceMin = resp.results.mapNotNull { it.retailPrice }.minOrNull()?: BigDecimal.ZERO
                }
                retailPrice.retailPriceUSDMax = retailPrice.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                retailPrice.retailPriceUSDMin = retailPrice.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                return Pair(salePrice,retailPrice)
            }
        } catch (e: Exception) {
            log.error { "getAeRetailPriceV3ForPage ${product.productId}计算售价，划线价异常:${e.message}" }
        }
        return Pair(salePrice,retailPrice)
    }

    private fun getAeRetailPriceV2ForPage(product: Product,shop:Shop?,marketDefaultShop:Shop?,productRegionPriceRule:ProductRegionPriceRuleDto): RetailPriceDto {
        val retailPrice = RetailPriceDto()
        //最小值(用物流成本，仓库成本，物流支出的最小值去计算)
        val retailPriceMin = calRetailPrice(product,shop,marketDefaultShop,
            productRegionPriceRule.logisticsCost?.logisticsCostMin,
            productRegionPriceRule.storageCost?.storageCostMin,
            productRegionPriceRule.freightRate?.freightRateMin)
        //最大值(用物流成本，仓库成本，物流支出的最大值去计算)
        val retailPriceMax = calRetailPrice(product,shop,marketDefaultShop,
            productRegionPriceRule.logisticsCost?.logisticsCostMax,
            productRegionPriceRule.storageCost?.storageCostMax,
            productRegionPriceRule.freightRate?.freightRateMax)
        retailPrice.apply {
            this.retailPriceMax = retailPriceMin.retailPriceMax ?: BigDecimal.ZERO
            this.retailPriceMin = retailPriceMax.retailPriceMin ?: BigDecimal.ZERO
        }
        // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
        val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
        retailPrice.retailPriceUSDMax = retailPrice.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
        retailPrice.retailPriceUSDMin = retailPrice.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
        return retailPrice
    }

    private fun getAeRetailPriceV1ForPage(product: Product,shop:Shop?,marketDefaultShop:Shop?): RetailPriceDto {
        val retailPrice = RetailPriceDto()
//        //如果AE模板有值，则直接用模板的
//        val templateAeSpu = productTemplateAeSpuRepository.getByProductId(product.productId!!)
//        if(templateAeSpu!=null){
//            val templateAeSkuList = productTemplateAeSkuRepository.listByAeSpuIds(templateAeSpu.aeSpuId!!,Bool.YES.code).filter { it.retailPrice!=null }
//            if(templateAeSkuList.isNotEmpty()){
//                return retailPrice.apply {
//                    this.retailPriceMax = templateAeSkuList.filter { it.retailPrice!=null }.maxByOrNull { it.retailPrice!! }?.retailPrice
//                    this.retailPriceMin = templateAeSkuList.filter { it.retailPrice!=null }.minByOrNull { it.retailPrice!! }?.retailPrice
//                    // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
//                    val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
//                    this.retailPriceUSDMax = this.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
//                    this.retailPriceUSDMin = this.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
//                }
//            }
//        }

        //如果AE模板没有记录划线价，则要实时计算
        val req = AePricingCalculateReq(product.productId!!)
        //如果选款店铺是AE平台的，则使用这个店铺ID进行计算
        if(shop!=null && PlatformEnum.AE.platformId==shop.platformId){
            req.shopIds = mutableListOf(shop.shopId!!)
        }
        //没有指定选款店铺，但商品市场默认店铺为AE平台的店铺
        else if(shop==null && marketDefaultShop!=null && PlatformEnum.AE.platformId==marketDefaultShop.platformId){
            req.shopIds = mutableListOf(marketDefaultShop.shopId!!)
        }

        try {
            val resp = aeTemplatePricingService.calculate(req)
            if (!resp.success) {
                log.error { "${product.productId}计算划线价异常:${resp.errorMessage}" }
                return retailPrice
            } else {
                retailPrice.apply {
                    this.retailPriceMax = resp.results.mapNotNull { it.retailPrice }.maxOrNull()?: BigDecimal.ZERO
                    this.retailPriceMin = resp.results.mapNotNull { it.retailPrice }.minOrNull()?: BigDecimal.ZERO
                }
                // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
                retailPrice.retailPriceUSDMax = retailPrice.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                retailPrice.retailPriceUSDMin = retailPrice.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                return retailPrice
            }
        } catch (e: Exception) {
            log.error { "${product.productId}计算划线价异常:${e.message}" }
        }
        return retailPrice
    }

    private fun calRetailPrice(product:Product,shop:Shop?,marketDefaultShop:Shop?,
                                  logisticsCost:BigDecimal?,storageCost:BigDecimal?,
                                  freightRate:BigDecimal?):RetailPriceDto{
        val retailPrice = RetailPriceDto()
        //如果AE模板没有记录划线价，则要实时计算
        val req = AePricingCalculateReq(product.productId!!)
        //如果选款店铺是AE平台的，则使用这个店铺ID进行计算
        if(shop!=null && PlatformEnum.AE.platformId==shop.platformId){
            req.shopIds = mutableListOf(shop.shopId!!)
        }
        //没有指定选款店铺，但商品市场默认店铺为AE平台的店铺
        else if(shop==null && marketDefaultShop!=null && PlatformEnum.AE.platformId==marketDefaultShop.platformId){
            req.shopIds = mutableListOf(marketDefaultShop.shopId!!)
        }
        val configDto = PricingVariablesOverrideConfigDto()
        //最小值
        //物流成本
        logisticsCost?.let{
            configDto.logisticsCost = it
        }
        //仓储成本
        storageCost?.let{
            configDto.storageCost = it
        }
        //物流支出
        freightRate?.let{
            configDto.logisticsRate = it
        }

        req.overrideConfig = configDto
        try {
            val resp = aeTemplatePricingService.calculate(req)
            if (!resp.success) {
                log.error { "${product.productId}计算划线价最小值异常:${resp.errorMessage}" }
                return retailPrice
            } else {
                retailPrice.apply {
                    this.retailPriceMax = resp.results.mapNotNull { it.retailPrice }.maxOrNull()?: BigDecimal.ZERO
                    this.retailPriceMin = resp.results.mapNotNull { it.retailPrice }.minOrNull()?: BigDecimal.ZERO
                }
                // 如果是美元，则需要将价格转换为美元，目前库中存的是CNY价格
                val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code)
                retailPrice.retailPriceUSDMax = retailPrice.retailPriceMax!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                retailPrice.retailPriceUSDMin = retailPrice.retailPriceMin!!.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP)
                return retailPrice
            }
        } catch (e: Exception) {
            log.error { "${product.productId}计算划线价最小值异常:${e.message}" }
        }
        return retailPrice
    }

    override fun getGrossMarginByProduct(product:Product):BigDecimal?{
        val shop: Shop? = product.shopId?.let { shopRepository.getById(it) }

        var marketDefaultShop:Shop? = null
        //没有选款店铺，则根据市场编码指定默认店铺
        if (product.marketCode!=null && product.marketCode.isNotBlank()) {
            commonProperties.marketDefaultShop.firstOrNull{it.shopId!=null && it.marketCode == product.marketCode}?.let{mdShop->
                marketDefaultShop = shopRepository.getById(mdShop.shopId)
            }
        }
        val grossMarginRules = listGrossMarginRule()
        return getGrossMargin(shop,marketDefaultShop,SupplyModeEnum.getByCode(product.supplyMode),grossMarginRules)
    }

    private fun getGrossMargin(shop:Shop?,marketDefaultShop:Shop?,supplyType: SupplyModeEnum?,grossMarginRules:List<RegionPriceRuleGrossMarginResp>):BigDecimal?{
        var grossMargin: BigDecimal? = null
        val tempShop = shop?:marketDefaultShop
        if(tempShop!=null){
            grossMargin = grossMarginRules.firstOrNull {  rule-> rule.shopIds.isNotNull() && rule.shopIds.isNotEmpty() && rule.shopIds!!.contains( tempShop.shopId )
                    && supplyType!=null && supplyType.dictCode== rule.supplyType}?.rate
            if(grossMargin==null){
                grossMargin = grossMarginRules.firstOrNull { rule->tempShop.platformId == rule.platformId && supplyType!=null && supplyType.dictCode== rule.supplyType && rule.shopIds.isNullOrEmpty()}?.rate
            }
        }
        return grossMargin
    }

    override fun listLogisticsRuleResp(): List<RegionPriceRuleLogisticsResp> {
        val rules = regionPriceRuleLogisticsRepository.list()
        return rules.map {
            val resp = RegionPriceRuleLogisticsResp()
            resp.logisticsRuleId = it.logisticsRuleId
            resp.platformId = it.platformId
            val configDto = it.ruleConfig?.parseJson<RegionPriceRuleLogisticsConfigDto>()
            resp.aeConfig = configDto?.aeConfigDtoList?.let {
                val aeConfigList :MutableList<AEConfigResp> = mutableListOf()
                //按发货地分组
                val shippingPlaceToMap = it.groupBy { "${it.shippingPlaceId}-${it.shippingPlace}-${it.shippingPlaceCn}" }
                shippingPlaceToMap.forEach {(key, list2) ->
                    val costRespList = list2.map {
                        val costResp = ReceivingPlaceLogisticsCostResp(it.receivingPlace,it.receivingPlaceCn,it.cost,it.defaultRule)
                        costResp
                    }.toList()
                    try {
                        val shippingPlaces = key.split("-")
                        aeConfigList.add(AEConfigResp(shippingPlaces[0].toLong(),shippingPlaces[1],shippingPlaces[2],costRespList))
                    } catch (e: Exception) {
                        log.error(e) { "获取物流配置转换失败:${e.message} key=$key" }
                    }
                }
                aeConfigList
            }
            resp.lazadaConfig = configDto?.lazadaConfigDtoList?.map {
                val lazadaConfigResp = LazadaConfigResp()
                BeanUtils.copyProperties(it,lazadaConfigResp)
                lazadaConfigResp
            }
            resp.temuConfig = configDto?.temuConfigDtoList?.map {
                val temuConfigResp = TemuConfigResp()
                BeanUtils.copyProperties(it,temuConfigResp)
                temuConfigResp
            }
            resp
        }
    }

    override fun listStorageRuleResp():List<RegionPriceRuleStorageResp>{
        val rules = regionPriceRuleStorageRepository.list()
        return rules.map { it ->
            val resp = RegionPriceRuleStorageResp()
            resp.storageRuleId = it.storageRuleId
            resp.platformId = it.platformId
            val configDto = it.ruleConfig?.parseJson<RegionPriceRuleStorageConfigDto>()
            resp.aeStorageConfigList = configDto?.aeStorageConfigDtoList?.map {
                AEStorageConfigResp(it.shippingPlaceId,it.shippingPlace,it.shippingPlaceCn,it.cost)
            }
            resp.lazadaStorageConfigList = configDto?.lazadaStorageConfigDtoList?.map {
                LazadaStorageConfigResp(it.country,it.cost)
            }
            resp.temuStorageConfigList = configDto?.temuStorageConfigDtoList?.map {
                TemuStorageConfigResp(it.country,it.cost)
            }
            resp
        }
    }

    override fun getTaxRateRule(): RegionPriceRuleTaxRateResp?{
        val regionPriceRuleTaxRate = regionPriceRuleTaxRateRepository.ktQuery().one()
        return regionPriceRuleTaxRate?.let{
            val result = RegionPriceRuleTaxRateResp()
            result.taxRateRuleId = it.taxRateRuleId
            result.ruleType = it.ruleType
            result.ruleConfig = it.ruleConfig.parseArray<TaxRateRuleResp>()
            result
        }
    }

    override fun listRejectRateRule(): List<RegionPriceRuleRejectRateResp> {
        val list = regionPriceRuleRejectRateRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleRejectRateResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listAdRule(): List<RegionPriceRuleAdResp> {
        val list = regionPriceRuleAdRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleAdResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listCommissionRule(): List<RegionPriceRuleCommissionResp> {
        val list = regionPriceRuleCommissionRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleCommissionResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listWithdrawRule(): List<RegionPriceRuleWithdrawResp> {
        val list = regionPriceRuleWithdrawRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleWithdrawResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listGrossMarginRule(): List<RegionPriceRuleGrossMarginResp> {
        val list = regionPriceRuleGrossMarginRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleGrossMarginResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listMarketingRule(): List<RegionPriceRuleMarketingResp> {
        val list = regionPriceRuleMarketingRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleMarketingResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun listFreightRateRule(): List<RegionPriceRuleFreightRateResp>{
        val list = regionPriceRuleFreightRateRepository.ktQuery()
            .list()
        return list.map{
            val resp = RegionPriceRuleFreightRateResp().apply{
                this.freightRateRuleId = it.freightRateRuleId
                this.platformId = it.platformId
                val configDto = it.ruleConfig?.parseJson<RegionPriceRuleFreightRateConfigDto>()
                this.aeFreightRateRespList = configDto?.aeFreightRateRuleDtoList?.map {
                    AEFreightRateResp().apply{
                        this.shopId = it.shopId
                        this.receivingPlaceFreightRateList = it.receivingPlaceFreightRateList.map{
                            ReceivingPlaceFreightRateResp().apply{
                                this.shippingPlaceId = it.shippingPlaceId
                                this.shippingPlace = it.shippingPlace
                                this.shippingPlaceCn = it.shippingPlaceCn
                                this.freightRateList = it.freightRateList.map{
                                    FreightRateResp().apply{
                                        this.receivingPlace = it.receivingPlace
                                        this.receivingPlaceCn = it.receivingPlaceCn
                                        this.rate = it.rate
                                        this.defaultRule = it.defaultRule?:Bool.NO.code
                                    }
                                }.toList()
                            }
                        }
                    }
                }
                this.lazadaFreightRateRespList = configDto?.lazadaFreightRateRuleDtoList?.map {
                    LazadaFreightRateRuleResp().apply {
                        this.shopId = it.shopId
                        this.feightRateRespList = it.feightRateDtoList?.map {
                            LazadaFeightRateResp().apply {
                                this.country = it.country
                                this.rate = it.rate
                            }
                        }
                    }
                }
                this.temuFreightRateRespList = configDto?.temuFreightRateRuleDtoList?.map {
                    TemuFreightRateRuleResp().apply {
                        this.shopId = it.shopId
                        this.feightRateRespList = it.feightRateDtoList?.map {
                            TemuFeightRateResp().apply {
                                this.country = it.country
                                this.rate = it.rate
                            }
                        }
                    }
                }
            }
            resp
        }
    }

    override fun listDiscountRateRule(): List<RegionPriceRuleDiscountRateResp> {
        val list = regionPriceRuleDiscountRateRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleDiscountRateResp()
            BeanUtils.copyProperties(it,costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    override fun invalidateAllCache() {
        log.info { "开始清除所有区域定价配置相关缓存" }
        productSaleAndRetailPriceCache.invalidateAll()
        logisticsRuleCache.invalidateAll()
        storageRuleCache.invalidateAll()
        taxRuleCache.invalidateAll()
        rejectRateRuleCache.invalidateAll()
        adRuleCache.invalidateAll()
        commissionRuleCache.invalidateAll()
        withdrawRuleCache.invalidateAll()
        grossMarginRuleCache.invalidateAll()
        marketingRuleCache.invalidateAll()
        freightRuleCache.invalidateAll()
        discountRateCache.invalidateAll()
        log.info { "所有区域定价配置相关缓存清除完成" }
    }
}