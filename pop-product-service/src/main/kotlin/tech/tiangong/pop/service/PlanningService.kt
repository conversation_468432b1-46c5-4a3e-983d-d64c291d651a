package tech.tiangong.pop.service


import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.common.req.PlanningSupplyQuantityReq
import tech.tiangong.pop.common.resp.PlanningSupplyQuantityResp
import tech.tiangong.pop.req.planning.*
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.planning.DictPlatformCategoryResp
import tech.tiangong.pop.resp.planning.PlanningDetailResp
import tech.tiangong.pop.resp.planning.PlanningPageResp
import tech.tiangong.pop.resp.planning.PlanningPresetCategoryResp

/**
 * 企划管理
 * <AUTHOR>
 * @date 2024/11/18 15:09
 */
interface PlanningService {

    /**
     * 校验企划 渠道-平台-月份 是否存在
     * @param req 请求对象
     * @return true 存在, false 不存在
     */
    fun checkPlanningExist(req: PlanningCheckExistReq): Boolean

    /**
     * 创建/编辑-企划汇总
     * @param req 请求对象
     * @return
     */
    fun summaryCreate(req: PlanningSummaryCreateReq): Long

    /**
     * 创建/编辑-开发节奏
     * @param req 请求对象
     * @return
     */
    fun developRhythmCreate(req: PlanningDevelopRhythmCreateReq): Long

    /**
     * 创建/编辑-上架节奏
     * @param req 请求对象
     * @return
     */
    fun publishRhythmCreate(req: PlanningPublishRhythmCreateReq): Long

    /**
     * 创建/编辑-品类价格
     * @param req 请求对象
     * @return
     */
    fun categoryPriceCreate(req: PlanningCategoryPriceCreateReq): Long

    /**
     * 获取企划详情(包括企划汇总,开发节奏,上架节奏,品类价格)
     * @param planningId 企划id
     * @return 企划详情
     */
    fun detail(planningId: Long): PlanningDetailResp

    /**
     * 获取企划汇总列表
     * @param req 企划id
     * @return
     */
    fun page(req: PlanningPageReq): PageVo<PlanningPageResp>

    /**
     * 复制企划
     * @param req 企划id
     * @return 新企划id
     */
    fun copy(req: PlanningCopyReq): Long

    /**
     * 删除企划
     * @param req 企划id
     * @return
     */
    fun delete(req: PlanningDeleteReq)

    /**
     * 企划推送灵感
     * @param req 企划id
     * @return
     */
    fun pushInspiration(req: PlanningPushInspirationReq)

    /**
     * 查询-企划品类供给数统计
     * @param req 请求对象
     * @return
     */
    fun supplyQuantityCategory(req: PlanningSupplyQuantityReq): PlanningSupplyQuantityResp

    /**
     * 字典内部品类code获取平台品类
     * @param req
     * @return
     */
    fun getPlatformCategoryByDictCode(req: DictPlatformCategoryReq): DictPlatformCategoryResp

    /**
     * 企划预设品类
     * @return
     */
    fun getPresetCategory(req: PlanningPresetCategoryReq): List<PlanningPresetCategoryResp>

    /**
     * 导入预设品类
     * @param file
     */
    fun importPresetCategory(@RequestParam("file") file: MultipartFile): ImportResultResp
}