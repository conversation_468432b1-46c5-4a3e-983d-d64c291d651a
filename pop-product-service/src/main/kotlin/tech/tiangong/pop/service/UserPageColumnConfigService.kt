package tech.tiangong.pop.service

import tech.tiangong.pop.req.product.SaveShowPageColumnReq
import tech.tiangong.pop.resp.ShowPageColumnResp

/**
 * 用户列表页面展示列配置
 */
interface UserPageColumnConfigService {


    /**
     * 根据页面编码获取当前用户的列展示配置
     */
    fun getCurrentUserPageColumnByPageCode(pageCode:String):ShowPageColumnResp
    /**
     * 保存列表展示列配置(当前用户)
     */
    fun saveShowPageColumnConfig(req:SaveShowPageColumnReq)
}