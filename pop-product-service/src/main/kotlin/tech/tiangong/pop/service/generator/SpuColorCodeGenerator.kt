package tech.tiangong.pop.service.generator

import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component
import tech.tiangong.pop.dao.repository.BaseSkuInfoRepository

@Component
class SpuColorCodeGenerator(
    private val baseSkuInfoRepository: BaseSkuInfoRepository,
    private val redisTemplate: RedisTemplate<String, Any>
) {

    companion object {
        private const val BARCODE_MAX_SKCCODE_LOCK = "BARCODE:MAX_SKCODE_LOCK:"
    }

    /**
     * 考虑到这种操作较少且没并发，所以
     * 这里没有依赖redis，因为redis不支持事务，当保存出错时，需要处理跳号的问题
     * @param spu spu-款
     * @param skc skc颜色
     * @return 生成的颜色代码
     */
    fun generate(spu: String, skc: String): String {
        val entity = baseSkuInfoRepository.findOneBySpuAndSkc(spu, skc)
        // 如果存在则使用原来的编码
        if (entity != null) {
            return entity.colorCode!!
        }
        // 否则要根据spu维度自增
        val maxColorCode = baseSkuInfoRepository.findMaxColorCodeBySpu(spu)
        val value = maxColorCode + 1
        if (value > 99) {
            throw IllegalArgumentException("该spu[$spu]序列已用完")
        }
        return String.format("%02d", value)
    }

    /**
     * 使用 Redis 生成 color_code（按 spu 维度计数）。
     * 如果对应的 spu 与 skc 记录已存在，则直接返回原来的 color_code；
     * 否则，从 Redis 获取当前最大值，原子递增后生成新的 color_code。
     *
     * 注意：
     * 1. 如果 Redis 中没有计数器，则先通过数据库初始化该计数器值。
     * 2. 使用 Redis 原子递增可能会出现跳号问题（例如保存失败时），需要根据业务需求进行处理。
     *
     * @param spu spu-款
     * @param skc skc颜色
     * @return 生成的 color_code
     */
    fun generateByCache(spu: String, skc: String): String {
        // 1. 查询数据库，若存在则直接返回已存在的 color_code
        val entity = baseSkuInfoRepository.findOneBySpuAndSkc(spu, skc)
        if (entity != null) {
            return entity.colorCode!!
        }

        // 2. 定义 Redis 中的计数器 key（按 spu 维度）
        val redisKey = BARCODE_MAX_SKCCODE_LOCK + spu

        // 3. 如果 Redis 中不存在该计数器，则先从数据库初始化
        if (redisTemplate.hasKey(redisKey) != true) {
            val maxColorCode = baseSkuInfoRepository.findMaxColorCodeBySpu(spu)
            // 写入 Redis，初始化计数器值
            redisTemplate.opsForValue().set(redisKey, maxColorCode)
        }

        // 4. 原子递增，获取新的值
        val next = redisTemplate.opsForValue().increment(redisKey, 1)
            ?: throw RuntimeException("生成 color_code 失败")

        // 5. 校验上限，color_code 最大值为 99
        if (next > 99) {
            throw IllegalArgumentException("该spu[$spu]的颜色序列已用完")
        }

        // 6. 格式化为两位字符串返回
        return String.format("%02d", next)
    }
}
