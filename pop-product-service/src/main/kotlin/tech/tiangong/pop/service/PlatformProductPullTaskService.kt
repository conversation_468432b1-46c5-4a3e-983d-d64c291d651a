package tech.tiangong.pop.service

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.product.lazada.PlatformProductPullTaskExportReq
import tech.tiangong.pop.req.product.lazada.PlatformProductPullTaskPageQueryReq
import tech.tiangong.pop.resp.product.lazada.PlatformProductPullTaskPageVo

interface PlatformProductPullTaskService {
    /**
     * 分页查询平台商品同步任务
     */
    fun page(req: PlatformProductPullTaskPageQueryReq): PageVo<PlatformProductPullTaskPageVo>

    /**
     * 取消任务
     */
    fun cancelTask(taskId: Long)

    /**
     * 提交失败任务导出任务
     */
    fun submitPlatformProductSyncTaskExport(req: PlatformProductPullTaskExportReq)
}
