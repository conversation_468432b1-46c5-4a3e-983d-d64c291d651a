package tech.tiangong.pop.service.generator

import org.apache.commons.lang3.StringUtils
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Component
import tech.tiangong.pop.constant.RedisConstants.BARCODE_MAX_SPU_CODE_LOCK
import tech.tiangong.pop.dao.repository.BaseSkuInfoRepository

/**
 * spu编码生成器,因为数据库生成，生成后必须保存，不支持批量保存或并发
 *
 * <AUTHOR> Li
 */
@Component
class SpuCodeGenerator(
    private val baseSkuInfoRepository: BaseSkuInfoRepository,
    private val redisTemplate: RedisTemplate<String, Any>
) {

    fun generate(spu: String, year: String): String {
        val entity = baseSkuInfoRepository.findOneBySpu(spu)
        if (entity != null) {
            return entity.spuCode!!
        }
        val maxSpuCode = baseSkuInfoRepository.findMaxSpuCodeByRecordYear(year)
        val next = maxSpuCode + 1
        return StringUtils.substring(year, 2) + String.format("%08d", next)
    }

    fun generateByCache(spu: String, year: String): String {
        // 如果数据库中已经存在该 spu 对应的记录，则直接返回已有的 spu_code
        val entity = baseSkuInfoRepository.findOneBySpu(spu)
        if (entity != null) {
            return entity.spuCode!!
        }

        // 全局计数器 key（按年份区分）
        val redisKey = BARCODE_MAX_SPU_CODE_LOCK + year

        // 如果 Redis 中该 key 不存在，则先通过数据库查找当年的最大值进行初始化
        if (redisTemplate.hasKey(redisKey) != true) {
            val maxSpuCode = baseSkuInfoRepository.findMaxSpuCodeByRecordYear(year)
            // 初始化 Redis 中的计数器
            redisTemplate.opsForValue().set(redisKey, maxSpuCode)
        }

        // 原子递增：获取新的序号
        val next = redisTemplate.opsForValue().increment(redisKey, 1)
            ?: throw RuntimeException("生成 spu_code 失败")

        // 格式化 spu_code，例如：年份截取后两位 + 8 位序号（前置补 0）
        return StringUtils.substring(year, 2) + String.format("%08d", next)
    }

    fun generateByForce(spu: String, year: String): String {
        val maxSpuCode = baseSkuInfoRepository.findMaxSpuCodeByRecordYear(year)
        val next = maxSpuCode + 1
        return StringUtils.substring(year, 2) + String.format("%08d", next)
    }

    fun generateByForceByCache(spu: String, year: String): String {
        // 全局计数器 key（按年份区分）
        val redisKey = BARCODE_MAX_SPU_CODE_LOCK + year

        // 如果 Redis 中该 key 不存在，则先通过数据库查找当年的最大值进行初始化
        if (redisTemplate.hasKey(redisKey) != true) {
            val maxSpuCode = baseSkuInfoRepository.findMaxSpuCodeByRecordYear(year)
            // 初始化 Redis 中的计数器
            redisTemplate.opsForValue().set(redisKey, maxSpuCode)
        }

        // 原子递增：获取新的序号
        val next = redisTemplate.opsForValue().increment(redisKey, 1)
            ?: throw RuntimeException("生成 spu_code 失败")

        //int maxSpuCode=baseSkuInfoRepository.findMaxSpuCodeByRecordYear(year);
        //int next=maxSpuCode+1;
        return StringUtils.substring(year, 2) + String.format("%08d", next)
    }
}
