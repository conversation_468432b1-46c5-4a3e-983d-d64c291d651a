package tech.tiangong.pop.service

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.product.task.TaskExportByBatchUpdateReq
import tech.tiangong.pop.req.product.task.TaskUpdateProgressPageReq
import tech.tiangong.pop.resp.product.task.TaskUpdateProgressPageVo
import jakarta.servlet.http.HttpServletResponse

interface TaskService {

    /**
     * 根据任务拉取数据并比较更新job
     */
    fun productTaskHandlerJob(taskIds: Set<Long>?)

    /**
     * 更新进度-分页
     *
     * @param req
     * @return
     */
    fun updateProgressPage(req: TaskUpdateProgressPageReq): PageVo<TaskUpdateProgressPageVo>

    /**
     * 批量更新任务记录-导出结果
     *
     * @param response
     * @param req
     */
    fun exportByBatchUpdateTaskType(response: HttpServletResponse, req: TaskExportByBatchUpdateReq)
}
