package tech.tiangong.pop.service.settings

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.settings.ProductTitleConditionWordDeleteReq
import tech.tiangong.pop.req.settings.ProductTitleConditionWordPageQueryReq
import tech.tiangong.pop.req.settings.ProductTitleConditionWordSaveReq
import tech.tiangong.pop.resp.settings.ProductTitleConditionWordPageResp
import tech.tiangong.pop.resp.settings.ProductTitleConditionWordResp

/**
 * 商品标题条件词服务接口
 */
interface ProductTitleConditionWordService {
    
    /**
     * 分页查询商品标题条件词
     */
    fun page(req: ProductTitleConditionWordPageQueryReq): PageVo<ProductTitleConditionWordPageResp>
    
    /**
     * 查询商品标题条件词详情
     */
    fun getDetail(conditionWordId: Long): ProductTitleConditionWordResp
    
    /**
     * 保存或更新商品标题条件词
     */
    fun saveOrUpdate(req: ProductTitleConditionWordSaveReq): Boolean
    
    /**
     * 删除商品标题条件词
     */
    fun delete(req: ProductTitleConditionWordDeleteReq): Boolean

    /**
     * 根据条件字段和字段值ID获取条件词
     */
    fun getByConditionFieldAndFieldValueId(conditionField: String, fieldValueId: String): ProductTitleConditionWordResp?

    /**
     * 根据条件字段和字段值获取条件词
     */
    fun getByConditionFieldAndFieldValue(conditionField: String, fieldValue: String): ProductTitleConditionWordResp?

    /**
     * 清除所有缓存
     */
    fun invalidateAllCache()
}