package tech.tiangong.pop.service.product.impl

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.ae.CallAeComponent
import tech.tiangong.pop.component.lazada.CallLazadaComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.dto.product.ProductErrorInfoDto
import tech.tiangong.pop.enums.ProductErrorTypeEnum
import tech.tiangong.pop.req.product.RepairAeProductDetailReq
import tech.tiangong.pop.req.product.RepairAeProductUpdateReq
import tech.tiangong.pop.req.product.RepairLazadaProductDetailReq
import tech.tiangong.pop.req.product.RepairLazadaProductUpdateReq
import tech.tiangong.pop.resp.product.RepairAeProductDetailResp
import tech.tiangong.pop.resp.product.RepairLazadaProductDetailResp
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.RepairProductService
import java.util.*

/**
 * 异常商品修复
 * <AUTHOR>
 * @date 2025-5-5 01:18:12
 */
@Slf4j
@Service
class RepairProductServiceImpl(
    private val productRepository: ProductRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,

    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val callLazadaComponent: CallLazadaComponent,

    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val callAeComponent: CallAeComponent,
) : RepairProductService {

    /**
     * 商品详情-Lazada
     */
    override fun lazadaDetail(req: RepairLazadaProductDetailReq): List<RepairLazadaProductDetailResp> {
        val saleGoodsList = saleGoodsRepository.listByProductIdAndShopId(req.productId!!, req.shopId!!)
        if (saleGoodsList.isEmpty()) {
            throw IllegalArgumentException("销售商品不存在")
        }
        val respList = mutableListOf<RepairLazadaProductDetailResp>()
        saleGoodsList.forEach { saleGoods ->
            val product = productRepository.getById(saleGoods.productId!!)
            val saleSkcList = saleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
            if (saleSkcList.isEmpty()) {
                throw IllegalArgumentException("销售SKC不存在")
            }
            val saleSkuList = saleSkuRepository.getBySaleGoodsId(saleGoods.saleGoodsId!!)
            if (saleSkuList.isEmpty()) {
                throw IllegalArgumentException("销售SKU不存在")
            }
            val resp = RepairLazadaProductDetailResp().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
                this.country = saleGoods.country
                this.spuCode = saleGoods.spuCode
                this.sizeGroupCode = product.sizeGroupCode
                this.sizeGroupName = product.sizeGroupName
                this.errorState = saleGoods.errorState
                this.errorInfoDtoList = saleGoods.errorInfo?.parseJsonList(ProductErrorInfoDto::class.java) ?: emptyList()
                this.skcList = saleSkcList.map { saleSkc ->
                    RepairLazadaProductDetailResp.RepairLazadaProductDetailSkcResp().apply {
                        this.saleSkcId = saleSkc.saleSkcId
                        this.skuImg = saleSkc.pictures
                        this.combo = saleSkc.combo
                        this.skc = saleSkc.skc
                        this.platformColor = saleSkc.platformColor
                        this.innerColor = saleSkc.color
                        this.innerColorCode = saleSkc.colorCode
                        this.skuList = saleSkuList
                            .filter { saleSku -> saleSku.saleSkcId == saleSkc.saleSkcId }
                            .map { saleSku ->
                                RepairLazadaProductDetailResp.RepairLazadaProductDetailSkuResp().apply {
                                    this.saleSkuId = saleSku.saleSkuId
                                    this.barcode = saleSku.barcode
                                    this.barcodes = saleSku.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                    this.sellerSku = saleSku.sellerSku
                                    this.size = saleSku.sizeName
                                }
                            }
                    }
                }
            }
            respList.add(resp)
        }
        return respList
    }

    /**
     * 商品详情-AE
     */
    override fun aeDetail(req: RepairAeProductDetailReq): RepairAeProductDetailResp {
        val saleGoods = aeSaleGoodsRepository.getById(req.saleGoodsId)
        if (saleGoods == null) {
            throw IllegalArgumentException("销售商品不存在")
        }
        val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (saleSkcList.isEmpty()) {
            throw IllegalArgumentException("销售SKC不存在")
        }
        val saleSkuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (saleSkuList.isEmpty()) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        val product = productRepository.getById(saleGoods.productId!!)
        return RepairAeProductDetailResp().apply {
            this.saleGoodsId = saleGoods.saleGoodsId
            this.spuCode = saleGoods.spuCode
            this.sizeGroupCode = product.sizeGroupCode
            this.sizeGroupName = product.sizeGroupName
            this.errorState = saleGoods.errorState
            this.errorInfoDtoList = saleGoods.errorInfo?.parseJsonList(ProductErrorInfoDto::class.java) ?: emptyList()
            this.skcList = saleSkcList.map { saleSkc ->
                RepairAeProductDetailResp.RepairAeProductDetailSkcResp().apply {
                    this.saleSkcId = saleSkc.saleSkcId
                    this.skuImg = saleSkc.pictures
                    this.combo = saleSkc.combo
                    this.skc = saleSkc.skc
                    this.platformColor = saleSkc.platformColor
                    this.innerColor = saleSkc.color
                    this.innerColorCode = saleSkc.colorCode
                    this.skuList = saleSkuList
                        .filter { saleSku -> saleSku.saleSkcId == saleSkc.saleSkcId }
                        .map { saleSku ->
                            RepairAeProductDetailResp.RepairAeProductDetailSkuResp().apply {
                                this.saleSkuId = saleSku.saleSkuId
                                this.barcode = saleSku.barcode
                                this.barcodes = saleSku.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                this.sellerSku = saleSku.sellerSku
                                this.size = saleSku.sizeName
                            }
                        }
                }
            }
        }
    }

    /**
     * 更新商品(修复)-Lazada
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun lazadaUpdate(req: List<RepairLazadaProductUpdateReq>) {

        val saleGoodsIdList = req.mapNotNull { it.saleGoodsId }
        val saleGoodsList = saleGoodsRepository.listByIds(saleGoodsIdList)
        val saleGoodsIdMap = saleGoodsList.associateBy { it.saleGoodsId }

        val saleSkcIdList = req.flatMap { it.skcList.mapNotNull { skc -> skc.saleSkcId } }
        val saleSkcList = saleSkcRepository.listByIds(saleSkcIdList)
        val saleSkcIdMap = saleSkcList.associateBy { it.saleSkcId }

        val saleSkuIdList = req.flatMap { it.skcList.flatMap { skc -> skc.skuList.mapNotNull { sku -> sku.saleSkuId } } }
        val saleSkuList = saleSkuRepository.listByIds(saleSkuIdList)
        val saleSkuIdMap = saleSkuList.associateBy { it.saleSkuId }

        req.forEach { spu ->
            val saleGoods = saleGoodsIdMap[spu.saleGoodsId]
            if (saleGoods == null) {
                log.error { "销售商品不存在: saleGoodsId=${spu.saleGoodsId}" }
                return@forEach
            }

            val product = productRepository.getById(saleGoods.productId!!)

            spu.skcList.forEach { skc ->
                val saleSkc = saleSkcIdMap[skc.saleSkcId]
                if (saleSkc == null) {
                    log.error { "销售商品Skc不存在: SaleSkcId=${skc.saleSkcId}" }
                    return@forEach
                }
                // 更新SKC
                var isUpdate = false
                val updateSkc = SaleSkc().apply {
                    this.saleSkcId = saleSkc.saleSkcId
                }
                if (skc.combo == Bool.YES.code) {
                    // 组合商品, 处理color, 找到内部颜色和条码, 更新
                    val color = getComboColorByLazada(skc)
                    if (color.isNotBlank() && color != saleSkc.color) {
                        updateSkc.color = color
                        saleSkc.color = color
                        isUpdate = true
                    }
                } else {
                    if (skc.innerColor.isNotBlank() && skc.innerColor != saleSkc.color) {
                        updateSkc.color = skc.innerColor
                        saleSkc.color = skc.innerColor
                        isUpdate = true
                    }
                }
                if (skc.innerColorCode.isNotBlank() && skc.innerColorCode != saleSkc.colorCode) {
                    updateSkc.colorCode = skc.innerColorCode
                    saleSkc.colorCode = skc.innerColorCode
                    isUpdate = true
                }
                if (skc.combo != null && skc.combo != saleSkc.combo) {
                    updateSkc.combo = skc.combo
                    saleSkc.combo = skc.combo
                    isUpdate = true
                }
                if (skc.skc.isNotBlank() && skc.skc != saleSkc.skc) {
                    updateSkc.skc = skc.skc
                    saleSkc.skc = skc.skc
                    isUpdate = true
                }
                if (skc.platformColor.isNotBlank() && saleSkc.platformColor.isBlank()) {
                    updateSkc.platformColor = skc.platformColor
                    saleSkc.platformColor = skc.platformColor
                    isUpdate = true
                }
                if (isUpdate) {
                    saleSkcRepository.updateById(updateSkc)
                }
                // 循环SKU
                skc.skuList.forEach { sku ->
                    val saleSku = saleSkuIdMap[sku.saleSkuId]
                    if (saleSku == null) {
                        log.error { "销售商品Sku不存在: SaleSkuId=${sku.saleSkuId}" }
                        return@forEach
                    }
                    // 更新SKU
                    var isUpdate = false
                    val updateSku = SaleSku().apply {
                        this.saleSkuId = saleSku.saleSkuId
                    }
                    if (skc.combo == Bool.YES.code) {
                        // 组合商品
                        if (sku.barcodes.isNotEmpty() && sku.barcodes!!.toJson() != saleSku.barcodes) {
                            updateSku.barcodes = sku.barcodes?.toJson()
                            isUpdate = true
                        }
                    } else {
                        // 非组合商品
                        // 先通过skc找到条码, 如果找不到就生成
                        var tmpBarcode: String? = null
                        val productBarcode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(saleGoods.spuCode!!, saleSkc.skc!!, saleSku.sizeName!!)
                        if (productBarcode == null) {
                            // 条码不存在, 生成条码
                            // 添加条码
                            val barcodeReq = BatchCreateBarCodeReq().apply {
                                this.categoryCode = product.categoryCode
                                this.categoryName = product.categoryName
                                this.skcCode = saleSkc.skc
                                this.color = saleSkc.color
                                this.spuCode = product.spuCode
                                this.groupName = product.sizeGroupName
                                this.sourceGroupCode = product.sizeGroupCode
                                this.sizeValues = listOf(sku.size!!)
                                this.inspiraImgUrl = product.inspiraImgUrl
                                this.supplyMode = product.supplyMode
                                this.localPrice = saleSkc.localPrice
                                this.designImgUrl = saleSkc.pictures
                                this.mainImgUrl = saleSkc.pictures
                            }
                            val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                            if (CollectionUtils.isNotEmpty(barcodeResp)) {
                                tmpBarcode = barcodeResp[0].barcode
                            }
                        } else {
                            // 条码存在, 比较条码是否一致
                            tmpBarcode = productBarcode.barcode
                        }
                        if (tmpBarcode.isNotBlank() && tmpBarcode != saleSku.barcode) {
                            updateSku.barcode = tmpBarcode
                            isUpdate = true
                        }
                    }
//                    if (sku.sellerSku.isNotBlank() && sku.sellerSku != saleSku.sellerSku) {
//                        updateSku.sellerSku = sku.sellerSku
//                        isUpdate = true
//                    }
                    if (isUpdate) {
                        saleSkuRepository.updateById(updateSku)
                    }
                }
            }
            // 绑定sellerSku和条码关系
            callLazadaComponent.bindBarcode(product, saleGoods)
            // 重新校验异常
            lazadaCheckError(saleGoods)
        }
    }

    /**
     * 更新商品(修复)-AE
     */
    @Transactional(rollbackFor = [Exception::class])
    override fun aeUpdate(req: RepairAeProductUpdateReq) {
        val saleGoods = aeSaleGoodsRepository.getById(req.saleGoodsId)
        if (saleGoods == null) {
            log.error { "销售商品不存在: saleGoodsId=${req.saleGoodsId}" }
            throw IllegalArgumentException("销售商品不存在")
        }

        val saleSkcIdList = req.skcList.mapNotNull { skc -> skc.saleSkcId }
        val saleSkcList = aeSaleSkcRepository.listByIds(saleSkcIdList)
        val saleSkcIdMap = saleSkcList.associateBy { it.saleSkcId }

        val saleSkuIdList = req.skcList.flatMap { it.skuList.mapNotNull { sku -> sku.saleSkuId } }
        val saleSkuList = aeSaleSkuRepository.listByIds(saleSkuIdList)
        val saleSkuIdMap = saleSkuList.associateBy { it.saleSkuId }

        val product = productRepository.getById(saleGoods.productId!!)

        req.skcList.forEach { skc ->
            val saleSkc = saleSkcIdMap[skc.saleSkcId]
            if (saleSkc == null) {
                log.error { "销售商品Skc不存在: SaleSkcId=${skc.saleSkcId}" }
                return@forEach
            }


            // 更新SKC
            var isUpdate = false
            val updateSkc = AeSaleSkc().apply {
                this.saleSkcId = saleSkc.saleSkcId
            }
            if (skc.combo == Bool.YES.code) {
                // 组合商品, 处理color, 找到内部颜色和条码, 更新
                val color = getComboColorByAe(skc)
                if (color.isNotBlank() && color != saleSkc.color) {
                    updateSkc.color = color
                    saleSkc.color = color
                    isUpdate = true
                }
                // 组合商品colorCode使用平台颜色
                if (skc.platformColor.isNotBlank() && skc.platformColor != saleSkc.colorCode) {
                    updateSkc.colorCode = skc.platformColor
                    saleSkc.colorCode = skc.platformColor
                    isUpdate = true
                }
            } else {
                if (skc.innerColor.isNotBlank() && skc.innerColor != saleSkc.color) {
                    updateSkc.color = skc.innerColor
                    saleSkc.color = skc.innerColor
                    isUpdate = true
                }
                if (skc.innerColorCode.isNotBlank() && skc.innerColorCode != saleSkc.colorCode) {
                    updateSkc.colorCode = skc.innerColorCode
                    saleSkc.colorCode = skc.innerColorCode
                    isUpdate = true
                }
            }
            if (skc.combo != null && skc.combo != saleSkc.combo) {
                updateSkc.combo = skc.combo
                saleSkc.combo = skc.combo
                isUpdate = true
            }
            if (skc.skc.isNotBlank() && skc.skc != saleSkc.skc) {
                updateSkc.skc = skc.skc
                saleSkc.skc = skc.skc
                isUpdate = true
            }
            if (skc.platformColor.isNotBlank() && saleSkc.platformColor.isBlank()) {
                updateSkc.platformColor = skc.platformColor
                saleSkc.platformColor = skc.platformColor
                isUpdate = true
            }
            if (isUpdate) {
                aeSaleSkcRepository.updateById(updateSkc)
            }
            // 循环SKU
            skc.skuList.forEach { sku ->
                val saleSku = saleSkuIdMap[sku.saleSkuId]
                if (saleSku == null) {
                    log.error { "销售商品Sku不存在: SaleSkuId=${sku.saleSkuId}" }
                    return@forEach
                }
                // 更新SKU
                var isUpdate = false
                val updateSku = AeSaleSku().apply {
                    this.saleSkuId = saleSku.saleSkuId
                }
                if (skc.combo == Bool.YES.code) {
                    // 组合商品
                    if (sku.barcodes.isNotEmpty() && sku.barcodes!!.toJson() != saleSku.barcodes) {
                        updateSku.barcodes = sku.barcodes?.toJson()
                        isUpdate = true
                    }
                } else {
                    // 非组合商品
                    // 先通过skc找到条码, 如果找不到就生成
                    var tmpBarcode: String? = null
                    val productBarcode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(saleGoods.spuCode!!, saleSkc.skc!!, saleSku.sizeName!!)
                    if (productBarcode == null) {
                        // 条码不存在, 生成条码
                        // 添加条码
                        val barcodeReq = BatchCreateBarCodeReq().apply {
                            this.categoryCode = product.categoryCode
                            this.categoryName = product.categoryName
                            this.skcCode = saleSkc.skc
                            this.color = saleSkc.color
                            this.spuCode = product.spuCode
                            this.groupName = product.sizeGroupName
                            this.sourceGroupCode = product.sizeGroupCode
                            this.sizeValues = listOf(sku.size!!)
                            this.inspiraImgUrl = product.inspiraImgUrl
                            this.supplyMode = product.supplyMode
                            this.localPrice = saleSkc.localPrice
                            this.designImgUrl = saleSkc.pictures
                            this.mainImgUrl = saleSkc.pictures
                        }
                        val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                        if (CollectionUtils.isNotEmpty(barcodeResp)) {
                            tmpBarcode = barcodeResp[0].barcode
                        }
                    } else {
                        // 条码存在, 比较条码是否一致
                        tmpBarcode = productBarcode.barcode
                    }
                    if (tmpBarcode.isNotBlank() && tmpBarcode != saleSku.barcode) {
                        updateSku.barcode = tmpBarcode
                        isUpdate = true
                    }
                }
//                if (sku.sellerSku.isNotBlank() && sku.sellerSku != saleSku.sellerSku) {
//                    updateSku.sellerSku = sku.sellerSku
//                    isUpdate = true
//                }
                if (isUpdate) {
                    aeSaleSkuRepository.updateById(updateSku)
                }
            }
        }
        // 绑定sellerSku和条码关系
        callAeComponent.bindBarcode(product, saleGoods)
        // 重新校验异常
        aeCheckError(saleGoods)
    }

    /**
     * 检查商品异常-Lazada
     */
    override fun lazadaCheckError(saleGoods: SaleGoods) {
        // 记录原来的异常信息
        val sourceIsError = saleGoods.errorState
        val sourceErrorInfo = saleGoods.errorInfo?.parseJsonList(ProductErrorInfoDto::class.java)?.toMutableList() ?: mutableListOf()
        // 新的异常信息
        var newIsError = Bool.NO.code
        val tmpErrorInfo = mutableListOf<ProductErrorInfoDto>()

        // 检查skc
        val skcList = saleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (skcList.isNotEmpty()) {
            // 有一个非组合商品的skc为空, 则标记为异常
            val skcNull = skcList.filter { it.combo == Bool.NO.code }.any { it.skc.isBlank() }
            if (skcNull) {
                val dto = ProductErrorInfoDto().apply {
                    this.errorType = ProductErrorTypeEnum.SKC_ERROR.code
                    this.errorMsg = "SKC为空"
                }
                tmpErrorInfo.add(dto)
                newIsError = Bool.YES.code
            }
        }

        // 检查sku
        val skuList = saleSkuRepository.getBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (skuList.isNotEmpty()) {
            // skc map
            val skcMap = skcList.associateBy { it.saleSkcId }
            // 循环检查sku
            skuList.forEach { sku ->
                val skc = skcMap[sku.saleSkcId]
                if (skc != null) {
                    val barcodePair = checkBarcode(skc.combo, sku.barcode, sku.sellerSkuFlatId)
                    if (barcodePair.first == Bool.YES.code && barcodePair.second != null) {
                        tmpErrorInfo.add(barcodePair.second!!)
                        newIsError = barcodePair.first
                    }
                }
            }
        }

        // 合并新旧异常信息
        val newErrorInfo = mergeErrorMsg(tmpErrorInfo, sourceErrorInfo)

        // 判断异常信息是否一致, 一致则不需更新
        if (Objects.equals(sourceIsError, newIsError) && equalsJson(sourceErrorInfo.toJson(), newErrorInfo.toJson())) {
            // 不用更新
            return
        }
        val updateSaleGoods = SaleGoods().apply {
            this.saleGoodsId = saleGoods.saleGoodsId
            this.errorState = newIsError
            this.errorInfo = if (newIsError == Bool.YES.code) newErrorInfo.toJson() else "[]"
        }
        saleGoodsRepository.updateById(updateSaleGoods)
    }

    /**
     * 检查商品异常-AE
     */
    override fun aeCheckError(saleGoods: AeSaleGoods) {
        // 记录原来的异常信息
        val sourceIsError = saleGoods.errorState
        val sourceErrorInfo = saleGoods.errorInfo?.parseJsonList(ProductErrorInfoDto::class.java)?.toMutableList() ?: mutableListOf()
        // 新的异常信息
        var newIsError = Bool.NO.code
        val tmpErrorInfo = mutableListOf<ProductErrorInfoDto>()

        // 检查skc
        val skcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (skcList.isNotEmpty()) {
            // 有一个非组合商品的skc为空, 则标记为异常
            val skcNull = skcList.filter { it.combo == Bool.NO.code }.any { it.skc.isBlank() }
            if (skcNull) {
                val dto = ProductErrorInfoDto().apply {
                    this.errorType = ProductErrorTypeEnum.SKC_ERROR.code
                    this.errorMsg = "SKC为空"
                }
                tmpErrorInfo.add(dto)
                newIsError = Bool.YES.code
            }
        }

        // 检查sku
        val skuList = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (skuList.isNotEmpty()) {
            // skc map
            val skcMap = skcList.associateBy { it.saleSkcId }
            // 循环检查sku
            skuList.forEach { sku ->
                val skc = skcMap[sku.saleSkcId]
                if (skc != null) {
                    val barcodePair = checkBarcode(skc.combo, sku.barcode, sku.sellerSkuFlatId)
                    if (barcodePair.first == Bool.YES.code && barcodePair.second != null) {
                        tmpErrorInfo.add(barcodePair.second!!)
                        newIsError = barcodePair.first
                    }
                }
            }
        }

        // 合并新旧异常信息
        val newErrorInfo = mergeErrorMsg(tmpErrorInfo, sourceErrorInfo)

        // 判断异常信息是否一致, 一致则不需更新
        if (Objects.equals(sourceIsError, newIsError) && equalsJson(sourceErrorInfo.toJson(), newErrorInfo.toJson())) {
            // 不用更新
            return
        }
        val updateSaleGoods = AeSaleGoods().apply {
            this.saleGoodsId = saleGoods.saleGoodsId
            this.errorState = newIsError
            this.errorInfo = if (newIsError == Bool.YES.code) newErrorInfo.toJson() else "[]"
        }
        aeSaleGoodsRepository.updateById(updateSaleGoods)
    }

    /**
     * 比较两个json是否一致
     * @param json1
     * @param json2
     * @return
     */
    private fun equalsJson(json1: String?, json2: String?): Boolean {
        // 都为空, 返回true
        if (StringUtils.isBlank(json1) && StringUtils.isBlank(json2)) {
            return true
        }
        // 其中一个为空, 返回false
        if (StringUtils.isBlank(json1) || StringUtils.isBlank(json2)) {
            return false
        }
        val objectMapper = ObjectMapper()
        try {
            // 将JSON字符串转换为JsonNode
            val node1 = objectMapper.readTree(json1)
            val node2 = objectMapper.readTree(json2)
            // 比较两个JsonNode对象是否相等
            return node1.equals(node2)
        } catch (e: JsonProcessingException) {
            log.error(e) { "比较json异常: ${e.message}" }
            return false
        }
    }

    /**
     * 合并
     * @param errorList
     * @param tmpErrorList
     */
    private fun mergeErrorMsg(errorList: MutableList<ProductErrorInfoDto>, tmpErrorList: List<ProductErrorInfoDto>): List<ProductErrorInfoDto> {
        if (CollectionUtils.isNotEmpty(tmpErrorList)) {
            // 根据type分组
            tmpErrorList.groupBy { it.errorType }
                .forEach { (k, v) ->
                    val dto = ProductErrorInfoDto()
                    dto.errorType = k
                    dto.errorMsg = v.map { it.errorMsg }.distinct().joinToString(",")
                    errorList.add(dto)
                }
        }
        // 去重
        return errorList
            .groupBy { it.errorType }
            .map { (k, v) ->
                ProductErrorInfoDto().apply {
                    this.errorType = k
                    this.errorMsg = v.map { it.errorMsg }.distinct().joinToString(",")
                }
            }
    }

    /**
     * 检查条码关系
     */
    private fun checkBarcode(combo: Int?, barcode: String?, sellerSkuFlatId: Long?): Pair<Int, ProductErrorInfoDto?> {
        if (combo == Bool.NO.code) {
            // 有一个非组合商品的条码为空, 则标记为异常
            if (barcode.isBlank()) {
                val dto = ProductErrorInfoDto().apply {
                    this.errorType = ProductErrorTypeEnum.BARCODE_ERROR.code
                    this.errorMsg = "非组合商品的条码为空"
                }
                return Pair(Bool.YES.code, dto)
            }
        } else {
            // 有一个组合商品的barcodes为空(flatId为空), 则标记为异常
            if (sellerSkuFlatId.isNull()) {
                val dto = ProductErrorInfoDto().apply {
                    this.errorType = ProductErrorTypeEnum.BARCODE_ERROR.code
                    this.errorMsg = "组合商品的条码为空"
                }
                return Pair(Bool.YES.code, dto)
            }
        }
        return Pair(Bool.NO.code, null)
    }

    /**
     * 获取组合颜色
     */
    private fun getComboColorByLazada(skcReq: RepairLazadaProductUpdateReq.RepairLazadaProductUpdateSkcReq): String {
        // 提取sku下的所有barcode
        val barcodeList = skcReq.skuList.map { it.barcodes }.mapNotNull { it?.mapNotNull { it.barcode } }.flatten().distinct()
        require(!(barcodeList.isEmpty())) { "组合商品必须有条码" }
        val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
        return barcodeInfoList?.mapNotNull { it.color?.trim() }?.distinct()?.sorted()?.joinToString("+") ?: ""
    }

    /**
     * 获取组合颜色
     */
    private fun getComboColorByAe(skcReq: RepairAeProductUpdateReq.RepairAeProductUpdateSkcReq): String {
        // 提取sku下的所有barcode
        val barcodeList = skcReq.skuList.map { it.barcodes }.mapNotNull { it?.mapNotNull { it.barcode } }.flatten().distinct()
        require(!(barcodeList.isEmpty())) { "组合商品必须有条码" }
        val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
        return barcodeInfoList?.mapNotNull { it.color?.trim() }?.distinct()?.sorted()?.joinToString("+") ?: ""
    }
}