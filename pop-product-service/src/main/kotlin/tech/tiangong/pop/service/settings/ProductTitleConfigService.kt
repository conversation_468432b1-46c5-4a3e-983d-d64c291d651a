package tech.tiangong.pop.service.settings

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.settings.*
import tech.tiangong.pop.resp.settings.ProductTitleConfigResp

/**
 * 商品标题配置服务
 */
interface ProductTitleConfigService {

    /**
     * 分页查询标题配置
     *
     * @param req 查询请求
     * @return 分页结果
     */
    fun pageConfig(req: ProductTitleConfigPageQueryReq): PageVo<ProductTitleConfigResp>

    /**
     * 新建标题配置
     *
     * @param req 新建请求
     * @return 配置ID
     */
    fun createConfig(req: ProductTitleConfigCreateReq): Long

    /**
     * 修改标题数量
     *
     * @param req 修改请求
     * @return 是否成功
     */
    fun updateTitleCount(req: ProductTitleConfigUpdateCountReq): Boolean

    /**
     * 标题规则应用
     *
     * @param req 规则应用请求
     * @return 是否成功
     */
    fun applyRule(req: ProductTitleConfigApplyRuleReq): Boolean

    /**
     * 店铺应用
     *
     * @param req 店铺应用请求
     * @return 是否成功
     */
    fun applyShop(req: ProductTitleConfigApplyShopReq): Boolean

    /**
     * 根据平台ID查找配置
     *
     * @param platformId 平台ID
     * @return 标题配置响应
     */
    fun findByPlatformId(platformId: Long): ProductTitleConfigResp?

    /**
     * 根据平台ID和店铺ID查找配置（用于获取商品标题）
     *
     * @param platformId 平台ID
     * @param shopId 店铺ID
     * @return 标题配置响应
     */
    fun findByPlatformAndShop(platformId: Long, shopId: Long?): ProductTitleConfigResp?

    /**
     * 根据配置ID获取标题配置详情
     *
     * @param configId 配置ID
     * @return 标题配置响应
     */
    fun getDetail(configId: Long): ProductTitleConfigResp
} 