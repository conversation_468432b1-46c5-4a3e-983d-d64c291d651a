package tech.tiangong.pop.service.regionpricerule.impl

import com.alibaba.excel.EasyExcel
import com.alibaba.fastjson2.parseArray
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.Assert
import org.springframework.web.multipart.MultipartFile
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleFreightRateConfigDto
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleFreightRateConfigDto.*
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleLogisticsConfigDto
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleLogisticsConfigDto.*
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleStorageConfigDto
import tech.tiangong.pop.dao.entity.dto.RegionPriceRuleStorageConfigDto.*
import tech.tiangong.pop.dao.entity.dto.TaxRateRuleDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.enums.RegionPriceRuleTaxRateTypeEnum
import tech.tiangong.pop.enums.SupplyModeEnum
import tech.tiangong.pop.external.DictClientExternal
import tech.tiangong.pop.listeners.ImportAERegionPriceRuleFreightRateExcelListener
import tech.tiangong.pop.listeners.ImportAERegionPriceRuleLogisticsExcelListener
import tech.tiangong.pop.req.regionpricerule.*
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleFreightRateReq.*
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleLogisticsReq.*
import tech.tiangong.pop.req.regionpricerule.SaveRegionPriceRuleStorageReq.*
import tech.tiangong.pop.resp.regionpricerule.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleFreightRateResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleLogisticsResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleStorageResp.*
import tech.tiangong.pop.resp.regionpricerule.RegionPriceRuleTaxRateResp.TaxRateRuleResp
import tech.tiangong.pop.service.regionpricerule.RegionPriceRuleManageService
import java.math.BigDecimal
import kotlin.collections.component1
import kotlin.collections.component2

/**
 * 区域价格配置服务实现
 */
@Slf4j
@Service
class RegionPriceRuleManageServiceImpl(
    private val regionPriceRuleAdRepository: RegionPriceRuleAdRepository,
    private val regionPriceRuleCommissionRepository: RegionPriceRuleCommissionRepository,
    private val regionPriceRuleDiscountRateRepository: RegionPriceRuleDiscountRateRepository,
    private val regionPriceRuleFreightRateRepository: RegionPriceRuleFreightRateRepository,
    private val regionPriceRuleGrossMarginRepository: RegionPriceRuleGrossMarginRepository,
    private val regionPriceRuleLogisticsRepository: RegionPriceRuleLogisticsRepository,
    private val regionPriceRuleMarketingRepository: RegionPriceRuleMarketingRepository,
    private val regionPriceRuleRejectRateRepository: RegionPriceRuleRejectRateRepository,
    private val regionPriceRuleStorageRepository: RegionPriceRuleStorageRepository,
    private val regionPriceRuleTaxRateRepository: RegionPriceRuleTaxRateRepository,
    private val regionPriceRuleWithdrawRepository: RegionPriceRuleWithdrawRepository,
    private val shopRepository: ShopRepository,
    private val dictClientExternal: DictClientExternal
) : RegionPriceRuleManageService {

    //保存物流费用
    @Transactional(rollbackFor = [Exception::class])
    override fun saveLogisticsRule(req: SaveRegionPriceRuleLogisticsReq) {
        val rules = mutableListOf<RegionPriceRuleLogistics>()
        when (req.platform) {
            PlatformEnum.LAZADA -> {
                Assert.isTrue(
                    req.lazadaConfigReqList.isNotEmpty(),
                    "LAZADA物流费用参数不能为空"
                )
                rules.add(doSaveLazadaLogisticsConfig(req.lazadaConfigReqList!!))
            }

            PlatformEnum.AE -> {
                Assert.isTrue(
                    req.aeConfigReqList.isNotEmpty(),
                    "AE物流费用参数不能为空"
                )
                rules.add(doSaveAELogisticsConfig(req.aeConfigReqList!!))
            }

            PlatformEnum.TEMU -> {
                Assert.isTrue(
                    req.temuConfigReqList.isNotEmpty(),
                    "TEMU物流费用参数不能为空"
                )
                rules.add(doSaveTemuLogisticsConfig(req.temuConfigReqList!!))
            }

            else -> throw BusinessException("不支持平台ID配置${req.platform}")
        }

        //先删除旧数据
        regionPriceRuleLogisticsRepository.ktUpdate()
            .eq(RegionPriceRuleLogistics::platformId, req.platform!!.platformId)
            .eq(RegionPriceRuleLogistics::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        if (rules.isNotEmpty()) {
            regionPriceRuleLogisticsRepository.saveBatch(rules)
        }
    }


    // AE物流费用配置导入
    @Transactional(rollbackFor = [Exception::class])
    override fun importAELogisticsRule(file: MultipartFile) {
        val listener = ImportAERegionPriceRuleLogisticsExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()
        val importList = listener.getRuleList()
            .asSequence()
            .filter { it.shippingPlaceCn.isNotBlank() }
            .filter { it.receivingPlaceCn.isNotBlank() }
            .filter { it.cost.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入AE物流费用配置数据为空")
        }
        //发货地
        val aeShippingFrom = dictClientExternal.listByDictCode(DictEnum.AE_SHIPPING_FROM.dictCode)
        Assert.isTrue(aeShippingFrom.isNotNull(), "AE发货地字典配置不存在")
        //目的地
        val aeShippingTo = dictClientExternal.listByDictCode(DictEnum.AE_SHIPPING_TO.dictCode)
        Assert.isTrue(aeShippingFrom.isNotNull(), "AE目的地字典配置不存在")
        val req = SaveRegionPriceRuleLogisticsReq().apply {
            this.platform = PlatformEnum.AE
            var aeConfigReqList: MutableList<AEConfigReq> = mutableListOf()
            val shippingPlaceToReceivePlaceListMap = importList.groupBy { it.shippingPlaceCn }
            shippingPlaceToReceivePlaceListMap.forEach { (shippingPlaceCn, receivePlaceList) ->
                aeConfigReqList.add(AEConfigReq().apply {
                    val shippingFrom = getAEShippingFromOrToByName(aeShippingFrom!!, shippingPlaceCn!!)
                    this.shippingPlaceId = shippingFrom?.remark?.toLong()
                    this.shippingPlace = shippingFrom?.dictCode
                    this.shippingPlaceCn = shippingPlaceCn
                    this.receivingPlaceLogisticsCostList = receivePlaceList.map {
                        SaveReceivingPlaceLogisticsCostReq().apply {
                            val shippingTo = getAEShippingFromOrToByName(aeShippingTo!!, it.receivingPlaceCn!!)
                            this.receivingPlace = shippingTo?.dictCode
                            this.receivingPlaceCn = it.receivingPlaceCn
                            this.cost = BigDecimal(it.cost)
                            this.defaultRule = if (it.defaultRule!=null && it.defaultRule.equals("是")) Bool.YES.code else Bool.NO.code
                        }
                    }
                })
            }
            this.aeConfigReqList = aeConfigReqList
        }

        saveLogisticsRule(req)
    }

    private fun getAEShippingFromOrToByName(dict: DictVo, chineseName: String): DictVo? {
        dict.children?.forEach { shippingFrom ->
            if (shippingFrom.dictName == chineseName) {
                return shippingFrom
            }
        }
        return null
    }

    private fun doSaveLazadaLogisticsConfig(req: List<LazadaConfigReq>): RegionPriceRuleLogistics {
        val config = RegionPriceRuleLogisticsConfigDto()
        config.lazadaConfigDtoList = req.map {
            Assert.isTrue(it.country.isNotBlank() && it.cost.isNotNull(), "请选择国家站点")
            LazadaConfigDto().apply {
                this.country = it.country
                this.cost = it.cost
            }
        }.toList()
        return RegionPriceRuleLogistics().apply {
            this.platformId = PlatformEnum.LAZADA.platformId
            this.logisticsRuleId = IdHelper.getId()
            this.ruleConfig = config.toJson()
        }
    }

    private fun doSaveTemuLogisticsConfig(req: List<TemuConfigReq>): RegionPriceRuleLogistics {
        val config = RegionPriceRuleLogisticsConfigDto()
        config.temuConfigDtoList = req.map {
            Assert.isTrue(it.country.isNotBlank() && it.cost.isNotNull(), "请选择国家站点")
            TemuConfigDto().apply {
                this.country = it.country
                this.cost = it.cost
            }
        }.toList()
        return RegionPriceRuleLogistics().apply {
            this.platformId = PlatformEnum.TEMU.platformId
            this.logisticsRuleId = IdHelper.getId()
            this.ruleConfig = config.toJson()
        }
    }

    private fun doSaveAELogisticsConfig(req: List<AEConfigReq>): RegionPriceRuleLogistics {
        val shippingPlaceDefaultCountMap: MutableMap<String, Int> = mutableMapOf()
        req.forEach { ruleReq ->
            val count =
                ruleReq.receivingPlaceLogisticsCostList.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            ruleReq.shippingPlace?.let {
                if (shippingPlaceDefaultCountMap.contains(ruleReq.shippingPlace)) {
//                    shippingPlaceDefaultCountMap.put(it,count + shippingPlaceDefaultCountMap.getOrDefault(ruleReq.shippingPlace,0))
                    throw BusinessException("发货地重复${ruleReq.shippingPlace}")
                } else {
                    shippingPlaceDefaultCountMap[it] = count
                }
            }
        }
        shippingPlaceDefaultCountMap.forEach { (shippingPlace, defaultCount) ->
            if (defaultCount == 0) {
                throw BusinessException("发货地${shippingPlace}没有设置默认物流费用")
            }
            if (defaultCount > 1) {
                throw BusinessException("发货地${shippingPlace}只允许设置一个默认物流费用")
            }
        }

        val config = RegionPriceRuleLogisticsConfigDto()
        val aeConfigDtoList: MutableList<AEConfigDto> = mutableListOf()
        req.forEach { ruleReq ->
            ruleReq.receivingPlaceLogisticsCostList.forEach {
                aeConfigDtoList.add(AEConfigDto().apply {
                    this.shippingPlaceId = ruleReq.shippingPlaceId
                    this.shippingPlace = ruleReq.shippingPlace
                    this.shippingPlaceCn = ruleReq.shippingPlaceCn
                    this.receivingPlace = it.receivingPlace
                    this.receivingPlaceCn = it.receivingPlaceCn
                    this.cost = it.cost
                    this.defaultRule = it.defaultRule ?: Bool.NO.code
                })
            }
        }
        config.aeConfigDtoList = aeConfigDtoList
        return RegionPriceRuleLogistics().apply {
            this.platformId = PlatformEnum.AE.platformId
            this.logisticsRuleId = IdHelper.getId()
            this.ruleConfig = config.toJson()
        }
    }

    //查询物流费用
    override fun getLogisticsRuleByPlatform(platform: PlatformEnum): RegionPriceRuleLogisticsResp? {
        val logisticsConfig = regionPriceRuleLogisticsRepository.ktQuery()
            .eq(RegionPriceRuleLogistics::platformId, platform.platformId)
            .one()
        return logisticsConfig?.let {
            val resp = RegionPriceRuleLogisticsResp()
            resp.logisticsRuleId = it.logisticsRuleId
            resp.platformId = it.platformId
            val configDto = it.ruleConfig?.parseJson<RegionPriceRuleLogisticsConfigDto>()
            resp.aeConfig = configDto?.aeConfigDtoList?.let {
                val aeConfigList: MutableList<AEConfigResp> = mutableListOf()
                //按发货地分组
                val shippingPlaceToMap =
                    it.groupBy { "${it.shippingPlaceId}-${it.shippingPlace}-${it.shippingPlaceCn}" }
                shippingPlaceToMap.forEach { (key, list2) ->
                    val costRespList = list2.map {
                        val costResp = ReceivingPlaceLogisticsCostResp(
                            it.receivingPlace,
                            it.receivingPlaceCn,
                            it.cost,
                            it.defaultRule
                        )
                        costResp
                    }.toList()
                    val shippingPlaces = key.split("-")
                    aeConfigList.add(
                        AEConfigResp(
                            shippingPlaces[0].toLong(),
                            shippingPlaces[1],
                            shippingPlaces[2],
                            costRespList
                        )
                    )
                }
                aeConfigList
            }
            resp.lazadaConfig = configDto?.lazadaConfigDtoList?.map {
                val lazadaConfigResp = LazadaConfigResp()
                BeanUtils.copyProperties(it, lazadaConfigResp)
                lazadaConfigResp
            }
            resp.temuConfig = configDto?.temuConfigDtoList?.map {
                val temuConfigResp = TemuConfigResp()
                BeanUtils.copyProperties(it, temuConfigResp)
                temuConfigResp
            }
            return resp
        }
    }

    //保存仓储成本配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveStorageRule(req: SaveRegionPriceRuleStorageReq) {
        val rules = mutableListOf<RegionPriceRuleStorage>()
        when (req.platform) {
            PlatformEnum.LAZADA -> {
                Assert.isTrue(
                    req.lazadaStorageRuleList.isNotNull() && req.lazadaStorageRuleList!!.isNotEmpty(),
                    "LAZADA仓储成本配置参数不能为空"
                )
                rules.add(doSaveLazadaStorageConfig(req.lazadaStorageRuleList!!))
            }

            PlatformEnum.AE -> {
                Assert.isTrue(
                    req.aeStorageRuleList.isNotNull() && req.aeStorageRuleList!!.isNotEmpty(),
                    "AE仓储成本配置参数不能为空"
                )
                rules.add(doSaveAEStorageConfig(req.aeStorageRuleList!!))
            }

            PlatformEnum.TEMU -> {
                Assert.isTrue(
                    req.temuStorageRuleList.isNotNull() && req.temuStorageRuleList!!.isNotEmpty(),
                    "TEMU仓储成本配置参数不能为空"
                )
                rules.add(doSaveTemuStorageConfig(req.temuStorageRuleList!!))
            }

            else -> throw BusinessException("不支持平台ID配置${req.platform}")
        }

        //先删除旧数据
        regionPriceRuleStorageRepository.ktUpdate()
            .eq(RegionPriceRuleStorage::platformId, req.platform!!.platformId)
            .eq(RegionPriceRuleStorage::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        if (rules.isNotEmpty()) {
            regionPriceRuleStorageRepository.saveBatch(rules)
        }
    }

    private fun doSaveAEStorageConfig(req: List<AEStorageRuleReq>): RegionPriceRuleStorage {
        val shippingPlaceCountMap =
            req.groupingBy { "${it.shippingPlaceId}-${it.shippingPlace}-${it.shippingPlaceCn}" }.eachCount()
        shippingPlaceCountMap.forEach { (key, count) ->
            val shippingPlaces = key.split("-")
            if (count > 1) {
                throw BusinessException("发货地国家重复${shippingPlaces[3]}")
            }
        }
        val config = RegionPriceRuleStorageConfigDto()
        val aeStorageConfigDtoList: MutableList<AEStorageConfigDto> = mutableListOf()
        aeStorageConfigDtoList.addAll(req.map {
            AEStorageConfigDto().apply {
                this.shippingPlaceId = it.shippingPlaceId
                this.shippingPlace = it.shippingPlace
                this.shippingPlaceCn = it.shippingPlaceCn
                this.cost = it.cost
            }
        }.toList())
        config.aeStorageConfigDtoList = aeStorageConfigDtoList
        return RegionPriceRuleStorage().apply {
            this.storageRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.AE.platformId
            this.ruleConfig = config.toJson()
        }
    }

    private fun doSaveLazadaStorageConfig(req: List<LazadaStorageRuleReq>): RegionPriceRuleStorage {
        val config = RegionPriceRuleStorageConfigDto()
        config.lazadaStorageConfigDtoList = req.map {
            Assert.isTrue(it.country.isNotBlank() && it.cost.isNotNull(), "请选择国家站点")
            LazadaStorageConfigDto().apply {
                this.country = it.country
                this.cost = it.cost
            }
        }.toList()
        return RegionPriceRuleStorage().apply {
            this.storageRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.LAZADA.platformId
            this.ruleConfig = config.toJson()
        }
    }

    private fun doSaveTemuStorageConfig(req: List<TemuStorageRuleReq>): RegionPriceRuleStorage {
        val config = RegionPriceRuleStorageConfigDto()
        config.temuStorageConfigDtoList = req.map {
            Assert.isTrue(it.country.isNotBlank() && it.cost.isNotNull(), "请选择国家站点")
            TemuStorageConfigDto().apply {
                this.country = it.country
                this.cost = it.cost
            }
        }.toList()
        return RegionPriceRuleStorage().apply {
            this.storageRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.TEMU.platformId
            this.ruleConfig = config.toJson()
        }
    }

    //查询仓储成本配置
    override fun queryStorageRuleByPlatform(platform: PlatformEnum): RegionPriceRuleStorageResp? {
        val regionPriceRuleStorage = regionPriceRuleStorageRepository.ktQuery()
            .eq(RegionPriceRuleStorage::platformId, platform.platformId)
            .one()

        return regionPriceRuleStorage?.let {
            val resp = RegionPriceRuleStorageResp()
            resp.storageRuleId = it.storageRuleId
            resp.platformId = it.platformId
            val configDto = it.ruleConfig?.parseJson<RegionPriceRuleStorageConfigDto>()
            resp.aeStorageConfigList = configDto?.aeStorageConfigDtoList?.map {
                AEStorageConfigResp(it.shippingPlaceId, it.shippingPlace, it.shippingPlaceCn, it.cost)
            }
            resp.lazadaStorageConfigList = configDto?.lazadaStorageConfigDtoList?.map {
                LazadaStorageConfigResp(it.country, it.cost)
            }
            resp.temuStorageConfigList = configDto?.temuStorageConfigDtoList?.map {
                TemuStorageConfigResp(it.country, it.cost)
            }
            return resp
        }
    }

    //保存退货率配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveRejectRateRule(req: List<SaveRegionPriceRuleRejectRateReq>) {
        val shopIdToCountMap = req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty() }.flatMap { it.shopIds!! }.groupingBy { it }.eachCount()
        shopIdToCountMap.forEach { (shopId, count) ->
            Assert.isTrue(count < 2, "店铺${shopId}重复设置")
        }
        val platformToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { it.platformId }.eachCount()
        platformToCountMap.forEach { (platformId, count) ->
            if (count > 1) {
                throw BusinessException("平台:${PlatformEnum.getByPlatformId(platformId!!)?.platformName}重复设置")
            }
        }

        val platformToMap = req.groupBy { it.platformId }
        platformToMap.forEach { (platformId, list1) ->
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            if (defaultCount > 1) {
                throw BusinessException("平台${PlatformEnum.getByPlatformId(platformId!!)?.platformName}设置了多个默认退货率配置")
            }
        }
        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleRejectRateRepository.ktUpdate()
            .`in`(RegionPriceRuleRejectRate::platformId, platformIds)
            .eq(RegionPriceRuleRejectRate::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleRejectRate().apply {
                this.rejectRateRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleRejectRateRepository.saveBatch(rules)
        }
    }

    //查询退货率配置
    override fun queryRejectRateRule(): List<RegionPriceRuleRejectRateResp> {
        val list = regionPriceRuleRejectRateRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleRejectRateResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存广告成本配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveAdRule(req: List<SaveRegionPriceRuleAdReq>) {
        val shopIdToCountMap = req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty() }.flatMap { it.shopIds!! }.groupingBy { it }.eachCount()
        shopIdToCountMap.forEach { (shopId, count) ->
            Assert.isTrue(count < 2, "店铺${shopId}重复设置")
        }
        val platformToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { it.platformId }.eachCount()
        platformToCountMap.forEach { (platformId, count) ->
            if (count > 1) {
                throw BusinessException("平台:${PlatformEnum.getByPlatformId(platformId!!)?.platformName}重复设置")
            }
        }

        val platformToMap = req.groupBy { it.platformId }
        platformToMap.forEach { (platformId, list1) ->
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            if (defaultCount > 1) {
                throw BusinessException("平台${PlatformEnum.getByPlatformId(platformId!!)?.platformName}设置了多个默认广告成本配置")
            }
        }
        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleAdRepository.ktUpdate()
            .`in`(RegionPriceRuleAd::platformId, platformIds)
            .eq(RegionPriceRuleAd::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleAd().apply {
                this.adRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleAdRepository.saveBatch(rules)
        }
    }

    //查询广告成本配置
    override fun queryAdRule(): List<RegionPriceRuleAdResp> {
        val list = regionPriceRuleAdRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleAdResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存交易佣金配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveCommissionRule(req: List<SaveRegionPriceRuleCommissionReq>) {
        val shopIdToCountMap = req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty() }.flatMap { it.shopIds!! }.groupingBy { it }.eachCount()
        shopIdToCountMap.forEach { (shopId, count) ->
            Assert.isTrue(count < 2, "店铺${shopId}重复设置")
        }
        val platformIdAndBusinessTypeToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { "${it.platformId}-${it.businessType}" }.eachCount()
        platformIdAndBusinessTypeToCountMap.forEach { (platformIdAndBusinessType, count) ->
            val keys = platformIdAndBusinessType.split("-")
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (count > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "运营模式:${shopBusinessType.description}重复设置"
                )
            }
        }
        val platformIdAndBusinessTypeToMap = req.groupBy { "${it.platformId}-${it.businessType}" }
        platformIdAndBusinessTypeToMap.forEach { (platformIdAndBusinessType, list1) ->
            val keys = platformIdAndBusinessType.split("-")
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "运营模式:${shopBusinessType.description}设置了多个默认交易佣金配置"
                )
            }
        }

        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleCommissionRepository.ktUpdate()
            .`in`(RegionPriceRuleCommission::platformId, platformIds)
            .eq(RegionPriceRuleCommission::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleCommission().apply {
                this.commissionRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.businessType = it.businessType
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleCommissionRepository.saveBatch(rules)
        }
    }

    //查询交易佣金配置
    override fun queryCommissionRule(): List<RegionPriceRuleCommissionResp> {
        val list = regionPriceRuleCommissionRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleCommissionResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存提现手续费配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveWithdrawRule(req: List<SaveRegionPriceRuleWithdrawReq>) {
        val shopIdToCountMap = req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty() }.flatMap { it.shopIds!! }.groupingBy { it }.eachCount()
        shopIdToCountMap.forEach { (shopId, count) ->
            Assert.isTrue(count < 2, "店铺${shopId}重复设置")
        }
        val platformAndBusinessTypeToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { "${it.platformId}-${it.businessType}" }.eachCount()
        platformAndBusinessTypeToCountMap.forEach { (platformIdAndBusinessType, count) ->
            val keys = platformIdAndBusinessType.split("-")
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (count > 1) {
                throw BusinessException("平台:${platformEnum?.platformName}," + "运营模式:${shopBusinessType.description}重复设置")
            }
        }

        val platformAndBusinessTypeToMap = req.groupBy { "${it.platformId}-${it.businessType}" }
        platformAndBusinessTypeToMap.forEach { (platformIdAndBusinessType, list1) ->
            val keys = platformIdAndBusinessType.split("-")
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "运营模式:${shopBusinessType.description}设置了多个默认提现手续费配置"
                )
            }
        }

        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleWithdrawRepository.ktUpdate()
            .`in`(RegionPriceRuleWithdraw::platformId, platformIds)
            .eq(RegionPriceRuleWithdraw::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleWithdraw().apply {
                this.withdrawRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.businessType = it.businessType
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleWithdrawRepository.saveBatch(rules)
        }
    }

    //查询提现手续费配置
    override fun queryWithdrawRule(): List<RegionPriceRuleWithdrawResp> {
        val list = regionPriceRuleWithdrawRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleWithdrawResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存目标毛利率配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveGrossMarginRule(req: List<SaveRegionPriceRuleGrossMarginReq>) {
        val shopIdAndSupplyTypeToCountMap: MutableMap<String,Int> = mutableMapOf()
        req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty()}.forEach {grossMarginReq ->
            grossMarginReq.shopIds!!.forEach { shopId ->
                if(grossMarginReq.supplyType.isNotNull() && grossMarginReq.supplyType.isNotBlank()){
                    var key = "${shopId}-${grossMarginReq.supplyType}"
                    if(shopIdAndSupplyTypeToCountMap.containsKey(key)){
                        shopIdAndSupplyTypeToCountMap.put(key,shopIdAndSupplyTypeToCountMap.getOrDefault(key,0)+1)
                    }else{
                        shopIdAndSupplyTypeToCountMap.put(key,1)
                    }
                }
            }
        }
        shopIdAndSupplyTypeToCountMap.forEach { (shopIdAndSupplyType, count) ->
            val keys = shopIdAndSupplyType.split("-")
            Assert.isTrue(count < 2, "店铺${keys[0]},供给方式${keys[1]}重复设置")
        }
        val platformAndSupplyTypeToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { "${it.platformId}-${it.supplyType}" }.eachCount()
        platformAndSupplyTypeToCountMap.forEach { (platformAndSupplyType, count) ->
            val keys = platformAndSupplyType.split("-")
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val supplyType = SupplyModeEnum.getByCode(keys[1])
            if (count > 1) {
                throw BusinessException("平台:${platformEnum?.platformName}," +"供给方式:${supplyType?.desc}重复设置")
            }
        }

        val platformAndSupplyTypeToMap = req.groupBy { "${it.platformId}-${it.supplyType}" }
        platformAndSupplyTypeToMap.forEach { (platformAndSupplyType, list1) ->
            val keys = platformAndSupplyType.split("-")
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val supplyType = SupplyModeEnum.getByCode(keys[1])
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "供给方式:${supplyType?.desc}设置了多个默认目标毛利率配置"
                )
            }
        }
        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleGrossMarginRepository.ktUpdate()
            .`in`(RegionPriceRuleGrossMargin::platformId, platformIds)
            .eq(RegionPriceRuleGrossMargin::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleGrossMargin().apply {
                this.grossMarginRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.supplyType = it.supplyType
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleGrossMarginRepository.saveBatch(rules)
        }
    }

    //查询目标毛利率配置
    override fun queryGrossMarginRule(): List<RegionPriceRuleGrossMarginResp> {
        val list = regionPriceRuleGrossMarginRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleGrossMarginResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存营销费用配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveMarketingRule(req: List<SaveRegionPriceRuleMarketingReq>) {
        val shopIdToCountMap = req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty() }.flatMap { it.shopIds!! }.groupingBy { it }.eachCount()
        shopIdToCountMap.forEach { (shopId, count) ->
            Assert.isTrue(count < 2, "店铺${shopId}重复设置")
        }
        val platformAndBusinessTypeToCountMap = req.filter { it.shopIds.isNullOrEmpty() }.groupingBy { "${it.platformId}-${it.businessType}" }.eachCount()
        platformAndBusinessTypeToCountMap.forEach { (platformAndBusinessType, count) ->
            val keys = platformAndBusinessType.split("-")
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (count > 1) {
                throw BusinessException("平台:${platformEnum?.platformName}," + "运营模式:${shopBusinessType.description}重复设置")
            }
        }

        val platformAndBusinessTypeToMap = req.groupBy { "${it.platformId}-${it.businessType}" }
        platformAndBusinessTypeToMap.forEach { (platformAndBusinessType, list1) ->
            val keys = platformAndBusinessType.split("-")
            val defaultCount = list1.count { it.defaultRule.isNotNull() && it.defaultRule == Bool.YES.code }
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "运营模式:${shopBusinessType.description}设置了多个默认营销费用配置"
                )
            }
        }
        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleMarketingRepository.ktUpdate()
            .`in`(RegionPriceRuleMarketing::platformId, platformIds)
            .eq(RegionPriceRuleMarketing::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleMarketing().apply {
                this.marketingRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.businessType = it.businessType
                this.shopIds = it.shopIds?.toJson()
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleMarketingRepository.saveBatch(rules)
        }
    }

    //查询营销费用配置
    override fun queryMarketingRule(): List<RegionPriceRuleMarketingResp> {
        val list = regionPriceRuleMarketingRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleMarketingResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存物流支出配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveFreightRateRule(req: SaveRegionPriceRuleFreightRateReq) {
        val rules = mutableListOf<RegionPriceRuleFreightRate>()
        when (req.platform) {
            PlatformEnum.LAZADA -> {
                Assert.isTrue(
                    req.lazadaFreightRateRuleReqList.isNotEmpty(),
                    "LAZADA物流支出配置参数不能为空"
                )
                rules.add(doSaveLazadaFreightRate(req.lazadaFreightRateRuleReqList!!))
            }

            PlatformEnum.AE -> {
                Assert.isTrue(
                    req.aeFreightRateRuleReqList.isNotEmpty(),
                    "AE物流支出配置参数不能为空"
                )
                rules.add(doSaveAEFreightRate(req.aeFreightRateRuleReqList!!))
            }

            PlatformEnum.TEMU -> {
                Assert.isTrue(
                    req.temuFreightRateRuleReqList.isNotEmpty(),
                    "TEMU物流支出配置参数不能为空"
                )
                rules.add(doSaveTemuFreightRate(req.temuFreightRateRuleReqList!!))
            }

            else -> throw BusinessException("不支持平台ID配置${req.platform}")
        }
        //先删除旧数据
        regionPriceRuleFreightRateRepository.ktUpdate()
            .`in`(RegionPriceRuleFreightRate::platformId, req.platform!!.platformId)
            .eq(RegionPriceRuleFreightRate::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        if (rules.isNotEmpty()) {
            regionPriceRuleFreightRateRepository.saveBatch(rules)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun importAEFreightRateRule(file: MultipartFile) {
        // AE物流支出比例配置导入
        val listener = ImportAERegionPriceRuleFreightRateExcelListener()
        EasyExcel.read(file.inputStream, listener).doReadAll()
        val importList = listener.getFreightRateList()
            .asSequence()
            .filter { it.shippingPlaceCn.isNotBlank() }
            .filter { it.shopName.isNotBlank() }
            .filter { it.receivingPlaceCn.isNotBlank() }
            .filter { it.rate.isNotBlank() }
            .toList()
        if (CollectionUtils.isEmpty(importList)) {
            throw BaseBizException("导入AE物流支出比例配置数据为空")
        }
        //发货地
        val aeShippingFrom = dictClientExternal.listByDictCode(DictEnum.AE_SHIPPING_FROM.dictCode)
        Assert.isTrue(aeShippingFrom != null, "AE发货地字典配置不存在")
        //目的地
        val aeShippingTo = dictClientExternal.listByDictCode(DictEnum.AE_SHIPPING_TO.dictCode)
        Assert.isTrue(aeShippingFrom != null, "AE目的地字典配置不存在")
        val importShopNameList = importList.mapNotNull { it.shopName }
        Assert.isTrue(importShopNameList.isNotEmpty(), "店铺名称不能为空")
        val dbShopList = shopRepository.listByShopName(importShopNameList, PlatformEnum.AE)
        val shopNameToMap = dbShopList.associateBy { it.shopName }
        val notExistShopName = importShopNameList.firstOrNull { !shopNameToMap.containsKey(it) }
        Assert.isTrue(notExistShopName.isNullOrBlank(), "店铺${notExistShopName}不存在")
        val req = SaveRegionPriceRuleFreightRateReq().apply {
            this.platform = PlatformEnum.AE
            var receivingPlaceFreightRateList: MutableList<AEFreightRateRuleReq> = mutableListOf()
            val shopNameToFreightRateListMap = importList.groupBy { it.shopName }
            shopNameToFreightRateListMap.forEach { (shopName, list) ->
                receivingPlaceFreightRateList.add(AEFreightRateRuleReq().apply {
                    this.shopId = shopNameToMap[shopName]?.shopId
                    var receivingPlaceFreightRateList: MutableList<ReceivingPlaceFreightRateReq> = mutableListOf()

                    val shippingPlaceToReceivePlaceListMap = list.groupBy { it.shippingPlaceCn }
                    shippingPlaceToReceivePlaceListMap.forEach { (shippingPlaceCn, receivePlaceList) ->
                        receivingPlaceFreightRateList.add(ReceivingPlaceFreightRateReq().apply {
                            val shippingFrom = getAEShippingFromOrToByName(aeShippingFrom!!, shippingPlaceCn!!)
                            this.shippingPlaceId = shippingFrom?.remark?.toLong()
                            this.shippingPlace = shippingFrom?.dictCode
                            this.shippingPlaceCn = shippingPlaceCn
                            this.freightRateList = receivePlaceList.map {
                                FreightRateReq().apply {
                                    val shippingTo = getAEShippingFromOrToByName(aeShippingTo!!, it.receivingPlaceCn!!)
                                    this.receivingPlace = shippingTo?.dictCode
                                    this.receivingPlaceCn = it.receivingPlaceCn
                                    this.rate = BigDecimal(it.rate)
                                    this.defaultRule = if (it.defaultRule!=null && it.defaultRule.equals("是")) Bool.YES.code else Bool.NO.code
                                }
                            }
                        })
                        this.receivingPlaceFreightRateList = receivingPlaceFreightRateList
                    }
                })
            }
            this.aeFreightRateRuleReqList = receivingPlaceFreightRateList
        }

        saveFreightRateRule(req)
    }

    private fun doSaveLazadaFreightRate(req: List<LazadaFreightRateRuleReq>): RegionPriceRuleFreightRate {
        val lazadaFreightRateRuleDtoList = req.map {
            LazadaFreightRateRuleDto().apply {
                this.shopId = it.shopId
                this.feightRateDtoList = it.feightRateReqList?.map {
                    LazadaFeightRateDto().apply {
                        this.country = it.country
                        this.rate = it.rate
                    }
                }?.toList()
            }
        }.toList()
        val ruleConfigDto = RegionPriceRuleFreightRateConfigDto()
        ruleConfigDto.lazadaFreightRateRuleDtoList = lazadaFreightRateRuleDtoList
        return RegionPriceRuleFreightRate().apply {
            this.freightRateRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.LAZADA.platformId
            this.ruleConfig = ruleConfigDto.toJson()
        }
    }

    private fun doSaveTemuFreightRate(req: List<TemuFreightRateRuleReq>): RegionPriceRuleFreightRate {
        val temuFreightRateRuleDtoList = req.map {
            TemuFreightRateRuleDto().apply {
                this.shopId = it.shopId
                this.feightRateDtoList = it.feightRateReqList?.map {
                    TemuFeightRateDto().apply {
                        this.country = it.country
                        this.rate = it.rate
                    }
                }
            }
        }.toList()
        val ruleConfigDto = RegionPriceRuleFreightRateConfigDto()
        ruleConfigDto.temuFreightRateRuleDtoList = temuFreightRateRuleDtoList
        return RegionPriceRuleFreightRate().apply {
            this.freightRateRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.TEMU.platformId
            this.ruleConfig = ruleConfigDto.toJson()
        }
    }

    private fun doSaveAEFreightRate(req: List<AEFreightRateRuleReq>): RegionPriceRuleFreightRate {
        val shopIds: MutableList<Long> = mutableListOf()
        val platformIdAndShopIdAndShippingPlaceDefaultCountToMap: MutableMap<String, Int> = mutableMapOf()
        req.forEach { ruleReq ->
            ruleReq.receivingPlaceFreightRateList.forEach { receivingPlaceFreightRate ->
                Assert.isTrue(ruleReq.shopId!=null,"店铺ID不能为空")
                shopIds.add(ruleReq.shopId!!)
                val key = "${ruleReq.shopId}-${receivingPlaceFreightRate.shippingPlace}"
                val count =
                    receivingPlaceFreightRate.freightRateList.count { it.defaultRule != null && it.defaultRule == Bool.YES.code }
                if (platformIdAndShopIdAndShippingPlaceDefaultCountToMap.contains(key)) {
                    platformIdAndShopIdAndShippingPlaceDefaultCountToMap[key] =
                        count + platformIdAndShopIdAndShippingPlaceDefaultCountToMap.getOrDefault(key, 0)
                } else {
                    platformIdAndShopIdAndShippingPlaceDefaultCountToMap[key] = count
                }
            }
        }
        val shopIdToMap = shopRepository.listByIds(shopIds).associateBy { it.shopId }
        platformIdAndShopIdAndShippingPlaceDefaultCountToMap.forEach { (platformIdAndShopIdAndShippingPlace, defaultCount) ->
            val keys = platformIdAndShopIdAndShippingPlace.split("-")
            val shopName = keys[0].let {
                shopIdToMap[it.toLong()]?.shopName ?: it
            }
            if (defaultCount == 0) {
                throw BusinessException(
                    "平台:${PlatformEnum.AE.platformName}," +
                            "店铺: ${shopName}," +
                            "发货地:${keys[1]}" +
                            "没有设置默认物流支出配置"
                )
            }
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${PlatformEnum.AE.platformName}," +
                            "店铺: ${shopName}," +
                            "发货地:${keys[1]}" +
                            "设置了多个默认物流支出配置"
                )
            }
        }
        val aeFreightRateRuleDtoList: MutableList<AEFreightRateRuleDto> = mutableListOf()
        req.forEach { ruleReq ->
            aeFreightRateRuleDtoList.add(AEFreightRateRuleDto().apply {
                this.shopId = ruleReq.shopId
                this.receivingPlaceFreightRateList = ruleReq.receivingPlaceFreightRateList.map {
                    ReceivingPlaceFreightRateDto().apply {
                        this.shippingPlaceId = it.shippingPlaceId
                        this.shippingPlace = it.shippingPlace
                        this.shippingPlaceCn = it.shippingPlaceCn
                        this.freightRateList = it.freightRateList.map { freightRate ->
                            FreightRateDto().apply {
                                this.receivingPlace = freightRate.receivingPlace
                                this.receivingPlaceCn = freightRate.receivingPlaceCn
                                this.rate = freightRate.rate
                                this.defaultRule = freightRate.defaultRule ?: Bool.NO.code
                            }
                        }
                    }
                }.toList()
            })
        }
        val ruleConfigDto = RegionPriceRuleFreightRateConfigDto()
        ruleConfigDto.aeFreightRateRuleDtoList = aeFreightRateRuleDtoList
        return RegionPriceRuleFreightRate().apply {
            this.freightRateRuleId = IdHelper.getId()
            this.platformId = PlatformEnum.AE.platformId
            this.ruleConfig = ruleConfigDto.toJson()
        }
    }


    //查询物流支出配置
    override fun getFreightRateRuleByPlatform(platform: PlatformEnum): RegionPriceRuleFreightRateResp? {
        val freightRate = regionPriceRuleFreightRateRepository.ktQuery()
            .eq(RegionPriceRuleFreightRate::platformId, platform.platformId)
            .one()
        return freightRate?.let {
            RegionPriceRuleFreightRateResp().apply {
                this.freightRateRuleId = it.freightRateRuleId
                this.platformId = it.platformId
                val configDto = it.ruleConfig?.parseJson<RegionPriceRuleFreightRateConfigDto>()
                this.aeFreightRateRespList = configDto?.aeFreightRateRuleDtoList?.map {
                    AEFreightRateResp().apply {
                        this.shopId = it.shopId
                        this.receivingPlaceFreightRateList = it.receivingPlaceFreightRateList.map {
                            ReceivingPlaceFreightRateResp().apply {
                                this.shippingPlaceId = it.shippingPlaceId
                                this.shippingPlace = it.shippingPlace
                                this.shippingPlaceCn = it.shippingPlaceCn
                                this.freightRateList = it.freightRateList.map {
                                    FreightRateResp().apply {
                                        this.receivingPlace = it.receivingPlace
                                        this.receivingPlaceCn = it.receivingPlaceCn
                                        this.rate = it.rate
                                        this.defaultRule = it.defaultRule ?: Bool.NO.code
                                    }
                                }.toList()
                            }
                        }
                    }
                }
                this.lazadaFreightRateRespList = configDto?.lazadaFreightRateRuleDtoList?.map {
                    LazadaFreightRateRuleResp().apply {
                        this.shopId = it.shopId
                        this.feightRateRespList = it.feightRateDtoList?.map {
                            LazadaFeightRateResp().apply {
                                this.country = it.country
                                this.rate = it.rate
                            }
                        }
                    }
                }
                this.temuFreightRateRespList = configDto?.temuFreightRateRuleDtoList?.map {
                    TemuFreightRateRuleResp().apply {
                        this.shopId = it.shopId
                        this.feightRateRespList = it.feightRateDtoList?.map {
                            TemuFeightRateResp().apply {
                                this.country = it.country
                                this.rate = it.rate
                            }
                        }
                    }
                }
            }
        }
    }

    //保存折扣率配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveDiscountRateRule(req: List<SaveRegionPriceRuleDiscountRateReq>) {
        val shopIdToCountMap: MutableMap<String,Int> = mutableMapOf()
        req.filter { it.shopIds.isNotNull() && it.shopIds.isNotEmpty()}.forEach {discountRateReq ->
            discountRateReq.shopIds!!.forEach { shopId ->
                if(discountRateReq.supplyType.isNotNull() && discountRateReq.supplyType.isNotBlank()){
                    var key = "${shopId}-${discountRateReq.supplyType}"
                    if(shopIdToCountMap.containsKey(key)){
                        shopIdToCountMap.put(key,shopIdToCountMap.getOrDefault(key,0)+1)
                    }else{
                        shopIdToCountMap.put(key,1)
                    }
                }
            }
        }
        shopIdToCountMap.forEach { (shopIdAndSupplyType, count) ->
            val keys = shopIdAndSupplyType.split("-")
            Assert.isTrue(count < 2, "店铺${keys[0]},供给方式${keys[1]}重复设置")
        }
        val platformAndBusinessTypeToCountMap = req.filter { it.shopIds.isNullOrEmpty() && it.supplyType.isNullOrBlank() }.groupingBy { "${it.platformId}-${it.businessType}" }.eachCount()
        platformAndBusinessTypeToCountMap.forEach { (platformAndBusinessType, count) ->
            val keys = platformAndBusinessType.split("-")
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (count > 1) {
                throw BusinessException("平台:${platformEnum?.platformName}," + "运营模式:${shopBusinessType}重复设置")
            }
        }

        val platformAndBusinessTypeToMap = req.groupBy { "${it.platformId}-${it.businessType}" }
        platformAndBusinessTypeToMap.forEach { (platformAndBusinessType, list1) ->
            val keys = platformAndBusinessType.split("-")
            val defaultCount = list1.count { it.defaultRule != null && it.defaultRule == Bool.YES.code }
            val platformEnum = PlatformEnum.getByPlatformId(keys[0].toLong())
            val shopBusinessType = ProductShopBusinessType.fromValue(keys[1].toInt())
            if (defaultCount > 1) {
                throw BusinessException(
                    "平台:${platformEnum?.platformName}," +
                            "运营模式:${shopBusinessType}设置了多个默认折扣率配置"
                )
            }
        }
        val platformIds = req.map { it.platformId!! }
        //先删除旧数据
        regionPriceRuleDiscountRateRepository.ktUpdate()
            .`in`(RegionPriceRuleDiscountRate::platformId, platformIds)
            .eq(RegionPriceRuleDiscountRate::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val rules = req.map {
            val rule = RegionPriceRuleDiscountRate().apply {
                this.discountRateRuleId = IdHelper.getId()
                this.channelId = it.channelId
                this.platformId = it.platformId!!
                this.businessType = it.businessType
                this.shopIds = it.shopIds?.toJson()
                this.supplyType = it.supplyType
                this.rate = it.rate
                this.defaultRule = it.defaultRule ?: Bool.NO.code
            }
            rule
        }
        if (rules.isNotEmpty()) {
            regionPriceRuleDiscountRateRepository.saveBatch(rules)
        }
    }

    //查询折扣率配置
    override fun queryDiscountRateRule(): List<RegionPriceRuleDiscountRateResp> {
        val list = regionPriceRuleDiscountRateRepository.ktQuery()
            .list()

        return list.map {
            val costResp = RegionPriceRuleDiscountRateResp()
            BeanUtils.copyProperties(it, costResp)
            costResp.shopIds = it.shopIds?.parseArray<Long>()
            costResp
        }.toList()
    }

    //保存综合税率配置
    @Transactional(rollbackFor = [Exception::class])
    override fun saveTaxRateRule(req: SaveRegionPriceRuleTaxRateReq) {
        when (req.ruleType) {
            RegionPriceRuleTaxRateTypeEnum.ENTITY.code -> {
                req.ruleConfig?.forEach { rateConfig ->
                    Assert.isTrue(rateConfig.entityCode.isNotBlank(), "主体不能为空")
                }
            }

            RegionPriceRuleTaxRateTypeEnum.SHOP.code -> {
                req.ruleConfig?.forEach { rateConfig ->
                    Assert.isTrue(rateConfig.platformId != null && rateConfig.shopId != null, "平台和店铺不能为空")
                }
            }

            RegionPriceRuleTaxRateTypeEnum.SUPPLY_TYPE.code -> {
                req.ruleConfig?.forEach { rateConfig ->
                    Assert.isTrue(rateConfig.supplyTypeCode.isNotBlank(), "供给方式不能为空")
                }
            }

            RegionPriceRuleTaxRateTypeEnum.ENTITY_SUPPLY_TYPE.code -> {
                req.ruleConfig?.forEach { rateConfig ->
                    Assert.isTrue(
                        rateConfig.entityCode.isNotBlank() && rateConfig.supplyTypeCode.isNotBlank(),
                        "主体和供给方式不能为空"
                    )
                }
            }

            RegionPriceRuleTaxRateTypeEnum.SHOP_SUPPLY_TYPE.code -> {
                req.ruleConfig?.forEach { rateConfig ->
                    Assert.isTrue(
                        rateConfig.platformId != null && rateConfig.shopId != null && rateConfig.supplyTypeCode.isNotBlank(),
                        "平台，店铺和供给方式不能为空"
                    )
                }
            }
        }
        //先删除旧数据
        regionPriceRuleTaxRateRepository.ktUpdate()
            .eq(RegionPriceRuleTaxRate::deleted, Bool.NO.code)
            .remove()
        //再保存新数据
        val regionPriceRuleTaxRate = req.let {
            val rule = RegionPriceRuleTaxRate().apply {
                this.taxRateRuleId = IdHelper.getId()
                this.ruleType = it.ruleType
                val list = it.ruleConfig?.map { config ->
                    val taxRateRuleDto = TaxRateRuleDto(
                        it.ruleType,
                        config.entityCode, config.entityName,
                        config.platformId, PlatformEnum.getByPlatformId(config.platformId?:-1)?.platformName,
                        config.shopId, config.shopName,
                        config.supplyTypeCode, config.supplyTypeName,
                        config.rate
                    )
                    taxRateRuleDto
                }?.toList()
                this.ruleConfig = list?.toJson()
            }
            rule
        }
        regionPriceRuleTaxRateRepository.save(regionPriceRuleTaxRate)
    }

    //查询综合税率配置
    override fun queryTaxRateRule(): RegionPriceRuleTaxRateResp? {
        val regionPriceRuleTaxRate = regionPriceRuleTaxRateRepository.ktQuery().one()
        return regionPriceRuleTaxRate?.let {
            val result = RegionPriceRuleTaxRateResp()
            result.taxRateRuleId = it.taxRateRuleId
            result.ruleType = it.ruleType
            result.ruleConfig = it.ruleConfig?.parseJsonList(TaxRateRuleResp::class.java)
            result
        }
    }
}
