package tech.tiangong.pop.service

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.settings.SkuCodeRulePageReq
import tech.tiangong.pop.req.settings.SkuCodeRuleSaveReq
import tech.tiangong.pop.resp.settings.SkuCodeRulePageResp
import tech.tiangong.pop.resp.settings.SkuRuleElementResp

interface SkuCodeRuleService {
    /**
     * 分页查询SKU编码规则
     *
     * @param req 包含分页和查询条件的请求对象
     * @return 包含分页结果的对象，内含SKU编码规则信息列表
     */
    fun page(req: SkuCodeRulePageReq): PageVo<SkuCodeRulePageResp>

    /**
     * 获取指定SKU编码规则的元素列表
     *
     * @param skuCodeRuleId SKU编码规则的主键ID
     * @return SKU规则元素信息列表
     */
    fun getElements(skuCodeRuleId: Long): List<SkuRuleElementResp>

    /**
     * 保存或更新SKU编码规则
     *
     * @param req 包含需要保存或更新的SKU编码规则数据
     * @return 操作是否成功，成功返回true，失败返回false
     */
    fun saveOrUpdate(req: SkuCodeRuleSaveReq): Boolean

    /**
     * 删除指定的SKU编码规则
     *
     * @param skuCodeRuleId SKU编码规则的主键ID
     * @return 操作成功与否，成功返回true，失败返回false
     */
    fun delete(skuCodeRuleId: Long): Boolean
}
