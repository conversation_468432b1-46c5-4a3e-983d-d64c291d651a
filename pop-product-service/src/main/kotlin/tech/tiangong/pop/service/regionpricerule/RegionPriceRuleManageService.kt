package tech.tiangong.pop.service.regionpricerule

import org.springframework.web.multipart.MultipartFile
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.req.regionpricerule.*
import tech.tiangong.pop.resp.regionpricerule.*

interface RegionPriceRuleManageService {

    /**
     * 保存区域价格-物流费用配置
     */
    fun saveLogisticsRule(req: SaveRegionPriceRuleLogisticsReq)

    /**
     * 导入AE区域价格-物流费用配置
     */
    fun importAELogisticsRule(file: MultipartFile)

    /**
     * 查询区域价格-物流费用配置
     */
    fun getLogisticsRuleByPlatform(platform: PlatformEnum): RegionPriceRuleLogisticsResp?

    /**
     * 保存区域价格-仓储成本配置
     */
    fun saveStorageRule(req: SaveRegionPriceRuleStorageReq)

    /**
     * 查询区域价格-仓储成本配置
     */
    fun queryStorageRuleByPlatform(platform: PlatformEnum): RegionPriceRuleStorageResp?

    /**
     * 保存区域价格-退货率配置
     */
    fun saveRejectRateRule(req: List<SaveRegionPriceRuleRejectRateReq>)

    /**
     * 查询区域价格-退货率配置
     */
    fun queryRejectRateRule(): List<RegionPriceRuleRejectRateResp>

    /**
     * 保存区域价格-广告成本配置
     */
    fun saveAdRule(req: List<SaveRegionPriceRuleAdReq>)

    /**
     * 查询区域价格-广告成本配置
     */
    fun queryAdRule(): List<RegionPriceRuleAdResp>

    /**
     * 保存区域价格-佣金配置
     */
    fun saveCommissionRule(req: List<SaveRegionPriceRuleCommissionReq>)

    /**
     * 查询区域价格-佣金配置
     */
    fun queryCommissionRule(): List<RegionPriceRuleCommissionResp>

    /**
     * 保存区域价格-提现手续费配置
     */
    fun saveWithdrawRule(req: List<SaveRegionPriceRuleWithdrawReq>)

    /**
     * 查询区域价格-提现手续费配置
     */
    fun queryWithdrawRule(): List<RegionPriceRuleWithdrawResp>

    /**
     * 保存区域价格-目标毛利率配置
     */
    fun saveGrossMarginRule(req: List<SaveRegionPriceRuleGrossMarginReq>)

    /**
     * 查询区域价格-目标毛利率配置
     */
    fun queryGrossMarginRule(): List<RegionPriceRuleGrossMarginResp>

    /**
     * 保存区域价格-营销费用配置
     */
    fun saveMarketingRule(req: List<SaveRegionPriceRuleMarketingReq>)

    /**
     * 查询区域价格-营销费用配置
     */
    fun queryMarketingRule(): List<RegionPriceRuleMarketingResp>


    /**
     * 保存区域价格-物流支出配置
     */
    fun saveFreightRateRule(req: SaveRegionPriceRuleFreightRateReq)

    /**
     * 导入AE区域价格-物流支出配置
     */
    fun importAEFreightRateRule(file: MultipartFile)

    /**
     * 查询区域价格-物流支出配置
     */
    fun getFreightRateRuleByPlatform(platform: PlatformEnum): RegionPriceRuleFreightRateResp?

    /**
     * 保存区域价格-折扣率配置
     */
    fun saveDiscountRateRule(req: List<SaveRegionPriceRuleDiscountRateReq>)

    /**
     * 查询区域价格-折扣率配置
     */
    fun queryDiscountRateRule(): List<RegionPriceRuleDiscountRateResp>

    /**
     * 保存区域价格-综合税率配置
     */
    fun saveTaxRateRule(req: SaveRegionPriceRuleTaxRateReq)

    /**
     * 查询区域价格-综合税率配置
     */
    fun queryTaxRateRule(): RegionPriceRuleTaxRateResp?
}
