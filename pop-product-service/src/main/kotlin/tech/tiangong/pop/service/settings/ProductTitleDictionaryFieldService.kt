package tech.tiangong.pop.service.settings

import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.settings.ProductTitleDictionaryFieldDeleteReq
import tech.tiangong.pop.req.settings.ProductTitleDictionaryFieldPageQueryReq
import tech.tiangong.pop.req.settings.ProductTitleDictionaryFieldSaveReq
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.settings.ProductTitleDictionaryFieldPageResp
import tech.tiangong.pop.resp.settings.ProductTitleDictionaryFieldResp
import tech.tiangong.pop.resp.settings.ProductTitleDictionaryFieldValueResp

/**
 * 商品标题词库字段服务接口
 */
interface ProductTitleDictionaryFieldService {
    
    /**
     * 分页查询商品标题词库字段
     */
    fun page(req: ProductTitleDictionaryFieldPageQueryReq): PageVo<ProductTitleDictionaryFieldPageResp>
    
    /**
     * 查询商品标题词库字段详情
     */
    fun getFieldDetail(fieldId: Long): ProductTitleDictionaryFieldResp
    
    /**
     * 保存或更新商品标题词库字段
     */
    fun saveOrUpdate(req: ProductTitleDictionaryFieldSaveReq): Boolean
    
    /**
     * 删除商品标题词库字段
     */
    fun delete(req: ProductTitleDictionaryFieldDeleteReq): Boolean
    
    /**
     * 批量导入商品标题词库翻译
     */
    fun importExcel(excelFile: MultipartFile): ImportResultResp

    /**
     * 根据系统字段和字段值查询字段值详情
     */
    fun getFieldValueBySysFieldAndValue(sysField: String, fieldValue: String): ProductTitleDictionaryFieldValueResp?

    /**
     * 根据系统字段和字段ID查询字段值详情
     */
    fun getFieldValueBySysFieldAndId(sysField: String, fieldValueId: String): ProductTitleDictionaryFieldValueResp?

    /**
     * 清除所有缓存
     */
    fun invalidateAllCache()
}