package tech.tiangong.pop.service.settings

import tech.tiangong.pop.bo.ProductPriceBasicCalcBO
import tech.tiangong.pop.bo.ProductPriceBasicCalcResultBO
import java.math.BigDecimal

/**
 * 产品价格基础倍率计算服务
 */
interface ProductPriceBasicCalcService {
    /**
     * 计算产品价格基础倍率
     */
    fun calculateMultipliers(calcBO: ProductPriceBasicCalcBO): ProductPriceBasicCalcResultBO

    /**
     * 计算ISP产品价格基础倍率
     */
    fun calculateIspMultipliers(calcBO: ProductPriceBasicCalcBO): BigDecimal

    /**
     * 计算SP产品价格基础倍率
     */
    fun calculateSpMultipliers(calcBO: ProductPriceBasicCalcBO): BigDecimal

    /**
     * 当数据发生变更时调用此方法清除缓存
     */
    fun invalidateAllCaches()
}
