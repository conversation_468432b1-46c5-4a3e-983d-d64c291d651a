package tech.tiangong.pop.service.impl

import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import com.aliexpress.open.domain.AliexpressPostproductRedefiningOnlineaeproductAeopModifyProductResponse
import com.aliexpress.open.domain.AliexpressPostproductRedefiningOnlineaeproductErrorDetail
import com.aliexpress.open.request.AliexpressPostproductRedefiningEditsimpleproductfiledRequest
import com.aliexpress.open.response.AliexpressPostproductRedefiningEditsimpleproductfiledResponse
import com.aliexpress.open.response.AliexpressPostproductRedefiningOnlineaeproductResponse
import com.global.iop.api.IopClient
import com.global.iop.api.IopClientImpl
import com.global.iop.domain.Protocol
import org.springframework.stereotype.Service
import org.springframework.web.servlet.View
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.aspect.annotation.ExternalLogApiCall
import tech.tiangong.pop.common.enums.ProductAeAuditStatusEnum.APPROVED
import tech.tiangong.pop.common.exception.AliexpressApiException
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository
import tech.tiangong.pop.dao.repository.ProductSyncLogRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum.ACTIVE
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum.UPDATE_PRODUCT
import tech.tiangong.pop.external.ExternalApiExecutor
import tech.tiangong.pop.req.sdk.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.*
import tech.tiangong.pop.resp.sdk.aliexpress.AeUpdateSkuStocksResp.ResultData
import tech.tiangong.pop.utils.checkSuccess
import tech.tiangong.pop.utils.parseAliResponse

/**
 * 海外托管商品
 *
 * <AUTHOR>
 */
@Service
class AliexpressLocalServiceImpl(
    private val aliexpressProperties: AliexpressProperties,
    private val shopRepository: ShopRepository,
    private val productSyncLogRepository: ProductSyncLogRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val error: View,
) : AliexpressServiceImpl(aliexpressProperties, shopRepository, productSyncLogRepository, aeSaleGoodsRepository) {

    private val iopClient: IopClient by lazy {
        IopClientImpl(
            aliexpressProperties.aePlatform.domain,
            aliexpressProperties.aePlatform.appKey,
            aliexpressProperties.aePlatform.appSecret
        )
    }

    /**
     * 上架商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun postProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest
    ): AliexpressProductEditResponse {
        ExternalApiExecutor.executeAliexpressApi("pageProduct") {
            val request = AliexpressLocalServiceProductPostRequest(
                1,
                productInfo.toLocalServiceProductDto(),
                "AE_GLOBAL"
            ).toRequest()
            val response = iopClient.execute(request, accessToken, Protocol.TOP)

            saveSyncLog(productId, request.toJson(), response.gopResponseBody!!, ACTIVE, response.gopErrorSubMsg)
            val productPostResponse =
                response.parseAliResponse<AliexpressLocalServiceProductPostResponse>().checkSuccess()
            return AliexpressProductEditResponse(ProductEditResult(productPostResponse.productId))
        }
    }

    /**
     * 编辑商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    @ExternalLogApiCall(businessIdParamName = "productId")
    override fun editProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest
    ): AliexpressProductEditResponse {
        ExternalApiExecutor.executeAliexpressApi("pageProduct") {
            val request = AliexpressLocalServiceProductEditRequest(
                1,
                productInfo.toLocalServiceProductDto(),
                "AE_GLOBAL"
            ).toRequest()
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            saveSyncLog(
                productId,
                request.toJson(),
                response.gopResponseBody!!,
                UPDATE_PRODUCT,
                response.gopErrorSubMsg
            )
            val productEditResponse =
                response.parseAliResponse<AliexpressLocalServiceProductEditResponse>().checkSuccess()
            return AliexpressProductEditResponse(ProductEditResult(productEditResponse.productId))
        }
    }

    /**
     * 查询商品详情
     *
     * @param platformProductId AE 商品ID
     * @param accessToken 访问令牌
     * @return 商品详情响应
     */
    override fun queryProduct(
        platformProductId: Long,
        accessToken: String
    ): AliexpressProductQueryResponse {
        ExternalApiExecutor.executeAliexpressApi("queryProduct") {
            val request = AliexpressLocalServiceProductQueryRequest(1, platformProductId, "AE_GLOBAL").toRequest()
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            val productQueryResponse =
                response.parseAliResponse<AliexpressLocalServiceProductQueryResponse>().checkSuccess()

            return productQueryResponse.toAliexpressProductQueryResponse()
        }
    }

    /**
     * 分页查询商品列表
     *
     * @param req 分页参数
     * @param accessToken 访问令牌
     * @return 商品列表响应
     */
    override fun pageProduct(
        req: AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery,
        accessToken: String
    ): AliexpressProductPageResponse {
        ExternalApiExecutor.executeAliexpressApi("pageProduct") {
            val request = AliexpressLocalServiceProductsListRequest(
                1,
                "AE_GLOBAL",
                req.pageSize,
                req.toLocalServiceSearchCondition(),
                req.currentPage
            ).toRequest()
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            val productPageResponse =
                response.parseAliResponse<AliexpressLocalServiceProductsListResponse>().checkSuccess()
            return productPageResponse.toAliexpressProductPageResponse()
        }
    }

    /**
     * 商品上架, 最多一次只能上架50个商品
     *
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    override fun onlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>
    ): AliexpressPostproductRedefiningOnlineaeproductResponse {
        ExternalApiExecutor.executeAliexpressApi("onlineAeProduct") {
            val errorDetails = platformProductIds.map {
                val request =
                    AliexpressLocalServiceProductStatusUpdateRequest(
                        "channelSellerId",
                        it,
                        "channel",
                        "ON_SHELF"
                    ).toRequest()
                val response = iopClient.execute(request, accessToken, Protocol.TOP)
                val statusUpdateResponse =
                    response.parseAliResponse<AliexpressLocalServiceProductStatusUpdateResponse>().checkSuccess()
                statusUpdateResponse
            }.map {
                AliexpressPostproductRedefiningOnlineaeproductErrorDetail().apply {
                    this.errorCode = it.code
                    this.errorMessage = it.errorMessage
                    this.productIds = listOf(it.productId)
                }
            }
            val onlineProductResponse =
                AliexpressPostproductRedefiningOnlineaeproductResponse().apply {
                    this.result = AliexpressPostproductRedefiningOnlineaeproductAeopModifyProductResponse().apply {
                        this.errorDetails = errorDetails
                        this.modifyCount = errorDetails.size
                        this.success = errorDetails.isEmpty()
                    }
                }
            return onlineProductResponse
        }

    }

    /**
     * 商品下架
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    override fun offlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>
    ): AeOfflineProductResp {
        ExternalApiExecutor.executeAliexpressApi("onlineAeProduct") {
            // 批量执行，后面再考虑要不要异步
            val errorDetails = platformProductIds.map {
                try {
                    val request =
                        AliexpressLocalServiceProductStatusUpdateRequest(
                            "channelSellerId",
                            it,
                            "channel",
                            "OFF_SHELF"
                        ).toRequest()
                    val response = iopClient.execute(request, accessToken, Protocol.TOP)
                    val statusUpdateResponse =
                        response.parseAliResponse<AliexpressLocalServiceProductStatusUpdateResponse>().checkSuccess()
                    return@map statusUpdateResponse
                } catch (e: Exception) {
                    return@map AliexpressLocalServiceProductStatusUpdateResponse(productId = it.toLong()).apply {
                        this.code = e.message
                        this.errorMessage = e.message
                    }
                }
            }.map {
                AliexpressPostproductRedefiningOnlineaeproductErrorDetail().apply {
                    this.errorCode = it.code
                    this.errorMessage = it.errorMessage
                    this.productIds = listOf(it.productId)
                }
            }
            val offlineProductResponse = AeOfflineProductResp(
                AeOfflineProductResp.ResultData(
                    errorDetails.map { AeOfflineProductResp.ResultData.ErrorDetail(it.errorMessage, it.productIds) },
                    errorDetails.size,
                    errorDetails.isEmpty()
                ),
                requestId = System.currentTimeMillis().toString()
            )
            return offlineProductResponse
        }
    }

    /**
     * 查询商品状态
     *
     * @param accessToken 访问令牌
     * @param productId 商品ID
     * @return AliexpressProductStatusResponse 商品状态响应
     */
    override fun queryProductStatus(
        accessToken: String,
        productId: Long
    ): AliexpressProductStatusResponse {
        return AliexpressProductStatusResponse(ProductStatusResult(0, productId = productId, status = APPROVED.value))
    }

    /**
     * 编辑单商品多sku价格
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuPrices SKU价格映射（必需）{"skuId 1": "价格", "skuId 2": "价格"}
     */
    override fun updateProductSkuPrices(
        accessToken: String,
        productId: Long,
        skuPrices: Map<String, String>
    ): AeUpdateSkuPricesResp {
        ExternalApiExecutor.executeAliexpressApi("updateProductSkuPrices") {
            val request = AliexpressLocalServiceProductPricesEditRequest(
                productId,
                skuPrices.map { SkuPriceModelDto(it.key.toLong(), it.value) },
                productId = productId,
                channel = "AE_GLOBAL"
            ).toRequest()
            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            val aliexpressLocalServiceProductSkuPriceUpdateResponse =
                response.parseAliResponse<AliexpressLocalServiceProductPricesEditResponse>().checkSuccess()
            return AeUpdateSkuPricesResp(
                code = aliexpressLocalServiceProductSkuPriceUpdateResponse.errorCode,
                requestId = aliexpressLocalServiceProductSkuPriceUpdateResponse.requestId,
            )
        }
    }

    /**

     * 编辑单商品多sku库存
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuStocks SKU库存映射（必需）{"skuId 1": "库存", "skuId 2": "库存"}
     */
    override fun updateProductSkuStocks(
        accessToken: String,
        productId: Long,
        skuStocks: Map<String, Long>
    ): AeUpdateSkuStocksResp {
        ExternalApiExecutor.executeAliexpressApi("updateProductSkuStocks") {
            // 查询商品库存
            val stockQueryRequest = AliexpressLocalServiceProductStocksQueryRequest(
                productId = productId,
                channelSellerId = 0,
                channel = "AE_GLOBAL",
            ).toRequest()
            val stockQueryResponse = iopClient.execute(stockQueryRequest, accessToken, Protocol.TOP)
            val stockQueryResult =
                stockQueryResponse.parseAliResponse<AliexpressLocalServiceProductStocksQueryResponse>().checkSuccess()
            stockQueryResult.checkSuccess()
            stockQueryResult.productSkuStockList.associateBy { it.skuId }

            // 映射库存及过滤可更新类型构建更新请求
            val productSkuStockList = stockQueryResult.productSkuStockList
                .mapNotNull { sku ->
                    if (skuStocks.keys.contains(sku.skuId.toString())) {
                        val skuWarehouseStockList = sku.querySkuWarehouseStockList
                            .filter { it.warehouseType == "dropshipping" }
                            .map {
                                SkuWarehouseStockUpdate(
                                    it.warehouseType,
                                    it.warehouseCode,
                                    skuStocks[sku.skuId.toString()]?.toInt() ?: it.sellableQuantity
                                )
                            }
                        if (skuWarehouseStockList.isNotEmpty()) {
                            ProductSkuStockUpdate(skuId = sku.skuId, skuWarehouseStockList = skuWarehouseStockList)
                        } else {
                            null
                        }
                    } else {
                        null
                    }
                }

            val request = AliexpressLocalServiceProductStocksUpdateRequest(
                productId = productId,
                channelSellerId = 0,
                channel = "AE_GLOBAL",
                productSkuStockList = productSkuStockList
            ).toRequest()

            val response = iopClient.execute(request, accessToken, Protocol.TOP)
            val skuPriceUpdateResponse =
                response.parseAliResponse<AliexpressLocalServiceProductStocksUpdateResponse>().checkSuccess()

            // 😮‍💨尽可能的保留错误信息
            val errorDetails = skuStocks.keys.map {
                ResultData.ErrorDetail().apply {
                    this.errorCode = skuPriceUpdateResponse.productSkuStockList.flatMap { it.skuWarehouseStockList }
                        .joinToString { it.errorCode.toString() }
                    this.productIds = listOf(productId.toString())
                }
            }

            return AeUpdateSkuStocksResp(
                code = skuPriceUpdateResponse.errorCode,
                requestId = skuPriceUpdateResponse.requestId,
                result = ResultData(
                    errorDetails = errorDetails,
                    modifyCount = errorDetails.size,
                    success = errorDetails.isEmpty(),
                    errorCode = skuPriceUpdateResponse.errorCode,
                    errorMessage = skuPriceUpdateResponse.errorMessage
                )
            )
        }
    }

    /**
     * 编辑商品的单个字段
     * @see tech.tiangong.pop.enums.ae.AeEditFiedEnum
     *
     * @param req.productId 商品ID（必需） 日志记录关联业务ID
     * @param req.fiedName （必需） AeEditFiedEnum
     * @param req.fiedValue （必需） AeEditFiedEnum不同类型, 不同value
     * @param accessToken 访问令牌（必需）
     * @return
     */
    override fun editSimpleProductFiled(
        req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest,
        accessToken: String
    ): AliexpressPostproductRedefiningEditsimpleproductfiledResponse {
        throw AliexpressApiException("海外托管模式不支持修改属性")
    }
}

/**
 * AliexpressLocalServiceProductsListResponse 转换为 AliexpressProductPageResponse
 */
private fun AliexpressLocalServiceProductsListResponse.toAliexpressProductPageResponse(): AliexpressProductPageResponse {
    return AliexpressProductPageResponse(
        result = this.result.toAliexpressProductPageResultDto()
    )
}

/**
 * Result 转换为 AliexpressProductPageResultDto
 */
private fun Result.toAliexpressProductPageResultDto(): AliexpressProductPageResultDto {
    return AliexpressProductPageResultDto(
        currentPage = this.currentPage,
        errorMsg = if (!this.success) "查询失败" else null,
        productCount = this.totalCount,
        success = this.success,
        totalPage = this.totalPage,
        errorCode = if (!this.success) -1 else null,
        errorMessage1 = null,
        dtoList = this.productList.map { it.toAliexpressProductPageResult() }
    )
}

/**
 * Product 转换为 AliexpressProductPageResult
 * 处理字段映射、类型转换和可空性差异
 */
private fun Product.toAliexpressProductPageResult(): AliexpressProductPageResult {
    return AliexpressProductPageResult(
        productId = this.productId,
        productType = null, // 源数据中没有此字段，设置为null
        auditFailureReason = this.auditFailureReason,
        currencyCode = this.currencyCode,
        gmtCreate = this.createdTime,
        gmtModified = this.modifiedTime,
        groupId = null, // 源数据中没有此字段
        imageURLs = convertImageUrls(this.imageUrls), // 转换图片URL分隔符
        ownerMemberId = null, // 源数据中没有此字段
        ownerMemberSeq = null, // 源数据中没有此字段
        productMaxPrice = this.productMaxPrice,
        productMinPrice = this.productMinPrice,
        src = null, // 源数据中没有此字段
        subject = this.title,
        wsDisplay = null, // 源数据中没有此字段
        wsOfflineDate = null, // 源数据中没有此字段
        freightTemplateId = this.shippingTemplate,
        couponEndDate = null, // 源数据中没有此字段
        couponStartDate = null // 源数据中没有此字段
    )
}

/**
 * 转换图片URL分隔符：从逗号分隔转换为分号分隔
 * 处理空字符串和null值的情况
 */
private fun convertImageUrls(imageUrls: String?): String? {
    return when {
        imageUrls.isNullOrBlank() -> null
        imageUrls.contains(",") -> imageUrls.replace(",", ";")
        else -> imageUrls
    }
}

/**
 * AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery 转换为 SearchCondition
 */
private fun AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery.toLocalServiceSearchCondition(): SearchCondition {
    TODO()
}
