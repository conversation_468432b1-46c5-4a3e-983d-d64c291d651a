package tech.tiangong.pop.service.settings

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.dto.settings.PriceAlertConfigKey
import tech.tiangong.pop.req.settings.BatchPriceAlertConfigurationSaveReq
import tech.tiangong.pop.req.settings.PriceAlertConfigurationQueryReq
import tech.tiangong.pop.req.settings.PriceAlertConfigurationSaveReq
import tech.tiangong.pop.resp.settings.PriceAlertConfigurationResp

/**
 * 价格预警配置服务接口
 */
interface PriceAlertConfigurationService {
    /**
     * 分页查询价格预警配置
     */
    fun page(req: PriceAlertConfigurationQueryReq): PageVo<PriceAlertConfigurationResp>

    /**
     * 根据ID查询价格预警配置详情
     */
    fun getById(id: Long): PriceAlertConfigurationResp?

    /**
     * 保存或更新价格预警配置
     */
    fun saveOrUpdate(req: PriceAlertConfigurationSaveReq): Boolean

    /**
     * 批量保存或更新价格预警配置
     */
    fun batchSaveOrUpdate(batchReq: BatchPriceAlertConfigurationSaveReq): Boolean

    /**
     * 删除价格预警配置
     */
    fun delete(id: Long): Boolean

    /**
     * 根据平台ID和品类ID查询价格预警配置
     */
    fun findByPlatformAndCategoryId(key: PriceAlertConfigKey): List<PriceAlertConfigurationResp>

    /**
     * 清除价格预警配置缓存
     */
    fun invalidateCache()
}