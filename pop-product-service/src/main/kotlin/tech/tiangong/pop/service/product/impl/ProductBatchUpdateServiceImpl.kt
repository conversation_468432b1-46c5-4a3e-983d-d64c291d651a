package tech.tiangong.pop.service.product.impl

import cn.afterturn.easypoi.excel.entity.ExportParams
import com.alibaba.excel.EasyExcel
import com.alibaba.excel.context.AnalysisContext
import com.alibaba.excel.event.AnalysisEventListener
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.constant.DatePatternConstants
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.code.generate.BusinessCodeGenerator
import team.aikero.blade.sequence.id.IdHelper
import tech.tiangong.ofp.open.common.req.StockSpuQuantityQueryReq
import tech.tiangong.ofp.open.common.resp.StockSpuQuantityQueryResp.StockSpuQuantityInfo
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductAePublishStateEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.ProductPriceInventoryDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ProductBatchUpdatePriceInventoryAeDTO
import tech.tiangong.pop.dto.product.ProductBatchUpdatePriceInventoryDTO
import tech.tiangong.pop.dto.product.ProductBatchUpdateTitleAeDTO
import tech.tiangong.pop.dto.product.ProductBatchUpdateTitleDTO
import tech.tiangong.pop.enums.CodeRuleEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskTypeEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskTypeEnum.*
import tech.tiangong.pop.enums.ProductTagEnum
import tech.tiangong.pop.external.StockClientExternal
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.resp.ImportFailureDetail
import tech.tiangong.pop.resp.ImportResultResp
import tech.tiangong.pop.resp.product.ProductPriceAlertSimpleCheckResp
import tech.tiangong.pop.service.product.ProductBatchUpdateService
import tech.tiangong.pop.service.product.ProductPriceAlertCheckService
import tech.tiangong.pop.utils.FileExportUtils
import java.io.IOException
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 商品批量更新
 * <AUTHOR>
 * @date 2025-2-25 14:34:09
 */
@Service
@Slf4j
class ProductBatchUpdateServiceImpl(
    private val businessCodeGenerator: BusinessCodeGenerator,
    private val productRepository: ProductRepository,
    private val stockClientExternal: StockClientExternal,
    private val productBatchUpdatePriceInventoryTaskRepository: ProductBatchUpdatePriceInventoryTaskRepository,
    private val productBatchUpdateTitleTaskRepository: ProductBatchUpdateTitleTaskRepository,
    private val taskInfoRepository: TaskInfoRepository,
    private val transactionManager: PlatformTransactionManager,
    private val productBatchUpdateImageTaskRepository: ProductBatchUpdateImageTaskRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val productTagRepository: ProductTagRepository,
    private val shopRepository: ShopRepository,
    private val productPriceAlertCheckService: ProductPriceAlertCheckService,
    private val temuSaleGoodsRepository: TemuSaleGoodsRepository,
) : ProductBatchUpdateService {
    companion object {
        private val PURE_DATETIME_PATTERN = DateTimeFormatter.ofPattern(DatePatternConstants.PURE_DATETIME_PATTERN)
    }

    /**
     * 批量更新-导出数据
     *
     * @param response
     * @param req
     */
    override fun exportByBatchUpdate(response: HttpServletResponse, req: ExportByBatchUpdateReq) {
        val taskTypeEnum = PlatformProductPullTaskTypeEnum.getByCode(req.taskType)
        // 创建自定义的 ExportParams 对象
        val exportParams = ExportParams()
        exportParams.title = "模板提供的数据能允许更新的只有划线价、销售价、库存，其它字段更新无更新作用"
        exportParams.isCreateHeadRows = true // 创建表头行

        when (taskTypeEnum) {
            BATCH_UPDATE_PRICE_INV -> {
                // 导出价格库存
                val fileName = "批量更新_${req.platform.platformName}_价格库存_导出_" + businessCodeGenerator.generate(CodeRuleEnum.PRODUCT_BATCH_UPDATE_EXPORT) + ".xlsx"
                try {
                    when (req.platform) {
                        PlatformEnum.LAZADA -> {
                            val allData = getExportDataByPriceInventory(req)
                            if (CollectionUtils.isEmpty(allData)) {
                                throw BusinessException("没有数据")
                            }
                            FileExportUtils.exportExcelEntity(exportParams, fileName, response, ProductBatchUpdatePriceInventoryDTO::class.java, allData)
                            return
                        }

                        PlatformEnum.AE -> {
                            val allData = getAeExportDataByPriceInventory(req)
                            if (CollectionUtils.isEmpty(allData)) {
                                throw BusinessException("没有数据")
                            }
                            FileExportUtils.exportExcelEntity(exportParams, fileName, response, ProductBatchUpdatePriceInventoryAeDTO::class.java, allData)
                            return
                        }

                        else -> {
                            throw BusinessException("<UNK>")
                        }
                    }
                } catch (e: IOException) {
                    log.error(e) { "导出失败:" }
                    throw BusinessException("导出失败")
                }
            }

            BATCH_UPDATE_TITLE -> {
                // 导出标题
                val fileName = "批量更新_${req.platform.platformName}_标题_导出_" + businessCodeGenerator.generate(CodeRuleEnum.PRODUCT_BATCH_UPDATE_EXPORT) + ".xlsx"
                try {
                    when (req.platform) {
                        PlatformEnum.LAZADA -> {
                            val allData = getExportDataByTitle(req)
                            if (CollectionUtils.isEmpty(allData)) {
                                throw BusinessException("没有数据")
                            }
                            FileExportUtils.exportExcelEntity(fileName, response, ProductBatchUpdateTitleDTO::class.java, allData)
                            return
                        }

                        PlatformEnum.AE -> {
                            val allData = getExportDataByTitle(req)
                                .map { it ->
                                    ProductBatchUpdateTitleAeDTO().apply {
                                        shopName = it.shopName
                                        spuCode = it.spuCode
                                        title = it.title
                                    }
                                }
                            if (CollectionUtils.isEmpty(allData)) {
                                throw BusinessException("没有数据")
                            }
                            FileExportUtils.exportExcelEntity(fileName, response, ProductBatchUpdateTitleAeDTO::class.java, allData)
                            return
                        }

                        else -> {
                            throw BusinessException("${req.platform.platformName} 平台暂不支持导出")
                        }
                    }
                } catch (e: IOException) {
                    log.error(e) { "导出失败:" }
                    throw BusinessException("导出失败")
                }
            }

            BATCH_UPDATE_IMAGE -> {
                // 导出图片
                throw BusinessException("暂不支持导出图片")
            }

            PUBLISH_PRODUCT -> {
                // 发布/更新
                throw BusinessException("暂不支持导出")
            }

            PULL -> {}
            SYNC -> {}
            null -> {
                throw IllegalArgumentException("类型错误")
            }
        }
    }

    /**
     * 批量更新-导入数据
     *
     * @param taskType
     * @param platform
     * @param file
     */
    override fun importByBatchUpdate(taskType: Int, platform: PlatformEnum, file: MultipartFile): ImportResultResp {
        var result = ImportResultResp()
        when (taskType) {
            BATCH_UPDATE_PRICE_INV.code -> {
                // 导入任务-价格库存
                result = loadImportDataByPriceInv(platform, file)
            }

            BATCH_UPDATE_TITLE.code -> {
                // 导入任务-标题
                result = loadImportDataByTitle(platform, file)
            }
        }
        return result
    }

    override fun checkPriceInv(platform: PlatformEnum, file: MultipartFile): List<ProductPriceAlertSimpleCheckResp> {
        val priceCheckRequests = mutableListOf<ProductPriceAlertCheckReq>()

        // 按平台类型处理导入数据并构建校验请求
        when (platform) {
            PlatformEnum.LAZADA -> {
                extractLazadaPriceCheckRequests(file, priceCheckRequests)
            }
            PlatformEnum.AE -> {
                extractAePriceCheckRequests(file, priceCheckRequests)
            }
            else -> {
                throw IllegalArgumentException("${platform.platformName} 平台暂不支持价格兜底")
            }
        }

        // 执行价格兜底
        return if (priceCheckRequests.isNotEmpty()) {
            productPriceAlertCheckService.batchCheckProducts(priceCheckRequests)
                .map { ProductPriceAlertSimpleCheckResp.from(it) }
        } else {
            emptyList()
        }
    }

    /**
     * 批量更新-提交图片更新任务
     *
     * @param req 更新图片请求
     * @return 导入结果
     */
    override fun submitBatchUpdateImageTask(req: ProductBatchUpdateImageReq) {
        // 校验SPU是否存在
        val spuList = req.spuCodes
        var tripleList: List<Triple<Long, Long, String>> = emptyList()
        var existingSpus: List<String> = emptyList()
        when (req.platform) {
            PlatformEnum.LAZADA -> {
                val list = saleGoodsRepository.listBySpuCodeAndShopId(req.shopId, req.spuCodes.toList())
                tripleList = list.map { Triple(it.productId!!, it.shopId!!, it.spuCode!!) }
                existingSpus = list.mapNotNull { it.spuCode }
            }

            PlatformEnum.AE -> {
                val list = aeSaleGoodsRepository.listBySpuCodeAndShopId(req.shopId, req.spuCodes.toList())
                tripleList = list.map { Triple(it.productId!!, it.shopId!!, it.spuCode!!) }
                existingSpus = list.mapNotNull { it.spuCode }
            }

            PlatformEnum.TEMU -> {
                val list = temuSaleGoodsRepository.listBySpuCodeAndShopId(req.shopId, req.spuCodes.toList())
                tripleList = list.map { Triple(it.productId!!, it.shopId!!, it.spuCode!!) }
                existingSpus = list.mapNotNull { it.spuCode }
            }


            else -> {}
        }

        val shop = shopRepository.getById(req.shopId)

        val nonExistentSpus = spuList.filter { !existingSpus.contains(it) }
        if (nonExistentSpus.isNotEmpty()) {
            throw BusinessException("以下SPU编码不存在：${nonExistentSpus.joinToString(", ")}")
        }

        val imageList = imageRepositoryRepository.ktQuery().`in`(ImageRepository::spuCode, spuList).list()
        val existingImageSpus = imageList.map { it.spuCode }
        val nonExistentImageSpus = spuList.filter { !existingImageSpus.contains(it) }
        if (nonExistentImageSpus.isNotEmpty()) {
            throw BusinessException("以下SPU编码不存在图片：${nonExistentImageSpus.joinToString(", ")}")
        }

        // 保存任务信息
        val saveData = mutableListOf<ProductBatchUpdateImageTask>()
        val saveTask = mutableListOf<TaskInfo>()

        // 记录任务
        tripleList
            .forEach { triple ->
                val taskData = ProductBatchUpdateImageTask()
                taskData.taskId = IdHelper.getId()
                taskData.platformId = req.platform.platformId
                taskData.shopId = triple.second
                taskData.shopName = shop.shopName
                taskData.spuCode = triple.third
                taskData.productId = triple.first
                saveData.add(taskData)

                val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())
                val taskInfo = TaskInfo()
                taskInfo.taskId = taskData.taskId
                taskInfo.taskName = "批量更新-${req.platform.platformName}-图片-$dateFormatStr"
                taskInfo.taskType = BATCH_UPDATE_IMAGE.code
                taskInfo.platformId = req.platform.platformId
                taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                saveTask.add(taskInfo)
            }

        productBatchUpdateImageTaskRepository.saveBatch(saveData)
        taskInfoRepository.saveBatch(saveTask)
    }

    /**
     * 读取导入数据-标题
     *
     * @param platform
     * @param file
     * @return
     */
    private fun loadImportDataByTitle(platform: PlatformEnum, file: MultipartFile): ImportResultResp {
        val result = ImportResultResp()
        when (platform) {
            PlatformEnum.LAZADA -> {
                EasyExcel.read(
                    file.inputStream, ProductBatchUpdateTitleDTO::class.java, object : AnalysisEventListener<ProductBatchUpdateTitleDTO>() {
                        private val cachedDataList: MutableList<ProductBatchUpdateTitleDTO> = mutableListOf()

                        override fun invoke(data: ProductBatchUpdateTitleDTO, context: AnalysisContext) {
                            // 在这里处理每一行数据，例如打印出来
                            var isError = false

                            // 抽出公共逻辑
                            fun checkField(check: Boolean, errorMessage: String) {
                                if (check) {
                                    val failureDetail = ImportFailureDetail()
                                    failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                                    failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                                    result.failureDetails.add(failureDetail)
                                    isError = true
                                }
                            }
                            // 校验
                            checkField(StringUtils.isBlank(data.shopName), "第 %s 行的”店铺“不能为空！")
                            checkField(StringUtils.isBlank(data.country), "第 %s 行的”国家站点“不能为空！")
                            checkField(StringUtils.isBlank(data.spuCode), "第 %s 行的”SPU“不能为空！")
                            checkField(StringUtils.isBlank(data.title), "第 %s 行的”标题“不能为空！")
                            if (isError) {
                                result.failCount += 1
                                return
                            }
                            cachedDataList.add(data)
                            result.successCount += 1
                        }

                        override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                            // 保存数据
                            saveData()
                        }

                        private fun saveData() {
                            if (CollectionUtils.isEmpty(cachedDataList)) {
                                log.warn { "没有数据，不进行存储" }
                                return
                            }

                            // 20一批
                            cachedDataList.chunked(20).map { batch ->
                                val saveData = mutableListOf<ProductBatchUpdateTitleTask>()
                                val saveTask = mutableListOf<TaskInfo>()
                                batch.forEach {
                                    val taskData = ProductBatchUpdateTitleTask()
                                    taskData.taskId = IdHelper.getId()
                                    taskData.shopName = it.shopName
                                    taskData.platformId = platform.platformId
                                    taskData.country = it.country
                                    taskData.spuCode = it.spuCode
                                    taskData.title = it.title
                                    saveData.add(taskData)

                                    val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())

                                    val taskInfo = TaskInfo()
                                    taskInfo.taskId = taskData.taskId
                                    taskInfo.taskName = "批量更新-${platform.platformName}-标题-$dateFormatStr"
                                    taskInfo.taskType = BATCH_UPDATE_TITLE.code
                                    taskInfo.platformId = platform.platformId
                                    taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                                    saveTask.add(taskInfo)
                                }
                                // 手动事务
                                TransactionTemplate(transactionManager).executeWithoutResult {
                                    productBatchUpdateTitleTaskRepository.saveBatch(saveData)
                                    taskInfoRepository.saveBatch(saveTask)
                                }
                            }
                            log.info { "导入-${platform.platformName}-${cachedDataList.size}条数据" }
                        }
                    }).sheet().doRead()
            }

            PlatformEnum.AE -> {
                EasyExcel.read(
                    file.inputStream, ProductBatchUpdateTitleAeDTO::class.java, object : AnalysisEventListener<ProductBatchUpdateTitleAeDTO>() {
                        private val cachedDataList: MutableList<ProductBatchUpdateTitleAeDTO> = mutableListOf()

                        override fun invoke(data: ProductBatchUpdateTitleAeDTO, context: AnalysisContext) {
                            // 在这里处理每一行数据，例如打印出来
                            var isError = false

                            // 抽出公共逻辑
                            fun checkField(check: Boolean, errorMessage: String) {
                                if (check) {
                                    val failureDetail = ImportFailureDetail()
                                    failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                                    failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                                    result.failureDetails.add(failureDetail)
                                    isError = true
                                }
                            }
                            // 校验
                            checkField(StringUtils.isBlank(data.shopName), "第 %s 行的”店铺“不能为空！")
                            checkField(StringUtils.isBlank(data.spuCode), "第 %s 行的”SPU“不能为空！")
                            checkField(StringUtils.isBlank(data.title), "第 %s 行的”标题“不能为空！")
                            if (isError) {
                                result.failCount += 1
                                return
                            }
                            cachedDataList.add(data)
                            result.successCount += 1
                        }

                        override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                            // 保存数据
                            saveData()
                        }

                        private fun saveData() {
                            if (CollectionUtils.isEmpty(cachedDataList)) {
                                log.warn { "没有数据，不进行存储" }
                                return
                            }

                            // 20一批
                            cachedDataList.chunked(20).map { batch ->
                                val saveData = mutableListOf<ProductBatchUpdateTitleTask>()
                                val saveTask = mutableListOf<TaskInfo>()
                                batch.forEach {
                                    val taskData = ProductBatchUpdateTitleTask()
                                    taskData.taskId = IdHelper.getId()
                                    taskData.shopName = it.shopName
                                    taskData.platformId = platform.platformId
                                    taskData.spuCode = it.spuCode
                                    taskData.title = it.title
                                    saveData.add(taskData)

                                    val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())

                                    val taskInfo = TaskInfo()
                                    taskInfo.taskId = taskData.taskId
                                    taskInfo.taskName = "批量更新-${platform.platformName}-标题-$dateFormatStr"
                                    taskInfo.taskType = BATCH_UPDATE_TITLE.code
                                    taskInfo.platformId = platform.platformId
                                    taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                                    saveTask.add(taskInfo)
                                }
                                // 手动事务
                                TransactionTemplate(transactionManager).executeWithoutResult {
                                    productBatchUpdateTitleTaskRepository.saveBatch(saveData)
                                    taskInfoRepository.saveBatch(saveTask)
                                }
                            }
                            log.info { "导入-${platform.platformName}-${cachedDataList.size}条数据" }
                        }
                    }).sheet().doRead()
            }

            else -> {
                throw IllegalArgumentException("${platform.platformName} 平台暂不支持导入")
            }

        }
        return result
    }

    /**
     * 读取导入数据-价格/库存，并进行价格兜底
     *
     * @param file 导入的Excel文件
     * @param platform 平台枚举
     * @return 导入结果与价格兜底结果
     */
    private fun loadImportDataByPriceInv(platform: PlatformEnum, file: MultipartFile): ImportResultResp {
        val importResult = ImportResultResp()

        // 按平台类型处理导入数据
        when (platform) {
            PlatformEnum.LAZADA -> {
                handleLazadaImportByPriceInv(file, importResult)
            }
            PlatformEnum.AE -> {
                handleAeImportByPriceInv(file, importResult)
            }
            else -> {
                throw IllegalArgumentException("${platform.platformName} 平台暂不支持导入")
            }
        }

        return importResult
    }

    /**
     * 处理Lazada平台导入
     */
    private fun handleLazadaImportByPriceInv(
        file: MultipartFile,
        result: ImportResultResp,
    ) {
        val platform = PlatformEnum.LAZADA
        // 使用EasyExcel读取导入文件
        EasyExcel.read(
            file.inputStream, ProductBatchUpdatePriceInventoryDTO::class.java,
            object : AnalysisEventListener<ProductBatchUpdatePriceInventoryDTO>() {
                private val cachedDataList: MutableList<ProductBatchUpdatePriceInventoryDTO> = mutableListOf()
                // 按国家分组的数据，用于构建价格兜底请求
                private val groupedByCountry = mutableMapOf<String, MutableList<ProductBatchUpdatePriceInventoryDTO>>()

                override fun invoke(data: ProductBatchUpdatePriceInventoryDTO, context: AnalysisContext) {
                    // 在这里处理每一行数据
                    var isError = false

                    // 抽出公共逻辑
                    fun checkField(check: Boolean, errorMessage: String) {
                        if (check) {
                            val failureDetail = ImportFailureDetail()
                            failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                            failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                            result.failureDetails.add(failureDetail)
                            isError = true
                        }
                    }

                    // 校验字段
                    checkField(StringUtils.isBlank(data.shopName), "第 %s 行的“店铺”不能为空！")
                    checkField(StringUtils.isBlank(data.country), "第 %s 行的“国家站点”不能为空！")
                    checkField(StringUtils.isBlank(data.spuCode), "第 %s 行的“SPU”不能为空！")
                    checkField(StringUtils.isBlank(data.skcCode), "第 %s 行的“SKC”不能为空！")
                    checkField(StringUtils.isBlank(data.color), "第 %s 行的“颜色”不能为空！")
                    checkField(StringUtils.isBlank(data.size), "第 %s 行的“尺码”不能为空！")

                    if (isError) {
                        result.failCount += 1
                        return
                    }

                    // 添加到缓存列表
                    cachedDataList.add(data)
                    // 按国家分组，为后续价格兜底做准备
                    if (!data.country.isNullOrBlank()) {
                        groupedByCountry.getOrPut(data.country!!) { mutableListOf() }.add(data)
                    }
                    result.successCount += 1
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 保存数据
                    saveData()
                }

                private fun saveData() {
                    if (CollectionUtils.isEmpty(cachedDataList)) {
                        log.warn { "没有数据，不进行存储" }
                        return
                    }

                    // 20一批
                    cachedDataList.chunked(20).map { batch ->
                        val saveData = mutableListOf<ProductBatchUpdatePriceInventoryTask>()
                        val saveTask = mutableListOf<TaskInfo>()
                        batch.forEach {
                            val taskData = ProductBatchUpdatePriceInventoryTask()
                            taskData.taskId = IdHelper.getId()
                            taskData.platformId = platform.platformId
                            taskData.shopName = it.shopName
                            taskData.country = it.country
                            taskData.spuCode = it.spuCode
                            taskData.skcCode = it.skcCode
                            taskData.color = it.color
                            taskData.size = it.size
                            taskData.publishState = it.status
                            taskData.tags = it.tags
                            taskData.supplyPrice = it.supplyPrice
                            taskData.purchasePrice = it.purchasePrice
                            taskData.pricingCost = it.pricingCost
                            taskData.retailPrice = it.retailPrice
                            taskData.salePrice = it.salePrice
                            taskData.regularStrikePrice = it.regularStrikePrice
                            taskData.regularSellingPrice = it.regularSellingPrice
                            taskData.purchaseStrikePrice = it.purchaseStrikePrice
                            taskData.purchaseSellingPrice = it.purchaseSellingPrice
                            taskData.stock = it.stock
                            taskData.physicalStock = it.physicalStock
                            saveData.add(taskData)

                            val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())

                            val taskInfo = TaskInfo()
                            taskInfo.taskId = taskData.taskId
                            taskInfo.taskName = "批量更新-${platform.platformName}-价格库存-$dateFormatStr"
                            taskInfo.taskType = BATCH_UPDATE_PRICE_INV.code
                            taskInfo.platformId = platform.platformId
                            taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                            saveTask.add(taskInfo)
                        }
                        // 手动事务
                        TransactionTemplate(transactionManager).executeWithoutResult {
                            productBatchUpdatePriceInventoryTaskRepository.saveBatch(saveData)
                            taskInfoRepository.saveBatch(saveTask)
                        }
                    }
                    log.info { "导入-${platform.platformName}-${cachedDataList.size}条数据" }
                }
            })
            .headRowNumber(2) // 设置为2以跳过第一行
            .sheet()
            .doRead()
    }

    /**
     * 处理AE平台导入
     */
    private fun handleAeImportByPriceInv(
        file: MultipartFile,
        result: ImportResultResp,
    ) {
        val platform = PlatformEnum.AE
        // 与Lazada类似的实现，但使用AE特定的DTO
        EasyExcel.read(
            file.inputStream, ProductBatchUpdatePriceInventoryAeDTO::class.java,
            object : AnalysisEventListener<ProductBatchUpdatePriceInventoryAeDTO>() {
                private val cachedDataList: MutableList<ProductBatchUpdatePriceInventoryAeDTO> = mutableListOf()
                // 按SPU分组的数据，用于构建价格兜底请求
                private val groupedBySpu = mutableMapOf<String, MutableList<ProductBatchUpdatePriceInventoryAeDTO>>()

                override fun invoke(data: ProductBatchUpdatePriceInventoryAeDTO, context: AnalysisContext) {
                    // 在这里处理每一行数据
                    var isError = false

                    // 抽出公共逻辑
                    fun checkField(check: Boolean, errorMessage: String) {
                        if (check) {
                            val failureDetail = ImportFailureDetail()
                            failureDetail.rowNumber = context.readRowHolder().rowIndex + 1
                            failureDetail.reason = String.format(errorMessage, context.readRowHolder().rowIndex + 1)
                            result.failureDetails.add(failureDetail)
                            isError = true
                        }
                    }

                    // 校验字段
                    checkField(StringUtils.isBlank(data.shopName), "第 %s 行的“店铺”不能为空！")
                    checkField(StringUtils.isBlank(data.spuCode), "第 %s 行的“SPU”不能为空！")
                    checkField(StringUtils.isBlank(data.skcCode), "第 %s 行的“SKC”不能为空！")
                    checkField(StringUtils.isBlank(data.color), "第 %s 行的“颜色”不能为空！")
                    checkField(StringUtils.isBlank(data.size), "第 %s 行的“尺码”不能为空！")

                    if (isError) {
                        result.failCount += 1
                        return
                    }

                    // 添加到缓存列表
                    cachedDataList.add(data)
                    // 按SPU分组，为后续价格兜底做准备
                    if (!data.spuCode.isNullOrBlank()) {
                        groupedBySpu.getOrPut(data.spuCode!!) { mutableListOf() }.add(data)
                    }
                    result.successCount += 1
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 保存数据
                    saveData()
                }

                private fun saveData() {
                    if (CollectionUtils.isEmpty(cachedDataList)) {
                        log.warn { "没有数据，不进行存储" }
                        return
                    }

                    // 20一批
                    cachedDataList.chunked(20).map { batch ->
                        val saveData = mutableListOf<ProductBatchUpdatePriceInventoryTask>()
                        val saveTask = mutableListOf<TaskInfo>()
                        batch.forEach {
                            val taskData = ProductBatchUpdatePriceInventoryTask()
                            taskData.taskId = IdHelper.getId()
                            taskData.platformId = platform.platformId
                            taskData.shopName = it.shopName
                            taskData.spuCode = it.spuCode
                            taskData.skcCode = it.skcCode
                            taskData.color = it.color
                            taskData.size = it.size
                            taskData.publishState = it.status
                            taskData.tags = it.tags
                            taskData.supplyPrice = it.supplyPrice
                            taskData.purchasePrice = it.purchasePrice
                            taskData.pricingCost = it.pricingCost
                            taskData.shipsFrom = it.shipsFrom
                            taskData.retailPrice = it.retailPrice
                            taskData.salePrice = it.salePrice
                            taskData.regularStrikePrice = it.regularStrikePrice
                            taskData.regularSellingPrice = it.regularSellingPrice
                            taskData.purchaseStrikePrice = it.purchaseStrikePrice
                            taskData.purchaseSellingPrice = it.purchaseSellingPrice
                            taskData.stock = it.stock
                            taskData.physicalStock = it.physicalStock
                            saveData.add(taskData)

                            val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())

                            val taskInfo = TaskInfo()
                            taskInfo.taskId = taskData.taskId
                            taskInfo.taskName = "批量更新-${platform.platformName}-价格库存-$dateFormatStr"
                            taskInfo.taskType = BATCH_UPDATE_PRICE_INV.code
                            taskInfo.platformId = platform.platformId
                            taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                            saveTask.add(taskInfo)
                        }
                        // 手动事务
                        TransactionTemplate(transactionManager).executeWithoutResult {
                            productBatchUpdatePriceInventoryTaskRepository.saveBatch(saveData)
                            taskInfoRepository.saveBatch(saveTask)
                        }
                    }
                    log.info { "导入-${platform.platformName}-${cachedDataList.size}条数据" }
                }
            })
            .headRowNumber(2) // 设置为2以跳过第一行
            .sheet()
            .doRead()
    }

    /**
     * 从Lazada Excel中提取价格兜底请求 - 不进行导入
     */
    private fun extractLazadaPriceCheckRequests(
        file: MultipartFile,
        priceCheckRequests: MutableList<ProductPriceAlertCheckReq>
    ) {
        val platform = PlatformEnum.LAZADA
        // 按国家分组的数据，用于构建价格兜底请求
        val groupedByCountry = mutableMapOf<String, MutableList<ProductBatchUpdatePriceInventoryDTO>>()

        // 使用EasyExcel读取导入文件
        EasyExcel.read(
            file.inputStream, ProductBatchUpdatePriceInventoryDTO::class.java,
            object : AnalysisEventListener<ProductBatchUpdatePriceInventoryDTO>() {
                override fun invoke(data: ProductBatchUpdatePriceInventoryDTO, context: AnalysisContext) {
                    if (StringUtils.isNoneBlank(data.country, data.spuCode, data.skcCode)) {
                        groupedByCountry.getOrPut(data.country!!) { mutableListOf() }.add(data)
                    }
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 构建价格兜底请求
                    buildPriceCheckRequests()
                }

                private fun buildPriceCheckRequests() {
                    // 按国家和SPU构建价格兜底请求
                    groupedByCountry.forEach { (country, items) ->
                        // 按SPU分组
                        val groupedBySpu = items.groupBy { it.spuCode }

                        groupedBySpu.forEach groupedBySpu@{ (spuCode, spuItems) ->
                            if (spuCode.isNullOrBlank()) return@groupedBySpu

                            // 创建价格兜底请求
                            val checkReq = ProductPriceAlertCheckReq(
                                spuCode = spuCode,
                                platform = platform,
                                countryCode = country,
                                skcList = buildSkcCheckList(spuItems)
                            )

                            // 添加到请求列表
                            priceCheckRequests.add(checkReq)
                        }
                    }
                }

                private fun buildSkcCheckList(items: List<ProductBatchUpdatePriceInventoryDTO>): List<ProductPriceAlertSkcCheckReq> {
                    // 按SKC分组
                    return items.groupBy { it.skcCode }
                        .mapNotNull { (skcCode, skcItems) ->
                            if (skcCode.isNullOrBlank()) return@mapNotNull null

                            // 如果只有一个SKU，就不需要创建SKU列表
                            if (skcItems.size == 1) {
                                val item = skcItems.first()
                                ProductPriceAlertSkcCheckReq(
                                    skc = skcCode,
                                    salePrice = item.salePrice,
                                    retailPrice = item.retailPrice
                                )
                            } else {
                                // 创建SKC级别的请求，包含多个SKU
                                ProductPriceAlertSkcCheckReq(
                                    skc = skcCode,
                                    skuList = skcItems.map { item ->
                                        ProductPriceAlertSkuCheckReq(
                                            sizeName = item.size,
                                            salePrice = item.salePrice,
                                            retailPrice = item.retailPrice
                                        )
                                    }
                                )
                            }
                        }
                }
            })
            .headRowNumber(2) // 设置为2以跳过第一行
            .sheet()
            .doRead()
    }

    /**
     * 从AE Excel中提取价格兜底请求 - 不进行导入
     */
    private fun extractAePriceCheckRequests(
        file: MultipartFile,
        priceCheckRequests: MutableList<ProductPriceAlertCheckReq>
    ) {
        val platform = PlatformEnum.AE
        // 按SPU分组的数据，用于构建价格兜底请求
        val groupedBySpu = mutableMapOf<String, MutableList<ProductBatchUpdatePriceInventoryAeDTO>>()
        // AE平台使用固定的全球站点代码
        val defaultCountryCode = CountryEnum.US.code

        // 使用EasyExcel读取导入文件
        EasyExcel.read(
            file.inputStream, ProductBatchUpdatePriceInventoryAeDTO::class.java,
            object : AnalysisEventListener<ProductBatchUpdatePriceInventoryAeDTO>() {
                override fun invoke(data: ProductBatchUpdatePriceInventoryAeDTO, context: AnalysisContext) {
                    // 只关心有效数据，用于价格兜底
                    if (StringUtils.isNoneBlank(data.spuCode, data.skcCode)) {
                        groupedBySpu.getOrPut(data.spuCode!!) { mutableListOf() }.add(data)
                    }
                }

                override fun doAfterAllAnalysed(p0: AnalysisContext?) {
                    // 构建价格兜底请求
                    buildPriceCheckRequests()
                }

                private fun buildPriceCheckRequests() {
                    // 按SPU构建价格兜底请求
                    groupedBySpu.forEach { (spuCode, spuItems) ->
                        if (spuCode.isBlank()) return@forEach

                        // 创建价格兜底请求
                        val checkReq = ProductPriceAlertCheckReq(
                            spuCode = spuCode,
                            platform = platform,
                            countryCode = defaultCountryCode,
                            skcList = buildSkcCheckList(spuItems)
                        )

                        // 添加到请求列表
                        priceCheckRequests.add(checkReq)
                    }
                }

                private fun buildSkcCheckList(items: List<ProductBatchUpdatePriceInventoryAeDTO>): List<ProductPriceAlertSkcCheckReq> {
                    // 按SKC分组
                    return items.groupBy { it.skcCode }
                        .mapNotNull { (skcCode, skcItems) ->
                            if (skcCode.isNullOrBlank()) return@mapNotNull null

                            // 如果只有一个SKU，就不需要创建SKU列表
                            if (skcItems.size == 1) {
                                val item = skcItems.first()
                                ProductPriceAlertSkcCheckReq(
                                    skc = skcCode,
                                    salePrice = item.salePrice,
                                    retailPrice = item.retailPrice
                                )
                            } else {
                                // 创建SKC级别的请求，包含多个SKU
                                ProductPriceAlertSkcCheckReq(
                                    skc = skcCode,
                                    skuList = skcItems.map { item ->
                                        ProductPriceAlertSkuCheckReq(
                                            sizeName = item.size,
                                            salePrice = item.salePrice,
                                            retailPrice = item.retailPrice
                                        )
                                    }
                                )
                            }
                        }
                }
            })
            .headRowNumber(2) // 设置为2以跳过第一行
            .sheet()
            .doRead()
    }

    /**
     * 获取导出数据-标题
     *
     * @param req
     * @return
     */
    private fun getExportDataByTitle(req: ExportByBatchUpdateReq): List<ProductBatchUpdateTitleDTO> {
        var pageNum = 1
        val pageSize = 500
        val allData: MutableList<ProductBatchUpdateTitleDTO> = mutableListOf()
        while (true) {
            val pageData = productRepository.getExportDataByTitle(pageNum, pageSize, req)
            if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
                break
            }

            pageNum += 1
            allData.addAll(pageData.records)
        }
        return allData
    }

    /**
     * 获取导出数据-库存/价格
     *
     * @param req
     * @return
     */
    private fun getExportDataByPriceInventory(req: ExportByBatchUpdateReq): List<ProductBatchUpdatePriceInventoryDTO> {
        var pageNum = 1
        val pageSize = 500
        val allData: MutableList<Pair<ProductPriceInventoryDto, String?>> = mutableListOf()
        while (true) {
            val pageData = productRepository.getExportDataByPriceInventory(pageNum, pageSize, req)
            if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
                break
            }
            pageData.records.forEach {
                allData.add(Pair(it, it.barcode))
            }
            pageNum += 1
        }

        // 远程调用获取库存
        val barcodes = allData.map { it.second }.distinct()
        val cacheStockMap = mutableMapOf<String, StockSpuQuantityInfo>()
        barcodes.chunked(500).forEach {
            val stockResp = stockClientExternal.querySpuQuantity(StockSpuQuantityQueryReq().apply { goodsCodes = it })
            // map key=barcode
            val stockMap = stockResp?.stockSpuQuantityList?.associateBy { it.goodsCode }
            if (!stockMap.isNullOrEmpty()) {
                cacheStockMap.putAll(stockMap)
            }
        }

        // 批量获取saleGoods标签
        val saleGoodsIds = allData.map { it.first }.mapNotNull { it.saleGoodsId }.distinct()
        val saleGoodsIdTagMap = if (saleGoodsIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(saleGoodsIds)
        } else {
            mapOf()
        }

        // 批量获取saleSkc标签
        val saleSkcIds = allData.map { it.first }.mapNotNull { it.saleSkcId }.distinct()
        val saleSkcIdTagMap = if (saleSkcIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSaleSkcIds(saleSkcIds)
        } else {
            mapOf()
        }

        // 组装dto
        val resp = mutableListOf<ProductBatchUpdatePriceInventoryDTO>()
        allData.forEach { (r, barcode) ->
            val dto = ProductBatchUpdatePriceInventoryDTO(
                shopName = r.shopName,
                country = r.country,
                spuCode = r.spuCode,
                skcCode = r.skcCode,
                color = r.color,
                size = r.size,
                status = ProductPublishStateEnum.getByCode(r.status)?.desc,
                tags = (ProductTagEnum.getDescListByPage(saleGoodsIdTagMap[r.productId] ?: emptyMap()) + ProductTagEnum.getDescListByPage(saleSkcIdTagMap[r.saleSkcId] ?: emptyMap())).distinct().joinToString(","),
                supplyPrice = r.localPrice,
                purchasePrice = r.purchasePrice,
                pricingCost = r.costPrice,
                retailPrice = r.retailPrice,
                salePrice = r.salePrice,
                regularStrikePrice = r.regularRetailPrice,
                regularSellingPrice = r.regularSalePrice,
                purchaseStrikePrice = r.purchaseRetailPrice,
                purchaseSellingPrice = r.purchaseSalePrice,
                stock = r.stockQuantity,
            )
            if (!barcode.isNullOrBlank()) {
                if (cacheStockMap.containsKey(barcode)) {
                    val stockInfo = cacheStockMap[barcode]
                    dto.physicalStock = stockInfo?.usableStockQuantity
                }
            }
            resp.add(dto)
        }
        return resp
    }

    /**
     * 获取导出数据-库存/价格
     *
     * @param req
     * @return
     */
    private fun getAeExportDataByPriceInventory(req: ExportByBatchUpdateReq): List<ProductBatchUpdatePriceInventoryAeDTO> {
        var pageNum = 1
        val pageSize = 500
        val allData: MutableList<Pair<ProductPriceInventoryDto, String?>> = mutableListOf()
        while (true) {
            val pageData = productRepository.getExportDataByPriceInventory(pageNum, pageSize, req)
            if (pageData.records.isEmpty() || pageData.records.isEmpty()) {
                break
            }
            pageData.records.forEach {
                allData.add(Pair(it, it.barcode))
            }
            pageNum += 1
        }

        // 远程调用获取库存
        val barcodes = allData.map { it.second }.distinct()
        val cacheStockMap = mutableMapOf<String, StockSpuQuantityInfo>()
        barcodes.chunked(500).forEach {
            val stockResp = stockClientExternal.querySpuQuantity(StockSpuQuantityQueryReq().apply { goodsCodes = it })
            // map key=barcode
            val stockMap = stockResp?.stockSpuQuantityList?.associateBy { it.goodsCode }
            if (!stockMap.isNullOrEmpty()) {
                cacheStockMap.putAll(stockMap)
            }
        }

        // 批量获取saleGoods标签
        val saleGoodsIds = allData.map { it.first }.mapNotNull { it.saleGoodsId }.distinct()
        val saleGoodsIdTagMap = if (saleGoodsIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(saleGoodsIds)
        } else {
            mapOf()
        }

        // 批量获取saleSkc标签
        val saleSkcIds = allData.map { it.first }.mapNotNull { it.saleSkcId }.distinct()
        val saleSkcIdTagMap = if (saleSkcIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSaleSkcIds(saleSkcIds)
        } else {
            mapOf()
        }

        // 组装dto
        val resp = mutableListOf<ProductBatchUpdatePriceInventoryAeDTO>()
        allData.forEach { (r, barcode) ->
            val dto = ProductBatchUpdatePriceInventoryAeDTO(
                shopName = r.shopName,
                spuCode = r.spuCode,
                skcCode = r.skcCode,
                color = r.color,
                size = r.size,
                status = ProductAePublishStateEnum.getByCode(r.status)?.desc,
                tags = (ProductTagEnum.getDescListByPage(saleGoodsIdTagMap[r.productId] ?: emptyMap()) + ProductTagEnum.getDescListByPage(saleSkcIdTagMap[r.saleSkcId] ?: emptyMap())).distinct().joinToString(","),
                supplyPrice = r.localPrice,
                purchasePrice = r.purchasePrice,
                pricingCost = r.costPrice,
                shipsFrom = r.shipsFrom,
                retailPrice = r.retailPrice,
                salePrice = r.salePrice,
                regularStrikePrice = r.regularRetailPrice,
                regularSellingPrice = r.regularSalePrice,
                purchaseStrikePrice = r.purchaseRetailPrice,
                purchaseSellingPrice = r.purchaseSalePrice,
                stock = r.stockQuantity,
            )
            if (!barcode.isNullOrBlank()) {
                if (cacheStockMap.containsKey(barcode)) {
                    val stockInfo = cacheStockMap[barcode]
                    dto.physicalStock = stockInfo?.usableStockQuantity
                }
            }
            resp.add(dto)
        }
        return resp
    }


    override fun exportByBatchTemuUpdate(response: HttpServletResponse, req: ExportByBatchUpdateTemuReq) {
        val fileName = "导出Temu批量更新商品_${SimpleDateFormat("yyyyMMddHHmmss").format(Date())}.xlsx"

        try {
//            FileExportUtils.exportExcelEntity(exportParams, fileName, response, ProductBatchUpdatePriceInventoryDTO::class.java, allData)

        }catch (e : Exception){

        }

    }

//    private fun getTemuExportData(req: ExportByBatchUpdateTemuReq): List<ProductBatchUpdatePriceInventoryDTO> {
//
//    }

}
