package tech.tiangong.pop.service

import team.aikero.blade.core.protocol.PageVo
import tech.tiangong.pop.req.planning.PlanningLogReq
import tech.tiangong.pop.req.planning.PlanningLogUpdatePageReq
import tech.tiangong.pop.resp.planning.PlanningLogUpdatePageResp

/**
 * 企划更新日志
 * <AUTHOR>
 * @date 2024/12/4 18:36
 */
interface PlanningLogService {

    /**
     * 新增更新记录
     * @param req
     */
    fun addLog(req: PlanningLogReq)

    /**
     * 更新记录(分页)
     * @param req
     * @return
     */
    fun page(req: PlanningLogUpdatePageReq): PageVo<PlanningLogUpdatePageResp>
}