package tech.tiangong.pop.service.product.impl

import cn.hutool.core.date.LocalDateTimeUtil
import jakarta.annotation.Resource
import org.springframework.beans.BeanUtils
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.enums.ProductUpdateTypeEnum
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.ProductSourceDataComponent
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.config.LazadaDefaultProperties
import tech.tiangong.pop.constant.ImageConstants.FileName
import tech.tiangong.pop.constant.ImageConstants.FileName.SEP
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.validation.CreateProductDtoValidator
import tech.tiangong.pop.enums.PlatformOperatorTypeEnum
import tech.tiangong.pop.enums.PriceCalculateRuleEnum
import tech.tiangong.pop.helper.ProductPublishHelper
import tech.tiangong.pop.req.product.AttributeIdValueReq
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.resp.product.ProductTitleResp
import tech.tiangong.pop.service.image.VisualImageSyncService
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductCreateTaskExecutor
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.ProductV2Service
import tech.tiangong.pop.service.product.price.cost.ProductCostPricingServiceV2
import tech.tiangong.pop.utils.CategoryUtils
import tech.tiangong.pop.utils.TransactionUtils
import tech.tiangong.sdp.sdk.client.InspirationClient
import java.time.LocalDateTime
import java.util.concurrent.ExecutorService

@Slf4j
@Service
class ProductCreateTaskExecutorImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val shopRepository: ShopRepository,
    private val productPictureRepository: ProductPictureRepository,
    private val barCodeService: BarCodeService,
    private val productSourceDataRepository: ProductSourceDataRepository,
    private val productSourceDataComponent: ProductSourceDataComponent,
    private val productV2Service: ProductV2Service,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val inspirationClient: InspirationClient,
    private val saleSkuRepository: SaleSkuRepository,
    private val productOperateLogRepository: ProductOperateLogRepository,
    private val productManageService: ProductManageService,
    private val lazadaDefaultProperties: LazadaDefaultProperties,
    private val productCostPricingServiceV2: ProductCostPricingServiceV2,
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val visualImageSyncService: VisualImageSyncService,
    private val commonProperties:CommonProperties,
) : ProductCreateTaskExecutor {


    @Transactional(rollbackFor = [Exception::class])
    override fun createProductBy(dto: CreateProductDto) {
        CreateProductDtoValidator.validate(dto)
        // 校验现货类型
        CreateProductDtoValidator.validSpotTypeAndPricingType(dto)

        val spuCode = dto.spuCode!!

        val products = productRepository.getListBySpuCode(spuCode)
        if (products.isEmpty()) {
            // 为空直接创建
            doCreateBy(dto, null)
        } else {
            // 待上架新增SKC
            for (product in products) {
                // 如果是未更新状态, 标记为更新
                if (Bool.YES.code == product.isUpdate) {
                    product.isUpdate = Bool.YES.code
                    productRepository.updateById(product)
                }

                doCreateBy(dto, product)
                // 已上架: 找出所有有pid的saleGoods, 按照product+shop维度创建记录 (已上架需要点应用更新按钮)
                productSourceDataComponent.saveProductSourceData(ProductUpdateTypeEnum.ADD_SKC, dto.toJson(), product.productId!!)
            }
        }


        // 初始化模板(待上架) 注意: 因为业务流程变动, 这里不再初始化待上架商品, 只处理新增SKC场景
        productManageService.initTemplateByProduct(productManageService.initTemplate(dto))
        //设置图包状态
        val latestProducts = productRepository.getListBySpuCode(spuCode)
        if (!latestProducts.isEmpty()) {
            val req = RefreshProductImagePackageStateReq()
            req.productIds = latestProducts.mapNotNull { it.productId }
            productManageService.refreshImagePackageState(req)
        }

        // 异步同步视觉中心图包
        TransactionUtils.afterCommit {
            runAsync(asyncExecutor) {
                //同步视觉中心图包
                visualImageSyncService.syncVisualImage(spuCode)
            }
        }
    }

    private fun doCreateBy(dto: CreateProductDto, dbProduct: Product?) {
        val (productId, product) = if (dbProduct != null) {
            Pair(dbProduct.productId, dbProduct)
        } else {
            val savedProduct = saveProduct(dto)
            val pid = savedProduct.productId

            // 设置属性
            if (dto.attributesList.isNotEmpty()) {
                val valueReqs = dto.attributesList!!.map {
                    AttributeIdValueReq().apply {
                        BeanUtils.copyProperties(it, this)
                    }
                }
                productV2Service.setProductAttribute(valueReqs, savedProduct)
            }

            Pair(pid, savedProduct)
        }

        // 校验图库资料是否存在
        val imageRepository = imageRepositoryRepository.getBySpuCode(product.spuCode!!)
        if (ProductPublishHelper.isSpotType(dto.supplyMode)) {
            if (imageRepository != null) {
                val picture = productPictureRepository.ktQuery()
                    .eq(ProductPicture::productId, product.productId)
                    .one()
                if (picture == null) {
                    val productPicture = ProductPicture().apply {
                        this.productId = product.productId
                        this.spuCode = product.spuCode
                        this.flowerUrl = dto.imageFlowerUrls?.toJson()
                        setPictureMaterialImages(dto, this)
                    }
                    productPictureRepository.save(productPicture)
                }
            }
        } else {
            val picture = productPictureRepository.ktQuery()
                .eq(ProductPicture::productId, product.productId)
                .one()
            if (picture == null) {
                val productPicture = ProductPicture().apply {
                    this.productId = product.productId
                    this.spuCode = product.spuCode
                    this.sourceImageUrl = imageRepository?.imageUrls
                    this.flowerUrl = dto.imageFlowerUrls?.toJson()
                    setPictureMaterialImages(dto, this)
                }
                productPictureRepository.save(productPicture)
            }
        }

        // 创建新商品
        doCreateProduct(dto, product, productId!!, dbProduct)

        // 创建元数据
        val productSourceData = ProductSourceData().apply {
            this.productId = productId
            this.processState = Bool.YES.code
            this.dataType = ProductUpdateTypeEnum.CREATE.code
            this.spu = dto.spuCode
            this.sourceData = dto.toJson()
        }
        productSourceDataRepository.save(productSourceData)
    }

    private fun doCreateProduct(dto: CreateProductDto, product: Product, productId: Long, dbProduct: Product?) {
        val shop: Shop?
        if (product.shopId == null && dto.shopName.isNotBlank()) {
            shop = shopRepository.getByShopName(dto.shopName!!)
            if (shop != null) {
                product.shopId = shop.shopId
                productRepository.updateById(product)
            }
        }

        // 获取数据库中已存在的SKC列表
        val dbSkcList = productSkcRepository.ktQuery()
            .eq(ProductSkc::productId, productId)
            .list()

        val dbSkcMap = dbSkcList.associateBy { it.skc }

        // 将DTO中的数据分为：新增SKC和需要更新的SKC
        val newSkcList = mutableListOf<CreateProductDto.Skc>()
        val updateSkcList = mutableListOf<Pair<CreateProductDto.Skc, ProductSkc>>()
        // 添加用于收集SKC操作信息的列表
        val addedSkcInfoList = mutableListOf<String>()
        val updatedSkcInfoList = mutableListOf<String>()

        for (skcDto in dto.dataList!!) {
            val existingSkc = dbSkcMap[skcDto.skc]
            if (existingSkc == null) {
                newSkcList.add(skcDto)
            } else {
                // 检查是否需要更新：判断是否缺少localPrice, cbPrice, purchasePrice
                val needUpdate = (skcDto.localPrice != null && existingSkc.localPrice == null) ||
                        (skcDto.cbPrice != null && existingSkc.cbPrice == null) ||
                        (skcDto.purchasePrice != null && existingSkc.purchasePrice == null)

                if (needUpdate) {
                    updateSkcList.add(Pair(skcDto, existingSkc))
                }
            }
        }

        // 如果没有需要处理的SKC，直接返回
        if (newSkcList.isEmpty() && updateSkcList.isEmpty()) {
            log.info { "没有需要处理的SKC，直接返回, spuCode=${product.spuCode}, dto: ${dto.toJson()}" }
            return
        }

        val skcList = mutableListOf<ProductSkc>()
        val barcodeReqList = mutableListOf<BatchCreateBarCodeReq>()
        val skuList = mutableListOf<SaleSku>()
        var defaultSizeLists = ProductConstant.DEFAULT_SIZE

        // 查询所有在线尺码
        val skuSizeList = saleSkuRepository.ktQuery()
            .eq(SaleSku::productId, product.productId)
            .eq(SaleSku::enable, Bool.YES.code)
            .select(SaleSku::sizeName)
            .list()
        if (skuSizeList.isNotEmpty()) {
            defaultSizeLists = skuSizeList.map { it.sizeName!! }
        }

        // 处理新增的SKC
        for (skc in newSkcList) {
            val productSkc = handleNewSkc(skc, productId, product, skcList, barcodeReqList, skuList, defaultSizeLists, dto, dbProduct)
            addedSkcInfoList.add("${productSkc.skc}(颜色:${productSkc.color})")
        }

        // 处理需要更新的SKC
        for (pair in updateSkcList) {
            handleExistingSkc(pair.first, pair.second, product, barcodeReqList, updatedSkcInfoList)
            skcList.add(pair.second)
        }

        // 处理SPOT类型的特殊逻辑
        processSkusIfSpot(dto, productSkcRepository.ktQuery().eq(ProductSkc::productId, productId).list(), productId)

        // 对所有SKC（新增和已有）创建条码
        if (barcodeReqList.isNotEmpty()) {
            barCodeService.createBarcodeByForce(barcodeReqList)
        }

        // 自动计算价格
        asyncCalPriceAfterCommit(product)

        // 记录操作日志（新增和更新SKC）
        if (addedSkcInfoList.isNotEmpty()) {
            val addLogContent = "批量新增SKC: [${addedSkcInfoList.joinToString(", ")}]"
            saveProductOperateLog(addLogContent, PlatformOperatorTypeEnum.ADD_SKC, productId)
        }

        if (updatedSkcInfoList.isNotEmpty()) {
            val updateLogContent = "批量更新SKC: [${updatedSkcInfoList.joinToString(", ")}]"
            saveProductOperateLog(updateLogContent, PlatformOperatorTypeEnum.UPDATE_SKC, productId)
        }
    }

    private fun asyncCalPriceAfterCommit(product: Product) {
        try {
            log.info { "[asyncCalPrice] 执行价格计算，spuCode=${product.spuCode}" }
            val productId = product.productId!!
            // 重新获取skc
            val skcList = productSkcRepository.getByProductId(productId)
            if (skcList.isNotEmpty()) {
                // 重算定价成本
                val costPriceResults = productCostPricingServiceV2.calculateCostPrice(productId)
                if (costPriceResults.isNotEmpty()) {
                    // 更新当前skc的成本价和定价成本
                    val maxCostPrice = costPriceResults.mapNotNull { it.costPrice }.maxOrNull()
                    if (maxCostPrice != null) {
                        val newUpdateSkcList = skcList
                            .filter { it.costPrice != maxCostPrice }
                            .onEach { it.costPrice = maxCostPrice }

                        if (newUpdateSkcList.isNotEmpty()) {
                            productSkcRepository.updateBatchById(newUpdateSkcList)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            log.error { "[asyncCalPrice] 价格计算异常, spuCode=${product.spuCode}, msg=${e.message}" }
        }
    }

    private fun handleNewSkc(
        skc: CreateProductDto.Skc,
        productId: Long,
        product: Product,
        skcList: MutableList<ProductSkc>,
        barcodeReqList: MutableList<BatchCreateBarCodeReq>,
        skuList: MutableList<SaleSku>,
        defaultSizeLists: List<String>,
        dto: CreateProductDto,
        dbProduct: Product?,
    ): ProductSkc {
        // 检查Spot类型的必填字段
        if (ProductPublishHelper.isSpotType(dto.supplyMode) && skc.skuList.isNullOrEmpty()) {
            throw BusinessException("现货try on尺码不允许为空")
        }

        if (ProductPublishHelper.isSpotType(dto.supplyMode) && skc.purchasePrice == null) {
            throw BusinessException("现货try on采购价不允许为空")
        }

        // 保存SKC
        val productSkc = ProductSkc().apply {
            BeanUtils.copyProperties(skc, this)
            this.productId = productId
            this.platformColor = skc.colorCode
            this.pictures = skc.pictures?.joinToString(",")
        }

        productSkcRepository.save(productSkc)
        skcList.add(productSkc)

        // 更新标识，新增SKC记录
        if (dbProduct != null) {
            val updateProduct = Product().apply {
                this.productId = productId
                this.isUpdate = Bool.YES.code
            }
            productRepository.updateById(updateProduct)

            // 缓存记录
            saveProductSourceData(dbProduct, dto)
        }

        // 准备条码创建请求
        val barcodeReq = prepareBarcodeRequest(product, productSkc, skc, dto, ProductConstant.STANDARD_SIZE)
        barcodeReqList.add(barcodeReq)

        // 初始化所有站点SKU
        createSkus(productId, productSkc.productSkcId!!, ProductConstant.STANDARD_SIZE, defaultSizeLists, skuList, dto)

        return productSkc
    }

    // 处理已存在但需要更新的SKC
    private fun handleExistingSkc(
        skcDto: CreateProductDto.Skc,
        existingSkc: ProductSkc,
        product: Product,
        barcodeReqList: MutableList<BatchCreateBarCodeReq>,
        updatedSkcInfoList: MutableList<String>,
    ) {
        val updateInfo = mutableMapOf<String, String>()
        // 更新缺失的字段
        var needUpdate = false
        val updateSkc = ProductSkc().apply {
            productSkcId = existingSkc.productSkcId
        }

        if (skcDto.localPrice != null && existingSkc.localPrice == null) {
            updateSkc.localPrice = skcDto.localPrice
            existingSkc.localPrice = skcDto.localPrice
            updateInfo["localPrice"] = "本土价:${skcDto.localPrice}"
            needUpdate = true
        }

        if (skcDto.cbPrice != null && existingSkc.cbPrice == null) {
            updateSkc.cbPrice = skcDto.cbPrice
            existingSkc.cbPrice = skcDto.cbPrice
            updateInfo["cbPrice"] = "跨境价:${skcDto.cbPrice}"
            needUpdate = true
        }

        if (skcDto.purchasePrice != null && existingSkc.purchasePrice == null) {
            updateSkc.purchasePrice = skcDto.purchasePrice
            existingSkc.purchasePrice = skcDto.purchasePrice
            updateInfo["purchasePrice"] = "采购价:${skcDto.purchasePrice}"
            needUpdate = true
        }

        // 如果有更新，保存到数据库
        if (needUpdate) {
            productSkcRepository.updateById(updateSkc)

            updatedSkcInfoList.add("${existingSkc.skc}(${updateInfo.values.joinToString(",")})")
        }

        val barcodeReq = getBatchCreateBarCodeReq(existingSkc, product)
        barcodeReqList.add(barcodeReq)
    }

    // 为已存在的SKC也准备条码创建请求（方法有幂等逻辑，不会重复创建）
    private fun getBatchCreateBarCodeReq(existingSkc: ProductSkc, product: Product): BatchCreateBarCodeReq {
        return BatchCreateBarCodeReq().apply {
            categoryCode = product.categoryCode
            categoryName = product.categoryName
            spuCode = product.spuCode
            skcCode = existingSkc.skc
            mainImgUrl = existingSkc.pictures // 使用现有图片
            designImgUrl = existingSkc.pictures
            supplyMode = product.supplyMode
            color = existingSkc.color
            groupName = product.sizeGroupName
            sourceGroupCode = product.sizeGroupCode
            localPrice = existingSkc.localPrice
            sizeValues = ProductConstant.STANDARD_SIZE
        }
    }

    // 创建SKUs
    private fun createSkus(
        productId: Long,
        skcId: Long,
        allSizeList: List<String>,
        defaultSizeLists: List<String>,
        skuList: MutableList<SaleSku>,
        dto: CreateProductDto,
    ) {
        val saleGoodsList = saleGoodsRepository.ktQuery()
            .eq(SaleGoods::productId, productId)
            .list()

        // 初始化所有站点SKU
        for (country in getCountryList()) {
            for (sizeName in allSizeList) {
                val saleSku = SaleSku().apply {
                    // 现货try on
                    if (ProductPublishHelper.isSpotType(dto.supplyMode)) {
                        enable = Bool.NO.code
                        publishState = ProductPublishStateEnum.IN_ACTIVE.code
                    }
                    // 原来逻辑
                    else {
                        if (defaultSizeLists.contains(sizeName)) {
                            enable = Bool.YES.code
                            publishState = ProductPublishStateEnum.ACTIVE.code
                        } else {
                            enable = Bool.NO.code
                            publishState = ProductPublishStateEnum.IN_ACTIVE.code
                        }
                    }

                    if (saleGoodsList.isNotEmpty()) {
                        saleGoodsList.find { it.country == country }?.let {
                            saleGoodsId = it.saleGoodsId
                        }
                    }

                    this.productId = productId
                    this.sizeName = sizeName
                    this.productSkcId = skcId
                    this.country = country
                    this.stockQuantity = lazadaDefaultProperties.defaultStock.toLong()
                }
                skuList.add(saleSku)
            }
        }
    }

    // 准备条码创建请求
    private fun prepareBarcodeRequest(
        product: Product,
        productSkc: ProductSkc,
        skc: CreateProductDto.Skc,
        dto: CreateProductDto,
        allSizeList: List<String>,
    ): BatchCreateBarCodeReq {
        return BatchCreateBarCodeReq().apply {
            categoryCode = product.categoryCode
            categoryName = product.categoryName
            spuCode = product.spuCode
            skcCode = productSkc.skc
            mainImgUrl = getMainImgUrl(dto)
            skc.pictures?.takeIf { it.isNotEmpty() }?.let {
                mainImgUrl = it.joinToString(",")
            }
            supplyMode = product.supplyMode
            designName = dto.designName
            designGroup = dto.designGroup
            localPrice = productSkc.localPrice
            inspiraImgUrl = dto.inspiraImgUrl
            color = productSkc.color
            groupName = product.sizeGroupName
            sourceGroupCode = product.sizeGroupCode
            localPrice = productSkc.localPrice
            sizeValues = allSizeList
        }
    }

    // 保存产品源数据
    private fun saveProductSourceData(dbProduct: Product, dto: CreateProductDto) {
        val productSourceData = ProductSourceData().apply {
            this.productId = dbProduct.productId
            this.spu = dbProduct.spuCode
            this.dataType = ProductUpdateTypeEnum.ADD_SKC.code
            this.sourceData = dto.toJson()
            this.processState = Bool.YES.code
        }
        productSourceDataRepository.save(productSourceData)
    }

    private fun processSkusIfSpot(dto: CreateProductDto, skcs: List<ProductSkc>, productId: Long) {
        if (!ProductPublishHelper.isSpotType(dto.supplyMode)) {
            return
        }
        if (skcs.isEmpty()) {
            log.info { "spot类型，skc为空, spuCode=${dto.spuCode}, dto=${dto.toJson()}" }
            return
        }

        val skuList = saleSkuRepository.ktQuery()
            .eq(SaleSku::productId, productId)
            .eq(SaleSku::enable, Bool.YES.code)
            .list()

        val dataList = dto.dataList!!
        // 生成全局尺码并集（所有SKC的sizeName去重集合）
        val globalSizeUnion = dataList.asSequence()
            .flatMap { skc -> skc.skuList?.asSequence() ?: emptySequence() }
            .mapNotNull { it.sizeName }
            .toMutableSet()

        if (skuList.isNotEmpty()) {
            globalSizeUnion.addAll(skuList.map { it.sizeName!! })
        }

        // 遍历所有SKC，强制覆盖全局并集中的每个尺码
        val saleSkuList = mutableListOf<SaleSku>()

        // 为每个SKC和尺码组合创建SaleSku
        for (skc in dataList) {
            val currentSkcSizes = skc.skuList?.mapNotNull { it.sizeName }?.toSet() ?: emptySet()
            val productSkc = skcs.find { it.skc == skc.skc }

            for (size in globalSizeUnion) {
                val saleSku = SaleSku().apply {
                    productSkcId = productSkc?.productSkcId
                    // enable=1条件：尺码存在于全局并集中（存在即有效）
                    enable = if (globalSizeUnion.contains(size)) Bool.YES.code else Bool.NO.code
                    sizeName = size
                    // publishState=1条件：当前SKC包含该尺码
                    publishState = if (currentSkcSizes.contains(size))
                        ProductPublishStateEnum.ACTIVE.code
                    else
                        ProductPublishStateEnum.IN_ACTIVE.code
                }
                saleSkuList.add(saleSku)
            }
        }

        if (saleSkuList.isEmpty()) {
            return
        }

        for (saleSku in saleSkuList) {
            saleSku.productSkcId?.let { skcId ->
                // 更新同一SKC下的尺码
                saleSkuRepository.ktUpdate()
                    .eq(SaleSku::sizeName, saleSku.sizeName)
                    .eq(SaleSku::productSkcId, skcId)
                    .eq(SaleSku::productId, productId)
                    .set(SaleSku::enable, saleSku.enable)
                    .set(SaleSku::publishState, saleSku.publishState)
                    .update()

                // 更新其他SKC下的相同尺码
                saleSkuRepository.ktUpdate()
                    .ne(SaleSku::productSkcId, skcId)
                    .eq(SaleSku::productId, productId)
                    .eq(SaleSku::sizeName, saleSku.sizeName)
                    .set(SaleSku::enable, saleSku.enable)
                    .update()
            }
        }
    }

    private fun saveProduct(dto: CreateProductDto): Product {
        val publishCategories = publishCategoryRepository.listWithCache()
        if (publishCategories.isEmpty()) {
            throw BusinessException("请维护品类管理!")
        }
        if (dto.categoryName.isNullOrBlank()) throw BusinessException("品类不能为空")

        val saveProduct = Product().apply {
            BeanUtils.copyProperties(dto, this)

            // 设置竞品价格
            setCmoPrice(this)

            countrys = dto.countrys?.joinToString(",") ?: ""

            // 设置标题
            if (dto.titleList.isNotEmpty()) {
                productTitle = dto.titleList!!.joinToString(" ") { it.trim() }
                val countryList = LazadaCountryEnum.getCountryList()
                val countryTitleList = countryList.map { country ->
                    ProductTitleResp().apply {
                        this.country = country
                        this.countryName = LazadaCountryEnum.getDescByCode(country)
                    }
                }
                allCountryTitle = countryTitleList.toJson()
            }

            // 设置品类ID
            categoryName = dto.categoryName!!.replace("-", ">")
            val publishCategoryIdByPath = CategoryUtils.findPublishCategoryIdByPath(publishCategories, categoryName!!)
                ?: throw BusinessException("品类: $categoryName 未设置")

            categoryId = publishCategoryIdByPath
            mainImgUrl = getMainImgUrl(dto)

            packageDimensionsLength = lazadaDefaultProperties.packageDimensionsLength.toString()
            packageDimensionsHeight = lazadaDefaultProperties.packageDimensionsHeight.toString()
            packageDimensionsWidth = lazadaDefaultProperties.packageDimensionsWidth.toString()
            packageWeight = lazadaDefaultProperties.packageWeight

            dto.imageLabelInfoList?.takeIf { it.isNotEmpty() }?.let {
                imageLabelInfoList = it.toJson()
            }
        }

        productRepository.save(saveProduct)
        return saveProduct
    }

    private fun setCmoPrice(product: Product) {
        // 竞价价格
        product.inspiraSourceId?.let { inspiraId ->
            try {
                val response = inspirationClient.getByInspirationOrPickingId(inspiraId)
                if (!response.successful || response.data == null) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，spuCode=${product.spuCode}，error=$response" }
                } else {
                    val data = response.data
                    data!!.inspirationInfo?.let {
                        product.cmpSalePrice = it.salePrice
                        product.cmpRetailPrice = it.retailPrice
                    }
                }
            } catch (e: Exception) {
                log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，spuCode=${product.spuCode}，exception=${e.message}" }
            }
        }
    }

    private fun getCountryList(): List<String> {
        return LazadaCountryEnum.getCountryCodeList()
    }

    /**
     * 保存产品操作日志
     *
     * @param content 日志内容
     * @param operationType 操作类型
     * @param productId 产品ID
     */
    private fun saveProductOperateLog(content: String, operationType: PlatformOperatorTypeEnum, productId: Long) {
        runAsync(asyncExecutor) {
            val productOperateLog = ProductOperateLog().apply {
                this.content = content
                this.logType = operationType.code
                this.productId = productId
            }
            productOperateLogRepository.save(productOperateLog)
        }
    }

    private fun setPictureMaterialImages(dto: CreateProductDto, productPicture: ProductPicture) {
        if (dto.materialImageList.isNotEmpty()) {
            dto.materialImageList!!.forEachIndexed { i, image ->
                if (image.orgImgName.isNullOrBlank()) {
                    image.orgImgName = "${dto.spuCode}${SEP}${FileName.MATERIAL}$SEP${i + 1}"
                }
            }
            productPicture.materialImages = dto.materialImageList?.toJson()
        }
    }

    private fun getMainImgUrl(dto: CreateProductDto): String? {
        var mainImgUrl = dto.mainImgUrl
        if (mainImgUrl != null) {
            val commaIndex = mainImgUrl.indexOf(',')
            if (commaIndex > 0) {
                mainImgUrl = mainImgUrl.substring(0, commaIndex)
            }
            // 兜底逻辑：超长直接丢弃
            if (mainImgUrl.length > 512) {
                mainImgUrl = null
            }
        }
        return mainImgUrl
    }
}