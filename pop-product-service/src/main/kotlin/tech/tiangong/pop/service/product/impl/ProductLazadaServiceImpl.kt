package tech.tiangong.pop.service.product.impl

import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.ObjectUtils
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.enums.Bool.NO
import team.aikero.blade.core.enums.Bool.YES
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.sequence.id.IdHelper.getId
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.bo.ProductPublishInfoBO
import tech.tiangong.pop.common.constant.LazadaConstants
import tech.tiangong.pop.common.constant.LazadaConstants.LAZADA_API_SUCCESS_CODE
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.*
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.common.req.BatchCreateBarCodeReq
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.component.LazadaProductSyncComponent
import tech.tiangong.pop.component.MarketStyleComponent
import tech.tiangong.pop.component.ProductSkuSyncComponent
import tech.tiangong.pop.component.lazada.CallLazadaComponent
import tech.tiangong.pop.component.lazada.LazadaProductPriceAlertComponent
import tech.tiangong.pop.component.lazada.LazadaUpdateProductComponent
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.mq.MessageRecord
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.image.ImageCollectionDTO
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.dto.product.ComboBarcodeInfoDto
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.external.InspirationClientExternal
import tech.tiangong.pop.helper.ImageCollectionHelper
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.product.AutoCalPriceReq
import tech.tiangong.pop.req.product.FetchProductReq
import tech.tiangong.pop.req.product.lazada.*
import tech.tiangong.pop.resp.category.PublishAttributePairResp
import tech.tiangong.pop.resp.product.AutoCalCountryPriceResp
import tech.tiangong.pop.resp.product.ProductFrontUrlResp
import tech.tiangong.pop.resp.product.ProductTitleResp
import tech.tiangong.pop.resp.product.lazada.*
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp
import tech.tiangong.pop.service.mq.MessageRecordService
import tech.tiangong.pop.service.product.BarCodeService
import tech.tiangong.pop.service.product.ProductLazadaService
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.update.ProductUpdateServiceSelector
import tech.tiangong.pop.utils.getRootMessage
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*
import java.util.concurrent.ExecutorService

/**
 * 商品-Lazada
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
@Slf4j
@Service
class ProductLazadaServiceImpl(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private var lazadaProductSyncComponent: LazadaProductSyncComponent,
    private var productSkuSyncComponent: ProductSkuSyncComponent,
    private val shopRepository: ShopRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val platformProductPullRepository: PlatformProductPullRepository,
    private val transactionManager: PlatformTransactionManager,
    private val taskInfoRepository: TaskInfoRepository,
    private val saleSkcRepository: SaleSkcRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val inspirationClientExternal: InspirationClientExternal,
    private val productAttributesRepository: ProductAttributesRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val barCodeService: BarCodeService,
    private val productPictureRepository: ProductPictureRepository,
    private val lazadaUpdateProductComponent: LazadaUpdateProductComponent,
    private val productSourceDataRepository: ProductSourceDataRepository,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val productPriceManagementService: ProductPriceManagementService,
    private val callLazadaComponent: CallLazadaComponent,
    private val messageRecordService: MessageRecordService,
    private val imageCollectionHelper: ImageCollectionHelper,
    private val productTagRepository: ProductTagRepository,
    private val productUpdateServiceSelector: ProductUpdateServiceSelector,
    private val lazadaProductPriceAlertComponent: LazadaProductPriceAlertComponent,
    private val productSyncLogRepository: ProductSyncLogRepository,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val marketStyleComponent: MarketStyleComponent,
) : ProductLazadaService {

    /**
     * 固定平台枚举-LAZADA
     */
    private val platformEnum = LAZADA
    private val channelEnum = ALIBABA

    // 成本价更新-历史时间
    private val costPriceUpdateHistoryTime = LocalDateTime.of(2025, 6, 1, 0, 0, 0)

    @Transactional(rollbackFor = [Exception::class])
    override fun submitPullTask(req: ProductLazadaPullReq) {
        // 获取店铺信息
        val shop = shopRepository.getById(req.shopId) ?: throw BusinessException("店铺不存在")

        val taskInfoList = mutableListOf<TaskInfo>()

        // 创建任务列表
        val tasks = mutableListOf<PlatformProductPullTask>()

        // 遍历商品ID
        req.platformProductIds.forEach { platformProductId ->
            // 根据店铺类型处理
            when (shop.countryType) {
                // 本土店：创建单条记录
                CountryTypeEnum.ASC.code -> {
                    val info = TaskInfo().apply {
                        this.taskId = IdHelper.getId()
                        this.taskName = "${PlatformProductPullTaskTypeEnum.PULL.desc}-${shop.shopName}-$platformProductId"
                        this.taskType = PlatformProductPullTaskTypeEnum.PULL.code
                        this.platformId = LAZADA.platformId
                        this.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                        this.retryCount = 0
                    }
                    taskInfoList.add(info)
                    tasks.add(createPullTask(info.taskId!!, shop, platformProductId.toString()))
                }
                // 跨境店：为每个国家创建记录
                CountryTypeEnum.CB.code -> {
                    LazadaCountryEnum.entries.forEach { lazadaCountry ->
                        val info = TaskInfo().apply {
                            this.taskId = IdHelper.getId()
                            this.taskName = "${PlatformProductPullTaskTypeEnum.PULL.desc}-${shop.shopName}-$platformProductId"
                            this.taskType = PlatformProductPullTaskTypeEnum.PULL.code
                            this.platformId = LAZADA.platformId
                            this.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                            this.retryCount = 0
                        }
                        taskInfoList.add(info)
                        tasks.add(createPullTask(info.taskId!!, shop, platformProductId.toString(), lazadaCountry.code))
                    }
                }

                else -> throw BusinessException("无效的店铺类型：${shop.countryType}")
            }
        }

        // 批量保存任务
        platformProductPullRepository.saveBatch(tasks)
        taskInfoRepository.saveBatch(taskInfoList)
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun submitSyncTask(req: ProductLazadaSyncReq) {
        // 获取商品信息并按 productId 分组
        val saleGoodsByProduct = saleGoodsRepository.listByProductIds(req.productIds)
            .groupBy { it.productId }

        if (saleGoodsByProduct.isEmpty()) {
            throw BusinessException("商品不存在")
        }

        val taskInfoList = mutableListOf<TaskInfo>()

        // 创建任务列表
        val tasks = saleGoodsByProduct.flatMap { (productId, goodsList) ->
            // 按 country 分组
            val goodsByCountry = goodsList.groupBy { it.country }

            req.country.map { targetCountry ->
                // 1. 优先获取目标 country 的商品
                val saleGoods = goodsByCountry[targetCountry]?.firstOrNull()
                    ?: goodsList.firstOrNull { it.platformProductId != null }
                    ?: goodsList.firstOrNull()
                    ?: throw BusinessException("商品ID $productId 无有效记录")

                // 2. 获取 platformProductId：如果当前商品没有，则从同 productId 下找第一个有效的
                val effectivePlatformProductId = saleGoods.platformProductId
                    ?: goodsList.firstOrNull { it.platformProductId != null }?.platformProductId
                    ?: throw BusinessException("商品ID $productId 的平台商品ID不能为空")

                val info = TaskInfo().apply {
                    this.taskId = IdHelper.getId()
                    this.taskName = "${PlatformProductPullTaskTypeEnum.SYNC.desc}-${saleGoods.shopName}-${saleGoods.productId}-$targetCountry"
                    this.taskType = PlatformProductPullTaskTypeEnum.SYNC.code
                    this.platformId = LAZADA.platformId
                    this.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                    this.retryCount = 0
                }
                taskInfoList.add(info)

                PlatformProductPullTask().apply {
                    platformProductPullTaskId = info.taskId
                    taskName = info.taskName
                    taskType = info.taskType
                    taskStatus = info.taskStatus
                    retryCount = info.retryCount

                    channelId = saleGoods.channelId
                    platformId = saleGoods.platformId
                    platformProductId = effectivePlatformProductId  // 使用找到的有效 platformProductId
                    this.productId = saleGoods.productId
                    this.spuCode = saleGoods.spuCode
                    shopId = saleGoods.shopId
                    shopName = saleGoods.shopName
                    country = targetCountry
                }
            }
        }.toList()

        if (tasks.isEmpty()) {
            throw BusinessException("没有需要同步的商品")
        }

        platformProductPullRepository.saveBatch(tasks)
        taskInfoRepository.saveBatch(taskInfoList)
    }


    /**
     * 根据任务拉取数据并比较更新
     *
     * @param task
     */
    override fun pullLazadaProductDetail(task: PlatformProductPullTask) {

        if (task.platformProductId.isNullOrBlank() || task.country.isNullOrBlank() || task.shopId == null) {
            log.error { "拉取商品详情失败，参数错误，task:${task.toJson()}" }
            return
        }
        val shop = shopRepository.getById(task.shopId)
        if (shop == null) {
            log.error { "拉取商品详情失败，店铺不存在，task:${task.toJson()}" }
            taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.FAILED, "店铺不存在:" + task.shopName)
            return
        }
        val result: LazadaProductItemDetailResp
        val spu: LazadaOriginalProductSpu?
        val skuList: List<LazadaOriginalProductSku>?

        try {
            // 任务进行中
            taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.PROCESSING, null)

            val resultData = lazadaProductSyncComponent.retryGetLzdProductDetail(
                task.platformProductId!!,
                task.country!!,
                shop.token!!,
                shop.shortCode!!
            )
            result = resultData.first
            spu = resultData.second?.first
            skuList = resultData.second?.second

            if (LAZADA_API_SUCCESS_CODE != result.code) {
                if (Objects.equals("207", result.code)) {
                    log.warn { "拉取商品详情-商品不存在，task=${task.toJson()}" }
                    taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, result.message)
                    return
                }
                if (Objects.equals(result.message, "E0901: Limit service request speed in server side temporarily.")) {
                    log.warn { "拉取商品详情-限流，改为排队中, 继续等下一波拉取重试 task=${task.toJson()}" }
                    taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.PENDING, result.message)
                    return
                }
                taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.FAILED, result.message)
                return
            }

            if (spu == null || skuList == null) {
                taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.FAILED, "拉取API请求成功, 但商品为空 message=${result.message}")
                return
            }
        } catch (e: Exception) {
            taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.FAILED, "拉取Lzd商品异常 ${e.message}")
            throw e
        }

        // 根据PID比较本地商品并更新(以下开始手动事务) 比较SPU, SKU(s)
        try {
            // 手动事务
            TransactionTemplate(transactionManager).executeWithoutResult {
                productSkuSyncComponent.syncSku(
                    task.shopId!!,
                    task.productId,
                    task.platformProductId!!,
                    task.country!!,
                    spu,
                    skuList
                )
            }
            // 任务完成
            taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.COMPLETED, null)
        } catch (e: Exception) {
            log.error(e) { "处理本地商品异常，task=${task.toJson()}" }
            taskInfoRepository.updateStatus(task.platformProductPullTaskId!!, PlatformProductPullTaskStatusEnum.FAILED, "处理本地商品异常 ${e.message}")
        }
    }

    /**
     * 创建商品拉取任务
     *
     * @param shop 店铺信息
     * @param platformProductId 平台商品ID
     * @param country 国家代码（可选，默认使用店铺国家）
     * @return 创建的任务实例
     */
    private fun createPullTask(
        taskId: Long,
        shop: Shop,
        platformProductId: String,
        country: String = shop.country?.uppercase(Locale.ENGLISH) ?: "",
    ): PlatformProductPullTask = PlatformProductPullTask().apply {
        platformProductPullTaskId = taskId
        taskName = "${PlatformProductPullTaskTypeEnum.PULL.desc}-${shop.shopName}-$platformProductId"
        taskType = PlatformProductPullTaskTypeEnum.PULL.code
        taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
        channelId = ALIBABA.channelId
        platformId = LAZADA.platformId
        this.platformProductId = platformProductId
        shopId = shop.shopId
        shopName = shop.shopName
        this.country = country
        retryCount = 0
    }

    /**
     * 根据sellerSku拉取商品详情
     * @param sellerSku
     * @param shopName
     * @param country
     */
    override fun pullLazadaProductDetailBySellerSku(sellerSku: String, shopName: String, country: String) {
        val shop = shopRepository.getByShopName(shopName, LAZADA) ?: throw BusinessException("店铺不存在")
        val resultData = lazadaProductSyncComponent.getBySellerSku(
            sellerSku,
            country,
            shop.token!!,
            shop.shortCode!!
        )
        val result = resultData.first
        val productList = resultData.second
        try {
            productList.forEach {
                val spu = it.first
                val skuList = it.second
                if (spu == null || skuList == null) {
                    log.error { "拉取商品详情-商品为空 message=${result.message}" }
                    return@forEach
                }
                // 手动事务
                TransactionTemplate(transactionManager).executeWithoutResult {
                    productSkuSyncComponent.syncSku(
                        shop.shopId!!,
                        null,
                        spu.itemId!!.toString(),
                        country,
                        spu,
                        skuList
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "处理本地商品异常 sellerSku: $sellerSku, shopName: $shopName, country: $country" }
            throw e
        }
    }


    /**
     * 异常商品修复-拉取Lazada数据
     * @param req
     */
    override fun fetchBySellerSku(req: FetchProductReq) {
        val shop = shopRepository.getById(req.shopId)
        if (Objects.isNull(shop)) {
            throw BaseBizException("店铺不存在")
        }

        var countryList = LazadaCountryEnum.getCountryList()
        if (CollectionUtils.isNotEmpty(req.countryList)) {
            countryList = req.countryList!!
        }
        countryList.forEach { country ->
            if (req.sellerSkuList != null) {
                req.sellerSkuList?.forEach { sellerSku ->
                    pullLazadaProductDetailBySellerSku(sellerSku, shop.shopName!!, country)
                }
            }
        }
    }

    override fun detail(req: ProductLazadaDetailReq): ProductLazadaDetailResp {

        if (req.saleGoodsId != null) {
            return detailBySingleCountry(req)
        }

        /*
        以下是全站点逻辑-不含组合商品
         */

        // 获取商品基础信息
        val product = productRepository.getById(req.productId)
            ?: throw IllegalArgumentException("商品不存在")

        // 3. 获取售卖商品信息
        val saleGoodsList: List<SaleGoods> = if (req.saleGoodsId != null) {
            // 如果前端传了saleGoodsId，则直接使用
            val saleGoods = saleGoodsRepository.getById(req.saleGoodsId)
                ?: throw IllegalArgumentException("销售商品不存在")
            listOf(saleGoods)
        } else {
            // 通过lazada_spu_id查询所有相关的已上架商品
            val queryWrapper = KtQueryWrapper(SaleGoods::class.java)
                .eq(SaleGoods::productId, req.productId)
                .eq(SaleGoods::shopId, req.shopId)

            saleGoodsRepository.list(queryWrapper)
                .also { require(it.isNotEmpty()) { "销售商品不存在" } }
        }

        // 提取上下架时间信息
        val publishInfo = extractProductPublishInfo(saleGoodsList)

        // 确定使用哪个saleGoods进行后续处理
        val primarySaleGoods = saleGoodsList.first()

        val saleGoodsIds = saleGoodsList.map { it.saleGoodsId!! }

        // 获取sale_skc信息
        val saleSkcs = saleSkcRepository.ktQuery()
            .`in`(SaleSkc::saleGoodsId, saleGoodsIds)
            .eq(SaleSkc::combo, NO.code)
            .eq(SaleSkc::state, YES.code)
            .orderByDesc(SaleSkc::createdTime)
            .list()
        // 获取sale_sku信息
        val saleSkus = saleSkuRepository.ktQuery()
            .`in`(SaleSku::saleGoodsId, saleGoodsIds)
            .orderByDesc(SaleSku::createdTime)
            .list()

        // 获取商品属性
        val attributes = productAttributesRepository.listByProductId(product.productId!!, platformEnum.platformId)

        // 获取图片信息
        val imageCollection = getImages(product.spuCode!!)

        val saleSkcIdList = saleSkcRepository.findAllBySaleGoodsIds(saleGoodsIds).mapNotNull { it.saleSkcId }

        // 批量获取product标签
        val saleSkcIdTagMap = if (saleSkcIdList.isNotEmpty()) {
            productTagRepository.getTagMapByLazadaSaleSkcIds(saleSkcIdList)
        } else {
            mapOf()
        }
        // 市场(一级节点)
        val marketMap = marketStyleComponent.getMarketNodeMap(1)
        // 系列(二级节点) 市场-系列
        val marketSeriesMap = marketStyleComponent.getMarketNodeMap(2)
        // 风格(三级节点) 市场-系列-风格
        val marketStyleMap = marketStyleComponent.getMarketNodeMap(3)
        // 构造返回结果
        return ProductLazadaDetailResp().apply {
            // 基础商品信息
            this.productId = product.productId
            this.shopId = req.shopId
            this.mainImgUrl = product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.clothingStyleName = product.clothingStyleName
            this.planningType = product.planningType
            this.marketCode = product.marketCode
            this.marketName = marketMap?.get(product.marketCode)
            this.marketSeriesCode = product.marketSeriesCode
            this.marketSeriesName = marketSeriesMap?.get(product.marketSeriesCode)
            // TODO 兼容历史数据，后续字典维护好可去掉
            this.clothingStyleName = product.clothingStyleName ?: marketStyleMap?.get(product.clothingStyleCode)

            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl

            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark
            this.creatorName = product.creatorName
            this.createdTime = product.createdTime
            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves
            this.printType = product.printType
            this.update = saleGoodsList.firstOrNull { it.updateState == YES.code }?.updateState ?: NO.code
            this.costPriceUpdateState = saleGoodsList.firstOrNull { it.costPriceUpdateState == YES.code }?.costPriceUpdateState ?: NO.code

            // 已上架商品特有信息 - 使用提取的发布信息
            this.firstPublishTime = publishInfo.firstPublishTime
            this.firstPublishUserName = publishInfo.publishUserName
            this.latestPublishTime = publishInfo.latestPublishTime
            this.latestPublishUserId = publishInfo.latestPublishUserId
            this.latestPublishUserName = publishInfo.latestPublishUserName
            this.latestOfflineTime = publishInfo.latestOfflineTime
            this.latestOfflineUserName = publishInfo.latestOfflineUserName
            this.reviserName = primarySaleGoods.reviserName
            this.revisedTime = primarySaleGoods.revisedTime

            // 灵感来源信息
            if (product.inspiraSourceId != null) {
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopList = mutableListOf<ProductLazadaDetailShopResp>().apply {
                            this.add(ProductLazadaDetailShopResp().apply {
                                this.shopId = data.pickingInfo?.shopId
                                this.shopName = data.pickingInfo?.shopName
                            })
                        }.toList()
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}" }
                }
            }

            // Lazada详情
            this.lazadaDetail = ProductLazadaSpecificDetailResp().apply {
                // SPU信息
                this.saleGoodsId = primarySaleGoods.saleGoodsId
                this.productTitle = primarySaleGoods.productTitle
                this.productTitleList = saleGoodsList.map { saleGoods ->
                    ProductTitleResp().apply {
                        this.country = saleGoods.country
                        this.countryName = CountryEnum.getByCode(saleGoods.country)?.desc
                        this.title = saleGoods.productTitle
                    }
                }
                this.brandId = primarySaleGoods.brandId
                this.brandName = primarySaleGoods.brandName
                this.stockTypeName = primarySaleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.countryStockTypeName = primarySaleGoods.countryStockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.delayDeliveryDays = primarySaleGoods.delayDeliveryDays
                this.sizeGroupName = product.sizeGroupName
                this.sizeGroupCode = product.sizeGroupCode

                // 类目信息
                this.categoryId = product.categoryId
                this.categoryMappingId = getPlatformCategory(product, LAZADA.platformId)?.categoryMappingId
                this.platformName = LAZADA.platformName
                this.platformCategoryName = primarySaleGoods.platformCategoryName

                // 包裹信息
                this.packageDimensionsLength = primarySaleGoods.packageDimensionsLength
                this.packageDimensionsWidth = primarySaleGoods.packageDimensionsWidth
                this.packageDimensionsHeight = primarySaleGoods.packageDimensionsHeight
                this.packageWeight = primarySaleGoods.packageWeight

                // 商品属性
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }

                // 图片信息
                this.mainUrlList = imageCollection.limitedMainImages()
                this.detailImageList = imageCollection.detailImages

                // 按颜色分组SKC数据 - 一个颜色可能对应多个销售站点的SKC
                val skcsByColor = saleSkcs.groupBy { it.color }

                // 将SKU按SKC ID分组，用于快速查找
                val skuMapBySkcId = saleSkus.groupBy { it.saleSkcId }

                // 重构SKC列表，以颜色为单位进行聚合
                this.skcList = skcsByColor.map { (color, colorSkcs) ->
                    // 使用每种颜色的第一条SKC记录作为代表
                    val representativeSKC = colorSkcs.first()

                    // 收集该颜色组下的所有saleSkcId
                    val saleSkcIds = colorSkcs.map { it.saleSkcId }

                    ProductLazadaSpecificDetailResp.ProductLazadaSkcResp().apply {
                        this.saleSkcId = representativeSKC.saleSkcId
                        this.skc = representativeSKC.skc
                        this.color = representativeSKC.color
                        this.colorCode = representativeSKC.colorCode
                        this.platformColor = representativeSKC.platformColor
                        this.pictures = representativeSKC.pictures
                        this.cbPrice = representativeSKC.cbPrice
                        this.localPrice = representativeSKC.localPrice
                        this.purchasePrice = representativeSKC.purchasePrice
                        this.costPrice = representativeSKC.costPrice
                        this.combo = representativeSKC.combo
                        this.allowedUpdateUnit = representativeSKC.allowedUpdateUnit
                        this.tagCodes = ProductTagEnum.getDescListByPage(saleSkcIdTagMap[representativeSKC.saleSkcId] ?: emptyMap()).distinct()

                        // 收集该颜色下所有SKC对应的所有SKU
                        val allSkusForThisColor = saleSkcIds.flatMap { skcId ->
                            skuMapBySkcId[skcId] ?: emptyList()
                        }

                        // 按尺码名称分组SKU
                        this.skuList = allSkusForThisColor.groupBy { it.sizeName }.map { (size, skus) ->
                            ProductLazadaSpecificDetailResp.ProductLazadaSkuResp().apply {
                                this.sizeName = size

                                if (skus.isNotEmpty()) {
                                    val sku = skus.first()
                                    this.sellerSku = sku.sellerSku
                                    this.barcode = sku.barcode
                                    this.barcodes = sku.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                    this.stockQuantity = sku.stockQuantity

                                    // 各站点价格信息
                                    this.countryPriceList = skus.map { countryPrice ->
                                        ProductLazadaSpecificDetailResp.ProductLazadaCountrySkuResp().apply {
                                            this.saleSkuId = countryPrice.saleSkuId
                                            this.platformProductId = countryPrice.platformProductId
                                            this.platformSkuId = countryPrice.platformSkuId
                                            this.country = countryPrice.country
                                            this.stockQuantity = countryPrice.stockQuantity
                                            this.sizeName = countryPrice.sizeName
                                            this.salePrice = countryPrice.salePrice
                                            this.retailPrice = countryPrice.retailPrice
                                            this.lastSalePrice = countryPrice.lastSalePrice
                                            this.lastRetailPrice = countryPrice.lastRetailPrice
                                            this.purchaseSalePrice = countryPrice.purchaseSalePrice
                                            this.purchaseRetailPrice = countryPrice.purchaseRetailPrice
                                            this.regularSalePrice = countryPrice.regularSalePrice
                                            this.regularRetailPrice = countryPrice.regularRetailPrice
                                            this.enable = countryPrice.enable
                                            this.publishState = countryPrice.publishState
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                this.imagePackageState = product.imagePackageState
                this.styleType = product.styleType
            }
        }
    }

    private fun detailBySingleCountry(req: ProductLazadaDetailReq): ProductLazadaDetailResp {
        if (req.saleGoodsId == null) {
            return ProductLazadaDetailResp()
        }
        // 获取商品基础信息
        val product = productRepository.getById(req.productId) ?: throw IllegalArgumentException("商品不存在")
        val saleGoods = saleGoodsRepository.getById(req.saleGoodsId) ?: throw IllegalArgumentException("销售商品不存在")

        // 获取sale_skc信息
        val saleSkcList = saleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!).filter { it.state == YES.code }
        // 获取sale_sku信息
        val saleSkus = saleSkuRepository.findBySaleSkcIds(saleSkcList.mapNotNull { it.saleSkcId }.toSet())

        // 获取商品属性
        val attributes = productAttributesRepository.listByProductId(product.productId!!, platformEnum.platformId)

        // 获取图片信息
        val imageCollection = getImages(product.spuCode!!)

        // 批量获取product标签
        val saleSkcIds = saleSkcList.mapNotNull { it.saleSkcId }
        val saleSkcIdTagMap = if (saleSkcIds.isNotEmpty()) {
            productTagRepository.getTagMapByLazadaSaleSkcIds(saleSkcIds)
        } else {
            mapOf()
        }

        // 构造返回结果
        return ProductLazadaDetailResp().apply {
            // 基础商品信息
            this.productId = product.productId
            this.shopId = req.shopId
            this.mainImgUrl = product.mainImgUrl
            this.spuCode = product.spuCode
            this.supplyMode = product.supplyMode
            this.spotTypeCode = product.spotTypeCode
            this.clothingStyleName = product.clothingStyleName
            this.planSourceName = product.planSourceName
            this.goodsRepType = product.goodsRepType
            this.categoryCode = product.categoryCode
            this.categoryName = product.categoryName
            this.inspiraSource = product.inspiraSource
            this.inspiraImgUrl = product.inspiraImgUrl
            this.selectStyleName = product.selectStyleName
            this.selectStyleTime = product.selectStyleTime
            this.buyerRemark = product.buyerRemark

            this.prototypeNum = product.prototypeNum
            this.goodsType = product.goodsType
            this.pricingType = product.pricingType
            this.spotType = product.spotType
            this.waves = product.waves

            this.creatorName = saleGoods.creatorName
            this.createdTime = saleGoods.createdTime
            this.update = saleGoods.updateState
            this.costPriceUpdateState = saleGoods.costPriceUpdateState
            this.firstPublishTime = saleGoods.publishTime
            this.firstPublishUserName = saleGoods.publishUserName
            this.latestPublishTime = saleGoods.latestPublishTime
            this.latestPublishUserId = saleGoods.latestPublishUserId
            this.latestPublishUserName = saleGoods.latestPublishUserName
            this.latestOfflineTime = saleGoods.latestOfflineTime
            this.latestOfflineUserName = saleGoods.latestOfflineUserName
            this.reviserName = saleGoods.reviserName
            this.revisedTime = saleGoods.revisedTime

            // 灵感来源信息
            if (product.inspiraSourceId != null) {
                try {
                    val data = inspirationClientExternal.getByInspirationOrPickingId(product.inspiraSourceId!!)
                    if (data?.inspirationInfo != null) {
                        this.inspiraSource = data.inspirationInfo?.inspirationImageSource
                        this.inspiraCountry = data.inspirationInfo?.countrySiteName
                        this.planSourceName = data.inspirationInfo?.planningSourceName
                        this.inspiraImgUrl = data.inspirationInfo?.inspirationImage
                    }
                    if (data?.pickingInfo != null) {
                        this.countryList = data.pickingInfo?.countrySiteName?.let { listOf(it) }
                        this.shopList = mutableListOf<ProductLazadaDetailShopResp>().apply {
                            this.add(ProductLazadaDetailShopResp().apply {
                                this.shopId = data.pickingInfo?.shopId
                                this.shopName = data.pickingInfo?.shopName
                            })
                        }.toList()
                        this.selectStyleTime = data.pickingInfo?.pickingTime
                        this.buyerRemark = data.pickingInfo?.remark
                    }
                } catch (e: Exception) {
                    log.error { "getByInspirationOrPickingId 获取灵感详情报错或者数据为空，Exception=${e.message}" }
                }
            }

            // Lazada详情
            this.lazadaDetail = ProductLazadaSpecificDetailResp().apply {
                // SPU信息
                this.saleGoodsId = saleGoods.saleGoodsId
                this.productTitle = saleGoods.productTitle
                this.productTitleList = mutableListOf<ProductTitleResp>().apply {
                    this.add(ProductTitleResp().apply {
                        this.country = saleGoods.country
                        this.countryName = CountryEnum.getByCode(saleGoods.country)?.desc
                        this.title = saleGoods.productTitle
                    })
                }
                this.brandId = saleGoods.brandId
                this.brandName = saleGoods.brandName
                this.stockTypeName = saleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.countryStockTypeName = saleGoods.countryStockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.delayDeliveryDays = saleGoods.delayDeliveryDays
                this.sizeGroupName = product.sizeGroupName
                this.sizeGroupCode = product.sizeGroupCode

                // 类目信息
                this.categoryId = product.categoryId
                this.categoryMappingId = getPlatformCategory(product, LAZADA.platformId)?.categoryMappingId
                this.platformName = LAZADA.platformName
                this.platformCategoryName = saleGoods.platformCategoryName

                // 包裹信息
                this.packageDimensionsLength = saleGoods.packageDimensionsLength
                this.packageDimensionsWidth = saleGoods.packageDimensionsWidth
                this.packageDimensionsHeight = saleGoods.packageDimensionsHeight
                this.packageWeight = saleGoods.packageWeight

                // 商品属性
                this.attributes = attributes.map {
                    PublishAttributePairResp().apply {
                        this.attributeId = it.attributeId
                        this.attributeValueId = it.attributeValueId
                    }
                }

                // 图片信息
                this.mainUrlList = imageCollection.limitedMainImages()
                this.detailImageList = imageCollection.detailImages

                // 将SKU按SKC ID分组，用于快速查找
                val skuMapBySkcId = saleSkus.groupBy { it.saleSkcId }

                // 重构SKC列表，以颜色为单位进行聚合
                this.skcList = saleSkcList.map { skc ->

                    ProductLazadaSpecificDetailResp.ProductLazadaSkcResp().apply {
                        this.saleSkcId = skc.saleSkcId
                        this.skc = skc.skc
                        this.color = skc.color
                        this.colorCode = skc.colorCode
                        this.platformColor = skc.platformColor
                        this.pictures = skc.pictures
                        this.cbPrice = skc.cbPrice
                        this.localPrice = skc.localPrice
                        this.purchasePrice = skc.purchasePrice
                        this.costPrice = skc.costPrice
                        this.combo = skc.combo
                        this.allowedUpdateUnit = skc.allowedUpdateUnit
                        this.tagCodes = ProductTagEnum.getDescListByPage(saleSkcIdTagMap[skc.saleSkcId] ?: emptyMap()).distinct()

                        // 按尺码名称分组SKU
                        this.skuList = skuMapBySkcId[skc.saleSkcId]?.map { sku ->
                            ProductLazadaSpecificDetailResp.ProductLazadaSkuResp().apply {
                                this.sizeName = sku.sizeName

                                this.sellerSku = sku.sellerSku
                                this.barcode = sku.barcode
                                this.barcodes = sku.barcodes?.parseJsonList(ComboBarcodeInfoDto::class.java)
                                this.stockQuantity = sku.stockQuantity

                                // 各站点价格信息
                                this.countryPriceList = listOf(
                                    ProductLazadaSpecificDetailResp.ProductLazadaCountrySkuResp().apply {
                                        this.saleSkuId = sku.saleSkuId
                                        this.platformProductId = sku.platformProductId
                                        this.platformSkuId = sku.platformSkuId
                                        this.country = sku.country
                                        this.stockQuantity = sku.stockQuantity
                                        this.sizeName = sku.sizeName
                                        this.salePrice = sku.salePrice
                                        this.retailPrice = sku.retailPrice
                                        this.lastSalePrice = sku.lastSalePrice
                                        this.lastRetailPrice = sku.lastRetailPrice
                                        this.purchaseSalePrice = sku.purchaseSalePrice
                                        this.purchaseRetailPrice = sku.purchaseRetailPrice
                                        this.regularSalePrice = sku.regularSalePrice
                                        this.regularRetailPrice = sku.regularRetailPrice
                                        this.enable = sku.enable
                                        this.publishState = sku.publishState
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    override fun page(req: ProductLazadaPageQueryReq): PageVo<ProductLazadaPageResp> {

        if (!req.country.isNullOrBlank()) {
            return pageBySingleCountry(req)
        }

        // 处理时间条件
        if (Objects.equals(YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }

        // 查询基础数据（按product_id+shop_id分组）
        val page = saleGoodsRepository.getLazadaPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val baseData = page.records

        if (CollectionUtils.isEmpty(baseData)) {
            return PageRespHelper.empty()
        }

        // 批量查询saleGoods数据
        val allSaleGoods = saleGoodsRepository.ktQuery()
            .`in`(SaleGoods::productId, baseData.map { it.productId!! })
            .list()

        val countrySaleGoods = if (!req.country.isNullOrBlank()) {
            allSaleGoods.filter { it.country == req.country }
        } else {
            allSaleGoods
        }

        // 按product_id和shop_id分组saleGoods
        val groupedSaleGoods = allSaleGoods.groupBy { Pair(it.productId!!, it.shopId) }
        val countryGroupedSaleGoods = countrySaleGoods.groupBy { Pair(it.productId!!, it.shopId) }

        // 批量查询SKU信息
        val productIds = baseData.mapNotNull { it.productId }.distinct()
        val saleGoodsIds = allSaleGoods.map { it.saleGoodsId!! }
        val allSaleSkus = saleSkuRepository.ktQuery()
            .`in`(SaleSku::saleGoodsId, saleGoodsIds)
            .list()

        val saleGoodsIdTagMap = if (saleGoodsIds.isNotEmpty()) {
            productTagRepository.getTagMapBySaleGoodsIds(saleGoodsIds)
        } else {
            mapOf()
        }

        val productIdTagMap = if (productIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(productIds)
        } else {
            mapOf()
        }

        // 按saleGoodsId分组SKU
        val skuBySaleGoodsId = allSaleSkus.groupBy { it.saleGoodsId!! }

        // 构建结果数据
        val result = baseData.map { baseItem ->
            // 获取该product+shop组合的所有saleGoods
            val productShopPair = Pair(baseItem.productId!!, baseItem.shopId)
            val saleGoodsList = groupedSaleGoods.getOrDefault(productShopPair, emptyList())
            val countrySaleGoodsList = countryGroupedSaleGoods.getOrDefault(productShopPair, emptyList())

            val saleSkcIdList = saleSkcRepository.findAllBySaleGoodsIds(countrySaleGoodsList.mapNotNull { it.saleGoodsId }).mapNotNull { it.saleSkcId }

            // 批量获取sale_skc标签
            val saleSkcIdTagMap = if (saleSkcIdList.isNotEmpty()) {
                productTagRepository.getTagMapByLazadaSaleSkcIds(saleSkcIdList)
            } else {
                mapOf()
            }
            val skcTagCodes: List<String> = saleSkcIdTagMap.values
                .flatMap { ProductTagEnum.getDescListByPage(it) }

            val saleGoodsTagCodes: List<String> = saleGoodsIdTagMap[baseItem.saleGoodsId]
                ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                ?: emptyList()

            val productIdTagCodes: List<String> = productIdTagMap[baseItem.productId]
                ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                ?: emptyList()

            val tagCodes = (skcTagCodes + saleGoodsTagCodes + productIdTagCodes).distinct()

            ProductLazadaPageResp().apply {
                // 基础信息
                this.productId = baseItem.productId
                this.productTitle = baseItem.productTitle
                this.mainImgUrl = baseItem.mainImgUrl
                this.spuCode = baseItem.spuCode
                this.supplyMode = baseItem.supplyMode
                this.waves = baseItem.waves
                this.channelId = baseItem.channelId
                this.platformId = baseItem.platformId
                this.shopId = baseItem.shopId
                this.shopName = baseItem.shopName
                this.categoryId = baseItem.categoryId
                this.categoryCode = baseItem.categoryCode
                this.categoryName = baseItem.categoryName
                this.update = countrySaleGoodsList.any { it.updateState == YES.code }
                this.costPriceUpdateState = countrySaleGoodsList.firstOrNull { it.costPriceUpdateState == YES.code }?.costPriceUpdateState
                this.priceException = baseItem.priceException
                this.tagCodes = tagCodes
                this.isError = baseItem.isError
                this.errorInfoDtoList = baseItem.errorInfoDtoList

                if (CollectionUtils.isNotEmpty(countrySaleGoodsList)) {
                    // 上下架状态：有一个为上架，则整体显示为上架状态
                    this.publishState = when {
                        countrySaleGoodsList.any { it.publishState == ProductPublishStateEnum.ACTIVE.code } ->
                            ProductPublishStateEnum.ACTIVE.code

                        countrySaleGoodsList.all { it.publishState == ProductPublishStateEnum.IN_ACTIVE.code } ->
                            ProductPublishStateEnum.IN_ACTIVE.code

                        countrySaleGoodsList.all { it.publishState == ProductPublishStateEnum.IN_ACTIVE.code } ->
                            ProductPublishStateEnum.DELETED.code

                        else -> ProductPublishStateEnum.WAIT.code
                    }
                    // 同步状态处理：按优先级 同步中 > 同步失败 > 同步成功 > 待同步
                    this.syncState = when {
                        // 优先展示同步中状态
                        countrySaleGoodsList.any { it.platformSyncState == PlatformSyncStateEnum.PROCESSING.code } ->
                            PlatformSyncStateEnum.PROCESSING.code
                        // 其次展示同步失败状态
                        countrySaleGoodsList.any { it.platformSyncState == PlatformSyncStateEnum.FAILURE.code } ->
                            PlatformSyncStateEnum.FAILURE.code
                        // 如果都同步成功，展示同步成功
                        countrySaleGoodsList.all { it.platformSyncState == PlatformSyncStateEnum.SUCCESS.code } ->
                            PlatformSyncStateEnum.SUCCESS.code
                        // 默认展示待同步状态
                        else -> PlatformSyncStateEnum.WAIT.code
                    }

                    // 上架状态站点
                    this.countryList = countrySaleGoodsList.filter { it.publishState == ProductPublishStateEnum.ACTIVE.code }
                        .mapNotNull { it.country }
                        .distinct()

                    // 前台链接
                    this.frontUrlList = mutableListOf<ProductFrontUrlResp>().apply {
                        // spu+shopId获取所有saleGoods(包括deleted)
                        val allSaleGoods = saleGoodsRepository.getAllAndDeletedBySpuCodeAndShopId(baseItem.spuCode!!, baseItem.shopId!!)
                            .filter { it.platformProductId != null }
                        if (allSaleGoods.isNotEmpty()) {
                            allSaleGoods.forEach { s ->
                                this.add(ProductFrontUrlResp().apply {
                                    this.country = s.country
                                    this.url = LazadaConstants.getFrontUrl(s.country!!, s.platformProductId.toString())
                                    this.platformProductId = s.platformProductId
                                    this.publishState = s.publishState
                                    this.createdTime = s.createdTime
                                })
                            }
                        }
                    }

                    // 计算首次上架时间等信息
                    val publishInfo = extractProductPublishInfo(countrySaleGoodsList)
                    this.firstPublishTime = publishInfo.firstPublishTime
                    this.publishUserName = publishInfo.publishUserName
                    this.latestPublishTime = publishInfo.latestPublishTime
                    this.latestPublishUserName = publishInfo.latestPublishUserName
                    this.latestOfflineTime = publishInfo.latestOfflineTime
                    this.latestOfflineUserName = publishInfo.latestOfflineUserName
                    this.revisedTime = publishInfo.latestRevisedTime
                    this.reviserName = publishInfo.latestRevisedUserName
                    saleGoodsList.filter { it.createdTime != null }
                        .maxByOrNull { it.createdTime!! }
                        ?.let { createdSaleGoods ->
                            this.createdTime = createdSaleGoods.createdTime
                            this.creatorName = createdSaleGoods.creatorName
                            this.creatorId = createdSaleGoods.creatorId
                        }

                    val saleGoods = countrySaleGoodsList.first()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.productTitle = baseItem.productTitle ?: saleGoods.productTitle
                    // 库存类型 货期
                    this.stockType = saleGoods.stockType
                    this.stockTypeName = saleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                    this.delayDeliveryDays = saleGoods.delayDeliveryDays

                    // 获取SKU数据统计
                    val allSkusForThisGroup = countrySaleGoodsList
                        .flatMap { sg -> skuBySaleGoodsId.getOrDefault(sg.saleGoodsId, emptyList()) }

                    // 最低销售价
                    allSkusForThisGroup
                        .filter { it.salePrice != null && it.salePrice!! > BigDecimal.ZERO }
                        .minByOrNull { it.salePrice!! }
                        ?.let { this.salePrice = it.salePrice?.toEngineeringString() }

                    // 总库存
                    this.stockQuantity = allSkusForThisGroup
                        .filter { it.enable == YES.code }
                        .sumOf { it.stockQuantity ?: 0L }

                    // 错误信息
                    /*val errorSkus = allSkusForThisGroup.filter { it.errorInfo != null && it.errorInfo.isNotBlank() }
                    this.isError = if (errorSkus.isNotEmpty()) 1 else 0
                    if (errorSkus.isNotEmpty()) {
                        // 收集错误信息
                        setErrorInfo(errorSkus.first().errorInfo)
                    }*/
                }
            }
        }

        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    private fun pageBySingleCountry(req: ProductLazadaPageQueryReq): PageVo<ProductLazadaPageResp> {

        if (req.country.isNullOrBlank()) {
            return PageRespHelper.empty()
        }
        // 处理时间条件
        if (Objects.equals(YES.code, req.today)) {
            req.createdTimeStart = LocalDateTime.now().with(LocalTime.MIN)
            req.createdTimeEnd = LocalDateTime.now().with(LocalTime.MAX)
        }

        // 1. 查询基础数据（按product_id+shop_id分组）
        val page = saleGoodsRepository.getLazadaPage(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        val baseData = page.records

        if (CollectionUtils.isEmpty(baseData)) {
            return PageRespHelper.empty()
        }

        val productIds = baseData.mapNotNull { it.productId }.distinct()
        val saleGoodsIds = baseData.mapNotNull { it.saleGoodsId }.distinct()

        // 批量查询saleGoods数据
        val allSaleGoods = saleGoodsRepository.listByIds(saleGoodsIds)
        // 按product_id和shop_id分组saleGoods
        val groupedSaleGoods = allSaleGoods.groupBy { Pair(it.productId!!, it.shopId) }

        // 批量获取sale商品详情
        val saleGoodsMap = allSaleGoods.associateBy { it.saleGoodsId }

        // 批量获取商品详情
        val products = productRepository.listByIds(productIds)
        val productMap = products.associateBy { it.productId }

        val saleGoodsIdTagMap = if (saleGoodsIds.isNotEmpty()) {
            productTagRepository.getTagMapByAeSaleGoodsIds(saleGoodsIds)
        } else {
            mapOf()
        }

        val productIdTagMap = if (productIds.isNotEmpty()) {
            productTagRepository.getTagMapByProductIds(productIds)
        } else {
            mapOf()
        }

        //  构建结果数据
        val result = baseData.map { baseItem ->
            ProductLazadaPageResp().apply {

                val product = productMap[baseItem.productId!!] ?: Product()
                val saleGoods = saleGoodsMap[baseItem.saleGoodsId!!] ?: SaleGoods()
                val saleSkuList = saleSkuRepository.getBySaleGoodsIds(listOf(baseItem.saleGoodsId!!))

                val saleSkcIdList = saleSkcRepository.findAllBySaleGoodsId(baseItem.saleGoodsId!!).mapNotNull { it.saleSkcId }

                // 批量获取sale_skc标签
                val saleSkcIdTagMap = if (saleSkcIdList.isNotEmpty()) {
                    productTagRepository.getTagMapByLazadaSaleSkcIds(saleSkcIdList)
                } else {
                    mapOf()
                }

                val skcTagCodes: List<String> = saleSkcIdTagMap.values
                    .flatMap { ProductTagEnum.getDescListByPage(it) }

                val saleGoodsTagCodes: List<String> = saleGoodsIdTagMap[baseItem.saleGoodsId]
                    ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                    ?: emptyList()

                val productIdTagCodes: List<String> = productIdTagMap[baseItem.productId]
                    ?.let { ProductTagEnum.getDescListByPage(it).toList() }
                    ?: emptyList()

                val tagCodes = (skcTagCodes + saleGoodsTagCodes + productIdTagCodes).distinct()

                // 基础信息
                this.productId = saleGoods.productId
                this.productTitle = saleGoods.productTitle
                this.mainImgUrl = product.mainImgUrl
                this.spuCode = saleGoods.spuCode
                this.supplyMode = product.supplyMode
                this.waves = product.waves
                this.shopId = saleGoods.shopId
                this.shopName = saleGoods.shopName
                this.categoryId = product.productId
                this.categoryCode = saleGoods.categoryCode
                this.categoryName = product.categoryName
                this.update = saleGoods.updateState == YES.code
                this.costPriceUpdateState = saleGoods.costPriceUpdateState
                this.priceException = product.priceException
                this.publishState = saleGoods.publishState
                this.syncState = saleGoods.platformSyncState
                this.country = saleGoods.country
                this.countryList = listOf(saleGoods.country!!)
                this.tagCodes = tagCodes
                this.frontUrlList = mutableListOf<ProductFrontUrlResp>().apply {
                    // spu+shopId获取所有saleGoods(包括deleted)
                    val allSaleGoods = saleGoodsRepository.getAllAndDeletedBySpuCodeAndShopIdAndCountry(saleGoods.spuCode!!, saleGoods.shopId!!, saleGoods.country!!)
                        .filter { it.platformProductId != null }
                    if (allSaleGoods.isNotEmpty()) {
                        allSaleGoods.forEach { s ->
                            this.add(ProductFrontUrlResp().apply {
                                this.country = s.country
                                this.url = LazadaConstants.getFrontUrl(s.country!!, s.platformProductId.toString())
                                this.platformProductId = s.platformProductId
                                this.publishState = s.publishState
                                this.createdTime = s.createdTime
                            })
                        }
                    }
                }
                this.firstPublishTime = saleGoods.publishTime
                this.publishUserName = saleGoods.publishUserName
                this.latestPublishTime = saleGoods.latestPublishTime
                this.latestPublishUserName = saleGoods.latestPublishUserName
                this.latestOfflineTime = saleGoods.latestOfflineTime
                this.latestOfflineUserName = saleGoods.latestOfflineUserName
                this.isError = saleGoods.errorState
                setErrorInfo(saleGoods.errorInfo)

                val productShopPair = Pair(baseItem.productId!!, baseItem.shopId)
                val saleGoodsList = groupedSaleGoods.getOrDefault(productShopPair, emptyList())
                // 从所有的站点中获取最早的创建时间和创建人
                saleGoodsList.filter { it.createdTime != null }
                    .maxByOrNull { it.createdTime!! }
                    ?.let { createdSaleGoods ->
                        this.createdTime = createdSaleGoods.createdTime
                        this.creatorName = createdSaleGoods.creatorName
                        this.creatorId = createdSaleGoods.creatorId
                    }
                this.revisedTime = saleGoods.revisedTime
                this.reviserName = saleGoods.reviserName
                this.reviserId = saleGoods.reviserId
                this.saleGoodsId = saleGoods.saleGoodsId
                this.productTitle = saleGoods.productTitle ?: product.productTitle
                // 库存类型 货期
                this.stockType = saleGoods.stockType
                this.stockTypeName = saleGoods.stockType?.let { StockTypeEnum.getByCode(it) }?.desc
                this.delayDeliveryDays = saleGoods.delayDeliveryDays
                this.salePrice = saleSkuList.filter { it.salePrice != null && it.salePrice!! > BigDecimal.ZERO }.minByOrNull { it.salePrice!! }?.let { it.salePrice?.toEngineeringString() }
                this.stockQuantity = saleSkuList
                    .filter { it.enable == YES.code }
                    .sumOf { it.stockQuantity ?: 0L }

                this.imagePackageState = product.imagePackageState
                this.styleType = product.styleType
            }
        }

        return PageRespHelper.of(page.current.toInt(), page.total, result)
    }

    /**
     * 更新Lazada商品
     * @param req
     */
    override fun update(req: ProductLazadaPublishedUpdateReq) {

        req.skcList?.forEach { skc ->
            if (skc.combo == YES.code) {
                if (skc.skuList.isNullOrEmpty()) {
                    throw IllegalArgumentException("组合商品必须包含SKU信息")
                }
                skc.skuList?.forEach { skuInfo ->
                    if (skuInfo.barcodes.isNullOrEmpty()) {
                        throw IllegalArgumentException("组合商品的SKU必须包含条码")
                    }
                    skuInfo.barcodes?.forEach { barcodeInfo ->
                        if (barcodeInfo.barcode.isNullOrBlank()) {
                            throw IllegalArgumentException("组合商品的SKU条码不能为空")
                        }
                        if (barcodeInfo.unit == null) {
                            throw IllegalArgumentException("件数不能为空")
                        }
                    }
                }
            }
        }

        // 验证商品存在性
        val product = productRepository.getById(req.productId!!) ?: throw BusinessException("商品不存在, productId=${req.productId}")
        val currentUser = CurrentUserHolder.get()
        log.info { "开始更新已上架Lazada商品, productId: ${req.productId}" }
        // 更新商品图库
        savePicture(product)

        // 3. 更新商品属性（如果传入了属性）
        req.attributes?.takeIf { it.isNotEmpty() }?.let { attrs ->
            // 先删除当前平台的属性，再批量保存
            productAttributesRepository.removeByProductIdAndPlatformId(product.productId!!, LAZADA.platformId)
            val attrEntities = attrs.map { attr ->
                ProductAttributes().apply {
                    attributeId = attr.attributeId
                    attributeValueId = attr.attributeValueId
                    productId = product.productId
                    categoryId = product.categoryId
                    platformId = LAZADA.platformId
                }
            }
            productAttributesRepository.saveBatch(attrEntities)
            log.info { "更新商品属性, productId: ${product.productId}" }
        }

        // 注意: 因为有全站点的更新, 因此saleGoodsId不为空, 则单站点更新; 若saleGoodsId为空, 则全站点更新
        val saleGoodsList: List<SaleGoods>
        // 是否全站点
        var isAllCountry = false
        if (req.saleGoodsId != null) {
            // 单站点更新
            saleGoodsList = saleGoodsRepository.listByIds(listOf(req.saleGoodsId!!))
        } else {
            // 全站点更新
            saleGoodsList = saleGoodsRepository.listByProductIdAndShopId(req.productId!!, req.shopId!!)
            isAllCountry = true
        }
        if (saleGoodsList.isEmpty()) {
            return
        }

        // 更新SaleGoods信息
        updateSaleGoodsList(req, saleGoodsList)
        // 更新SaleSkc和SaleSku信息
        saleGoodsList.forEach { saleGoods ->
            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->
                try {

                    // 状态-同步中
                    saleGoods.platformSyncState = PlatformSyncStateEnum.PROCESSING.code
                    saleGoodsRepository.updateById(saleGoods)

                    // 需要enable=0的sku
                    val noEnableSkuList = mutableListOf<SaleSku>()

                    // 更新销售SKC和SKU数据（仅针对已上架数据）
                    if (isAllCountry) {
                        // 全站点更新
                        req.skcList
                            ?.filter { it.combo != YES.code } // 全站点不处理组合商品
                            ?.forEach { skc ->
                                val noEnableSkuTmpList = updateSaleSkcAndSkuAllCountry(saleGoods, product, skc)
                                if (CollectionUtils.isNotEmpty(noEnableSkuTmpList)) {
                                    noEnableSkuList.addAll(noEnableSkuTmpList)
                                }
                            }
                    } else {
                        // 单站点更新
                        req.skcList
                            ?.forEach { skc ->
                                val noEnableSkuTmpList = updateSaleSkcAndSkuSingleCountry(saleGoods, product, skc)
                                if (CollectionUtils.isNotEmpty(noEnableSkuTmpList)) {
                                    noEnableSkuList.addAll(noEnableSkuTmpList)
                                }
                            }
                    }

                    // 绑定Lazada条码
                    callLazadaComponent.bindBarcode(product, saleGoods)

                    // 更新商品上架时间，上架人
                    saleGoodsRepository.updateById(SaleGoods().apply {
                        this.saleGoodsId = saleGoods.saleGoodsId
                        this.latestPublishTime = LocalDateTime.now()
                        this.latestPublishUserId = currentUser.id
                        this.latestPublishUserName = currentUser.name
                    })

                    // 更新Lazada: 调用updateProduct更新商品状态
                    lazadaUpdateProductComponent.updateProduct(
                        saleGoods.shopId!!,
                        saleGoodsRepository.getById(saleGoods.saleGoodsId)
                    )

                    try {
                        // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
                        callLazadaComponent.setBarcodeImage(saleGoods.productId!!, saleGoods.shopId!!, saleGoods.country!!)
                    } catch (e: Exception) {
                        log.error(e) { "修复barcode关系表的SKC图片异常: ${e.message}" }
                    }

                    // 更新需要enable=0的数据
                    if (noEnableSkuList.isNotEmpty()) {
                        val finalDisableList = noEnableSkuList.map { sku ->
                            SaleSku().apply {
                                saleSkuId = sku.saleSkuId
                                enable = NO.code
                            }
                        }
                        saleSkuRepository.updateBatchById(finalDisableList)
                        log.info { "批量禁用SKU完成, spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}, 数量: ${finalDisableList.size}" }
                    }

                    // 检查sku上架状态: 有一个上架即SPU上架, 没有一个上架即SPU下架
                    val newSkuList = saleSkuRepository.getBySaleGoodsId(saleGoods.saleGoodsId!!)
                    if (newSkuList.isEmpty()) {
                        saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                    } else {
                        val activeSku = newSkuList.firstOrNull { it.publishState == ProductPublishStateEnum.ACTIVE.code }
                        if (activeSku != null) {
                            saleGoods.publishState = ProductPublishStateEnum.ACTIVE.code
                        } else {
                            saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                        }
                    }

                    // saleGoods更新字段改为0
                    saleGoods.updateState = NO.code
                    saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                    saleGoodsRepository.updateById(saleGoods)
                    null
                } catch (e: Exception) {
                    log.error(e) { "事务执行过程中出现异常，触发回滚，spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}，error: ${e.message}" }

                    if (e !is PublishGlobalBizException) {
                        runAsync(asyncExecutor) {
                            productSyncLogRepository.addErrorSyncLog(
                                product, LAZADA.platformName, saleGoods.shopName, e.getRootMessage(),
                                PlatformOperatorTypeEnum.ACTIVE.code, saleGoods.country
                            )
                        }
                    }

                    // 手动标记事务回滚
                    status.setRollbackOnly()

                    // 状态-同步失败
                    saleGoods.platformSyncState = PlatformSyncStateEnum.FAILURE.code
                    saleGoodsRepository.updateById(saleGoods)

                    null
                }
            }
        }
    }

    /**
     * 更新skc和sku-单站点(包含组合商品)
     * sale_skc_id必定有, 组合商品可能没有, 没有则新增, 有则更新
     * sale_sku_id, 没有则新增, 有则更新 (req只传enable=1的数据, 比较本地不存在req的都是enable=0, 但是更新Lazada逻辑只筛选enable=1的sku, 所以要先更新Lazada,再把本地需要禁用enable=0)
     */
    private fun updateSaleSkcAndSkuSingleCountry(saleGoods: SaleGoods, product: Product, skcReq: ProductLazadaPublishedLazadaSkcInfoReq): List<SaleSku> {
        var saleSkc: SaleSkc? = null
        if (skcReq.saleSkcId == null) {
            // 是否组合商品
            if (skcReq.combo == YES.code) {

                if (skcReq.colorCode.isNullOrBlank()) {
                    throw IllegalArgumentException("组合商品必须有颜色")
                }

                // 提取sku下的所有barcode
                val barcodeList = skcReq.skuList?.mapNotNull { it.barcodes }?.flatten()?.mapNotNull { it.barcode }?.distinct()
                require(!(barcodeList.isNullOrEmpty())) { "组合商品必须有条码" }
                val barcodeInfoList = productBarCodeRepository.getListByBarcodes(barcodeList)
                val skcCodeList = barcodeInfoList?.mapNotNull { it.skc }?.distinct()
                val productSkcList = productSkcRepository.listBySkc(skcCodeList!!)
                val comboColor = productSkcList.mapNotNull { it.color?.trim() }.distinct().sorted().joinToString("+")

                // 组合商品, 创建SKC
                saleSkc = SaleSkc().apply {
                    this.saleSkcId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.color = comboColor
                    this.colorCode = skcReq.colorCode?.trim()
                    this.platformColor = skcReq.platformColor
                    this.combo = YES.code
                    this.state = YES.code
                }
                saleSkcRepository.save(saleSkc)
            }
        } else {
            saleSkc = saleSkcRepository.getById(skcReq.saleSkcId!!)
            saleSkc.platformColor = skcReq.platformColor
            saleSkcRepository.updateById(saleSkc)
        }

        if (saleSkc == null) {
            throw IllegalArgumentException("SKC不存在")
        }

        // 计算价格
        val autoCulPriceList = productPriceManagementService.autoCalPrice(AutoCalPriceReq().apply {
            this.productId = saleGoods.productId!!
            this.shopId = saleGoods.shopId
        })
        val priceMap = mutableMapOf<String, AutoCalCountryPriceResp>()
        autoCulPriceList.forEach { priceSkc ->
            priceSkc.countryPriceList.forEach {
                priceMap[priceSkc.productSkcId.toString() + it.country!!] = it
            }
        }
        // 获取对应的计算价格信息
        val autoPrice = priceMap[saleSkc.productSkcId.toString() + saleGoods.country]

        // 需要enable=1的sku
        val noEnableSkuList = mutableListOf<SaleSku>()

        val saveSaleSkuList = mutableListOf<SaleSku>()
        val updateSaleSkuList = mutableListOf<SaleSku>()

        // 更新SKU信息
        skcReq.skuList?.forEach { skuReq ->

            // 判断尺码是否有条码
            val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(saleGoods.spuCode, saleSkc.skc, skuReq.sizeName!!)
            var newBarcode: String? = null
            if (barCode == null && skcReq.combo != YES.code) {
                // 添加条码
                val barcodeReq = BatchCreateBarCodeReq().apply {
                    this.categoryCode = product.categoryCode
                    this.categoryName = product.categoryName
                    this.skcCode = saleSkc.skc
                    this.color = saleSkc.color
                    this.spuCode = product.spuCode
                    this.groupName = product.sizeGroupName
                    this.sourceGroupCode = product.sizeGroupCode
                    this.sizeValues = listOf(skuReq.sizeName!!)
                    this.inspiraImgUrl = product.inspiraImgUrl
                    this.supplyMode = product.supplyMode
                    this.localPrice = saleSkc.localPrice
                    this.designImgUrl = saleSkc.pictures
                    this.mainImgUrl = saleSkc.pictures
                }
                val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                if (CollectionUtils.isNotEmpty(barcodeResp)) {
                    newBarcode = barcodeResp[0].barcode
                }
            } else {
                newBarcode = barCode?.barcode
            }

            val saleSku = skuReq.saleSkuId?.let { saleSkuRepository.getById(it) }
            if (saleSku == null) {
                // 新增
                val newSaleSku = SaleSku().apply {
                    this.saleSkuId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.saleSkcId = saleSkc.saleSkcId
                    this.productId = saleGoods.productId
                    this.productSkcId = saleSkc.productSkcId
                    this.country = skuReq.country
                    this.stockQuantity = skuReq.stockQuantity
                    this.sizeName = skuReq.sizeName
                    this.shopName = saleGoods.shopName
                    this.brandId = saleGoods.brandId
                    this.brandName = saleGoods.brandName
                    this.salePrice = skuReq.salePrice ?: autoPrice?.salePrice
                    this.retailPrice = skuReq.retailPrice ?: autoPrice?.retailPrice
                    this.lastSalePrice = skuReq.lastSalePrice
                    this.lastRetailPrice = skuReq.lastRetailPrice
                    this.purchaseSalePrice = skuReq.purchaseSalePrice
                    this.purchaseRetailPrice = skuReq.purchaseRetailPrice
                    this.regularSalePrice = skuReq.regularSalePrice
                    this.regularRetailPrice = skuReq.regularRetailPrice
                    this.platformCategoryId = saleGoods.platformCategoryId
                    this.platformCategoryName = saleGoods.platformCategoryName
                    this.delayDeliveryDays = saleGoods.delayDeliveryDays
                    this.barcode = newBarcode
                    if (skcReq.combo == YES.code) {
                        if (skuReq.barcodes.isNullOrEmpty()) {
                            throw IllegalArgumentException("组合商品 ${skuReq.sizeName} 必须有条码")
                        }
                        this.barcodes = skuReq.barcodes?.toJson()
                    }
                    this.enable = YES.code
                    this.publishState = if (skuReq.flagFrontend == YES.code) {
                        ProductPublishStateEnum.ACTIVE.code
                    } else {
                        ProductPublishStateEnum.IN_ACTIVE.code
                    }
                    this.sellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, this)
                }
                saveSaleSkuList.add(newSaleSku)
            } else {
                // 更新
                saleSku.salePrice = skuReq.salePrice ?: autoPrice?.salePrice
                saleSku.retailPrice = skuReq.retailPrice ?: autoPrice?.retailPrice
                saleSku.lastSalePrice = skuReq.lastSalePrice
                saleSku.lastRetailPrice = skuReq.lastRetailPrice
                saleSku.purchaseSalePrice = skuReq.purchaseSalePrice
                saleSku.purchaseRetailPrice = skuReq.purchaseRetailPrice
                saleSku.regularSalePrice = skuReq.regularSalePrice
                saleSku.regularRetailPrice = skuReq.regularRetailPrice
                saleSku.stockQuantity = skuReq.stockQuantity
                saleSku.publishState = if (skuReq.flagFrontend == YES.code) {
                    ProductPublishStateEnum.ACTIVE.code
                } else {
                    ProductPublishStateEnum.IN_ACTIVE.code
                }
                saleSku.enable = YES.code
                if (skcReq.combo == YES.code) {
                    saleSku.barcodes = skuReq.barcodes?.toJson()
                }
                updateSaleSkuList.add(saleSku)
            }
        }
        if (CollectionUtils.isNotEmpty(saveSaleSkuList)) {
            saleSkuRepository.saveBatch(saveSaleSkuList)
        }
        if (CollectionUtils.isNotEmpty(updateSaleSkuList)) {
            saleSkuRepository.updateBatchById(updateSaleSkuList)
        }

        // 不在req的sku, 标记为enable=0
        val existSkuList = saleSkuRepository.list(
            KtQueryWrapper(SaleSku::class.java)
                .eq(SaleSku::saleGoodsId, saleGoods.saleGoodsId)
                .eq(SaleSku::saleSkcId, saleSkc.saleSkcId)
                .eq(SaleSku::enable, YES.code)
        )
        val saveSaleSkuId = saveSaleSkuList.map { it.saleSkuId }
        existSkuList
            .filter { existSku -> !saveSaleSkuId.contains(existSku.saleSkuId) }
            .forEach { existSku ->
                // 是否存在于req
                var exist = false
                skcReq.skuList
                    ?.filter { skuReq -> skuReq.saleSkuId != null }
                    ?.forEach { skuReq ->
                        if (Objects.equals(skuReq.saleSkuId, existSku.saleSkuId)
                            && Objects.equals(skuReq.country, existSku.country)
                            && Objects.equals(skuReq.sizeName, existSku.sizeName)
                            && existSku.enable == YES.code
                        ) {
                            exist = true
                            return@forEach
                        }
                    }
                if (!exist) {
                    // 不存在的标记 改为 删除
                    existSku.publishState = ProductPublishStateEnum.DELETED.code
                    noEnableSkuList.add(existSku)
                }
            }
        if (noEnableSkuList.isNotEmpty()) {
            saleSkuRepository.updateBatchById(noEnableSkuList)
            noEnableSkuList.addAll(noEnableSkuList)
        }
        return noEnableSkuList
    }

    /**
     * 更新skc和sku-全站点(不含组合商品)
     * sale_skc_id 不能使用
     * sale_sku_id, 没有则新增, 有则更新 (req只传enable=1的数据, 比较本地不存在req的都是enable=0, 但是更新Lazada逻辑只筛选enable=1的sku, 所以要先更新Lazada,再把本地需要禁用enable=0)
     */
    private fun updateSaleSkcAndSkuAllCountry(saleGoods: SaleGoods, product: Product, skcReq: ProductLazadaPublishedLazadaSkcInfoReq): List<SaleSku> {
        val saleSkc: SaleSkc?
        if (skcReq.saleSkcId != null) {
            saleSkc = saleSkcRepository.getOne(
                KtQueryWrapper(SaleSkc::class.java)
                    .eq(SaleSkc::saleGoodsId, saleGoods.saleGoodsId)
                    .eq(SaleSkc::skc, skcReq.skc)
            )
            saleSkc.platformColor = skcReq.platformColor
            saleSkcRepository.updateById(saleSkc)
        } else {
            // 不存在则创建
            val productSkc = productSkcRepository.getByProductIdAndSkc(saleGoods.productId!!, skcReq.skc!!)
            saleSkc = SaleSkc().apply {
                this.saleSkcId = IdHelper.getId()
                this.saleGoodsId = saleGoods.saleGoodsId
                this.productSkcId = productSkc?.productSkcId
                this.skc = productSkc?.skc
                this.color = productSkc?.color
                this.platformColor = skcReq.platformColor
                this.colorCode = productSkc?.colorCode
                this.colorAbbrCode = productSkc?.colorAbbrCode
                this.pictures = productSkc?.pictures
                this.state = YES.code
                this.cbPrice = productSkc?.cbPrice
                this.localPrice = productSkc?.localPrice
                this.purchasePrice = productSkc?.purchasePrice
                this.costPrice = productSkc?.costPrice
            }
            saleSkcRepository.save(saleSkc)
        }

        if (saleSkc == null) {
            throw IllegalArgumentException("SKC不存在")
        }

        // 计算价格
        val autoCulPriceList = productPriceManagementService.autoCalPrice(AutoCalPriceReq().apply {
            this.productId = saleGoods.productId!!
            this.shopId = saleGoods.shopId
        })
        val priceMap = mutableMapOf<String, AutoCalCountryPriceResp>()
        autoCulPriceList.forEach { priceSkc ->
            priceSkc.countryPriceList.forEach {
                priceMap[priceSkc.productSkcId.toString() + it.country!!] = it
            }
        }
        // 获取对应的计算价格信息
        val autoPrice = priceMap[saleSkc.productSkcId.toString() + saleGoods.country]

        // 需要enable=1的sku
        val noEnableSkuList = mutableListOf<SaleSku>()

        val saveSaleSkuList = mutableListOf<SaleSku>()
        val updateSaleSkuList = mutableListOf<SaleSku>()

        // 更新SKU信息
        skcReq.skuList
            ?.filter { skuReq -> skuReq.country == saleGoods.country }
            ?.forEach { skuReq ->

                // 判断尺码是否有条码
                val barCode = productBarCodeRepository.getBySpuCodeAndSkcAndSize(saleGoods.spuCode, saleSkc.skc, skuReq.sizeName!!)
                var newBarcode: String? = null
                if (barCode == null && skcReq.combo != YES.code) {
                    // 添加条码
                    val barcodeReq = BatchCreateBarCodeReq().apply {
                        this.categoryCode = product.categoryCode
                        this.categoryName = product.categoryName
                        this.skcCode = saleSkc.skc
                        this.color = saleSkc.color
                        this.spuCode = product.spuCode
                        this.groupName = product.sizeGroupName
                        this.sourceGroupCode = product.sizeGroupCode
                        this.sizeValues = listOf(skuReq.sizeName!!)
                        this.inspiraImgUrl = product.inspiraImgUrl
                        this.supplyMode = product.supplyMode
                        this.localPrice = saleSkc.localPrice
                        this.designImgUrl = saleSkc.pictures
                        this.mainImgUrl = saleSkc.pictures
                    }
                    val barcodeResp = barCodeService.createBarcodeByForce(listOf(barcodeReq))
                    if (CollectionUtils.isNotEmpty(barcodeResp)) {
                        newBarcode = barcodeResp[0].barcode
                    }
                } else {
                    newBarcode = barCode?.barcode
                }

                val saleSku = skuReq.saleSkuId?.let { saleSkuRepository.getById(it) }
                if (saleSku == null) {
                    // 新增
                    val newSaleSku = SaleSku().apply {
                        this.saleSkuId = IdHelper.getId()
                        this.saleGoodsId = saleGoods.saleGoodsId
                        this.saleSkcId = saleSkc.saleSkcId
                        this.productId = saleGoods.productId
                        this.productSkcId = saleSkc.productSkcId
                        this.country = skuReq.country
                        this.stockQuantity = skuReq.stockQuantity
                        this.sizeName = skuReq.sizeName
                        this.shopName = saleGoods.shopName
                        this.brandId = saleGoods.brandId
                        this.brandName = saleGoods.brandName
                        this.salePrice = skuReq.salePrice ?: autoPrice?.salePrice
                        this.retailPrice = skuReq.retailPrice ?: autoPrice?.retailPrice
                        this.lastSalePrice = skuReq.lastSalePrice
                        this.lastRetailPrice = skuReq.lastRetailPrice
                        this.purchaseSalePrice = skuReq.purchaseSalePrice
                        this.purchaseRetailPrice = skuReq.purchaseRetailPrice
                        this.regularSalePrice = skuReq.regularSalePrice
                        this.regularRetailPrice = skuReq.regularRetailPrice
                        this.platformCategoryId = saleGoods.platformCategoryId
                        this.platformCategoryName = saleGoods.platformCategoryName
                        this.delayDeliveryDays = saleGoods.delayDeliveryDays
                        this.barcode = newBarcode
                        this.enable = YES.code
                        this.publishState = if (skuReq.flagFrontend == YES.code) {
                            ProductPublishStateEnum.ACTIVE.code
                        } else {
                            ProductPublishStateEnum.IN_ACTIVE.code
                        }
                        this.sellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, this)
                    }
                    saveSaleSkuList.add(newSaleSku)
                } else {
                    // 更新
                    saleSku.salePrice = skuReq.salePrice ?: autoPrice?.salePrice
                    saleSku.retailPrice = skuReq.retailPrice ?: autoPrice?.retailPrice
                    saleSku.lastSalePrice = skuReq.lastSalePrice
                    saleSku.lastRetailPrice = skuReq.lastRetailPrice
                    saleSku.purchaseSalePrice = skuReq.purchaseSalePrice
                    saleSku.purchaseRetailPrice = skuReq.purchaseRetailPrice
                    saleSku.regularSalePrice = skuReq.regularSalePrice
                    saleSku.regularRetailPrice = skuReq.regularRetailPrice
                    saleSku.stockQuantity = skuReq.stockQuantity
                    saleSku.publishState = if (skuReq.flagFrontend == YES.code) {
                        ProductPublishStateEnum.ACTIVE.code
                    } else {
                        ProductPublishStateEnum.IN_ACTIVE.code
                    }
                    saleSku.enable = YES.code
                    updateSaleSkuList.add(saleSku)
                }
            }
        if (CollectionUtils.isNotEmpty(saveSaleSkuList)) {
            saleSkuRepository.saveBatch(saveSaleSkuList)
        }
        if (CollectionUtils.isNotEmpty(updateSaleSkuList)) {
            saleSkuRepository.updateBatchById(updateSaleSkuList)
        }

        // 不在req的sku, 标记为enable=0
        val existSkuList = saleSkuRepository.list(
            KtQueryWrapper(SaleSku::class.java)
                .eq(SaleSku::saleGoodsId, saleGoods.saleGoodsId)
                .eq(SaleSku::saleSkcId, saleSkc.saleSkcId)
                .eq(SaleSku::enable, YES.code)
        )
        val saveSaleSkuId = saveSaleSkuList.map { it.saleSkuId }
        existSkuList
            .filter { existSku -> !saveSaleSkuId.contains(existSku.saleSkuId) }
            .forEach { existSku ->
                // 是否存在于req
                var exist = false
                skcReq.skuList
                    ?.filter { skuReq -> skuReq.country == saleGoods.country }
                    ?.filter { skuReq -> skuReq.saleSkuId != null }
                    ?.forEach { skuReq ->
                        if (Objects.equals(skuReq.saleSkuId, existSku.saleSkuId)
                            && Objects.equals(skuReq.country, existSku.country)
                            && Objects.equals(skuReq.sizeName, existSku.sizeName)
                            && existSku.enable == YES.code
                        ) {
                            exist = true
                            return@forEach
                        }
                    }
                if (!exist) {
                    // 不存在的标记 改为 删除
                    existSku.publishState = ProductPublishStateEnum.DELETED.code
                    noEnableSkuList.add(existSku)
                }
            }
        if (noEnableSkuList.isNotEmpty()) {
            saleSkuRepository.updateBatchById(noEnableSkuList)
            noEnableSkuList.addAll(noEnableSkuList)
        }
        return noEnableSkuList
    }

    /**
     * 应用更新
     *
     * @param req
     */
    override fun applyUpdateProduct(req: ApplyUpdateProductReq) {
        val saleGoodsList = saleGoodsRepository.list(
            KtQueryWrapper(SaleGoods::class.java)
                .eq(SaleGoods::productId, req.productId)
                .eq(SaleGoods::shopId, req.shopId)
        )
        if (saleGoodsList.isNullOrEmpty()) {
            throw IllegalArgumentException("销售商品不存在")
        }

        val productSourceData = productSourceDataRepository.list(
            KtQueryWrapper(ProductSourceData::class.java)
                .eq(ProductSourceData::productId, req.productId)
                .eq(ProductSourceData::shopId, req.shopId)
                .eq(ProductSourceData::processState, NO.code)
        )
        if (CollectionUtils.isEmpty(productSourceData)) {
            throw BaseBizException("暂无更新数据")
        }
        val platformEnum = PlatformEnum.LAZADA
        val updateHandler = productUpdateServiceSelector.getSaleUpdateHandler(platformEnum)
        if (updateHandler == null) {
            throw BaseBizException("应用更新不支持平台 ${platformEnum.platformName}")
        }
        val saleGoodsIds = saleGoodsList.mapNotNull { it.saleGoodsId }
        for (sourceData in productSourceData) {
            when (sourceData.dataType) {
                ProductUpdateTypeEnum.ADD_SKC.code -> {
                    val data = sourceData.sourceData?.parseJson(CreateProductDto::class.java)
                        ?: throw BusinessException("源数据为空")
                    updateHandler.addSkc(data, saleGoodsIds)
                }

                ProductUpdateTypeEnum.UPDATE_PRICE.code,
                ProductUpdateTypeEnum.CANCLE.code,
                ProductUpdateTypeEnum.UPDATE_CATEGORY.code,
                ProductUpdateTypeEnum.UPDATE_COLOR.code,
                ProductUpdateTypeEnum.UPDATE_PRICING_TYPE.code,
                ProductUpdateTypeEnum.UPDATE_PURCHASES_PRICE.code,
                ProductUpdateTypeEnum.UPDATE_SIZE.code,
                ProductUpdateTypeEnum.UPDATE_CLOTHING_STYLE.code,
                    -> {
                    val data = sourceData.sourceData?.parseJson(ProductUpdateDto.Data::class.java)
                        ?: throw BusinessException("源数据为空")

                    when (sourceData.dataType) {
                        ProductUpdateTypeEnum.UPDATE_PRICE.code -> {
                            // sourceData.createdTime <= 2025年6月1日 00:00:00, 则执行以下逻辑
                            if (sourceData.createdTime!!.isBefore(costPriceUpdateHistoryTime)) {
                                updateHandler.updatePrice(data, saleGoodsIds)
                            }
                        }

                        ProductUpdateTypeEnum.CANCLE.code -> {
                            updateHandler.cancel(data, saleGoodsIds)
                        }

                        ProductUpdateTypeEnum.UPDATE_CATEGORY.code -> {
                            updateHandler.updateCategory(data, saleGoodsIds)
                        }

                        ProductUpdateTypeEnum.UPDATE_COLOR.code -> {
                            updateHandler.updateColor(data, saleGoodsIds)
                        }

                        ProductUpdateTypeEnum.UPDATE_PRICING_TYPE.code -> {
                            updateHandler.updatePricingType(data, saleGoodsIds)
                        }

                        ProductUpdateTypeEnum.UPDATE_PURCHASES_PRICE.code -> {
                            // sourceData.createdTime <= 2025年6月1日 00:00:00, 则执行以下逻辑
                            if (sourceData.createdTime!!.isBefore(costPriceUpdateHistoryTime)) {
                                updateHandler.updatePurchasesPrice(data, saleGoodsIds)
                            }
                        }

                        ProductUpdateTypeEnum.UPDATE_SIZE.code -> {
                            updateHandler.updateSize(data, saleGoodsIds)
                        }

                        ProductUpdateTypeEnum.UPDATE_CLOTHING_STYLE.code -> {
                            updateHandler.updateClothingStyle(data, saleGoodsIds)
                        }
                    }
                }

                else -> {
                    throw BusinessException("不支持的更新类型: ${sourceData.dataType}")
                }
            }

            sourceData.processState = YES.code
            productSourceDataRepository.updateById(sourceData)
        }

        val currentUser = CurrentUserHolder.get()
        saleGoodsList.forEach { saleGoods ->

            Thread.sleep(1000)

            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->
                try {

                    val product = productRepository.getById(saleGoods.productId!!)

                    // 绑定Lazada条码
                    callLazadaComponent.bindBarcode(product, saleGoods)

                    /*
                    应用更新只更改本地, 不会更改Lazada, 等用户点保存并更新按钮才同步到Lazada
                     */
                    // 更新商品上架时间，上架人
                    saleGoodsRepository.updateById(SaleGoods().apply {
                        this.saleGoodsId = saleGoods.saleGoodsId
//                        this.latestPublishTime = LocalDateTime.now()
//                        this.latestPublishUserId = currentUser.id
//                        this.latestPublishUserName = currentUser.name
                        this.updateState = NO.code
                        this.platformSyncState = PlatformSyncStateEnum.WAIT.code
                    })

                } catch (e: Exception) {
                    // 记录异常日志
                    e.printStackTrace()
                    log.error(e) { "事务执行过程中出现异常，触发回滚，spuCode: ${saleGoods.spuCode}, saleGoodsId: ${saleGoods.saleGoodsId}，error: ${e.message}" }
                    // 手动标记事务回滚
                    status.setRollbackOnly()
                    null
                }
            }
        }
    }

    /**
     * 弹窗查询-编辑库存/价格
     *
     * @param req
     */
    override fun querySkuByUpdateStockPrice(req: QuerySkuByUpdateStockPriceReq): List<QuerySkuByUpdateStockPriceResp> {
        // 提取sale_goods
        var saleGoodsList: List<SaleGoods> = emptyList()
        if (req.saleGoodsId != null) {
            // 单站点
            val saleGoods = saleGoodsRepository.getById(req.saleGoodsId)
            if (saleGoods != null) {
                saleGoodsList = listOf(saleGoods)
            }
        } else {
            // 全站点
            val getSaleGoodsList = saleGoodsRepository.list(
                KtQueryWrapper(SaleGoods::class.java)
                    .eq(SaleGoods::productId, req.productId)
                    .eq(SaleGoods::shopId, req.shopId)
            )
            if (CollectionUtils.isNotEmpty(getSaleGoodsList)) {
                saleGoodsList = getSaleGoodsList
            }
        }
        if (CollectionUtils.isEmpty(saleGoodsList)) {
            throw IllegalArgumentException("销售商品不存在")
        }

        val saleSkuList = saleSkuRepository.querySkuByUpdateStockPrice(saleGoodsList.map { it.saleGoodsId!! })
        if (CollectionUtils.isEmpty(saleSkuList)) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        return saleSkuList.map {
            QuerySkuByUpdateStockPriceResp().apply {
                this.productSkcId = it.productSkcId
                this.skc = it.skc
                this.country = it.country
                this.color = it.color
                this.combo = it.combo
                this.barcode = it.barcode
                this.saleSkuId = it.saleSkuId
                this.sizeName = it.sizeName
                this.quantity = it.stockQuantity
                this.salePrice = it.salePrice
                this.retailPrice = it.retailPrice
                this.lastSalePrice = it.lastSalePrice
                this.lastRetailPrice = it.lastRetailPrice
            }
        }
    }

    /**
     * 编辑库存/价格
     *
     * @param req
     */
    override fun updateSkuByUpdateStockPrice(req: List<UpdateSkuByUpdateStockPriceReq>) {
        if (req.isEmpty()) {
            throw IllegalArgumentException("销售SKU ID不能为空")
        }
        val saleSkuIds = req.map { it.saleSkuId!! }
        if (saleSkuIds.isEmpty()) {
            throw IllegalArgumentException("销售SKU ID不能为空")
        }
        val saleSkuList = saleSkuRepository.listByIds(saleSkuIds)
        if (saleSkuList.isEmpty()) {
            throw IllegalArgumentException("销售SKU不存在")
        }
        val updateSaleSkuList = req.map {
            SaleSku().apply {
                this.saleSkuId = it.saleSkuId
                this.stockQuantity = it.quantity
                this.salePrice = it.salePrice
                this.retailPrice = it.retailPrice
                this.lastSalePrice = it.lastSalePrice
                this.lastRetailPrice = it.lastRetailPrice
            }
        }
        saleSkuRepository.updateBatchById(updateSaleSkuList)

        // 组装MQ数据, 更新Lazada库存/价格
        val lazadaDataList: MutableList<LazadaPriceMqDto> = mutableListOf()

        val saleGoodsIds = saleSkuList.mapNotNull { it.saleGoodsId }
        val saleGoodsList = saleGoodsRepository.listByIds(saleGoodsIds)
        saleGoodsList.forEach { saleGoods ->
            val shop = shopRepository.getById(saleGoods.shopId!!)
            saleSkuList
                .filter { sku -> sku.saleGoodsId == saleGoods.saleGoodsId }
                .forEach { sku ->
                    val dto = LazadaPriceMqDto().apply {
                        this.platformId = saleGoods.platformId
                        this.shopId = saleGoods.shopId
                        this.shopName = saleGoods.shopName
                        this.productId = saleGoods.productId
                        this.saleProductId = saleGoods.saleGoodsId
                        this.saleSkuId = sku.saleSkuId
                        this.lazadaCountry = saleGoods.country
                        this.lazadaShopToken = shop.token
                        this.lazadaItemId = saleGoods.platformProductId
                        this.lazadaSkuId = sku.platformSkuId
                        this.lazadaSellerSku = sku.sellerSku
                        this.lazadaPrice = sku.retailPrice?.toString()
                        this.lazadaSalePrice = sku.salePrice?.toString()
                        this.lazadaStockQuantity = sku.stockQuantity?.toString()
                    }
                    lazadaDataList.add(dto)
                }
        }
        if (lazadaDataList.isNotEmpty()) {
            sendToLazada(lazadaDataList)
        }
    }

    /**
     * 发送数据到Lazada
     */
    private fun sendToLazada(lazadaDataList: List<LazadaPriceMqDto>) {
        val msgId = getId()
        val messageRecord = MessageRecord()
        messageRecord.id = msgId
        messageRecord.businessId = msgId.toString()
        messageRecord.exchange = MqConstants.EXCHANGE_POP_LAZADA_PRICE_UPDATE
        messageRecord.routingKey = MqConstants.KEY_POP_LAZADA_PRICE_UPDATE
        messageRecord.content = lazadaDataList.toJson()

        messageRecordService.preCommit(messageRecord)
        messageRecordService.commit(msgId, true)
    }

    /**
     * 获取图片分类集合
     * @param spuCode
     */
    private fun getImages(spuCode: String): ImageCollectionDTO {
        val imageRepository = imageRepositoryRepository.getBySpuCode(spuCode)
        return if (imageRepository != null) {
            imageCollectionHelper.buildImageCollection(spuCode, imageRepository)
        } else {
            ImageCollectionDTO()
        }
    }

    /**
     * 获取映射的平台品类
     *
     * @param product
     * @param platformId
     * @return
     */
    private fun getPlatformCategory(product: Product, platformId: Long): PublishCategoryMapping? {
        if (product.categoryId != null) {
            val categoryMapping = publishCategoryMappingRepository.getByPublishCategoryIdAndPlatformId(product.categoryId!!, platformId)
            if (categoryMapping != null) {
                return categoryMapping
            }
        }
        return null
    }

    /**
     * 从销售商品列表中提取产品发布信息
     *
     * @param saleGoodsList 销售商品列表
     * @return 产品发布信息
     */
    private fun extractProductPublishInfo(saleGoodsList: List<SaleGoods>): ProductPublishInfoBO {
        val publishInfo = ProductPublishInfoBO()
        if (saleGoodsList.isEmpty()) {
            return publishInfo
        }

        // 查找最近上架时间的记录并设置相关信息
        saleGoodsList.filter { it.latestPublishTime != null }
            .maxByOrNull { it.latestPublishTime!! }
            ?.let { latestPublishedGoods ->
                publishInfo.latestPublishTime = latestPublishedGoods.latestPublishTime
                publishInfo.latestPublishUserId = latestPublishedGoods.latestPublishUserId
                publishInfo.latestPublishUserName = latestPublishedGoods.latestPublishUserName
            }

        // 查找最近下架时间的记录并设置相关信息
        saleGoodsList.filter { it.latestOfflineTime != null }
            .maxByOrNull { it.latestOfflineTime!! }
            ?.let { latestOfflineGoods ->
                publishInfo.latestOfflineTime = latestOfflineGoods.latestOfflineTime
                publishInfo.latestOfflineUserName = latestOfflineGoods.latestOfflineUserName
            }

        // 查找最近修改时间的记录并设置相关信息
        saleGoodsList.filter { it.revisedTime != null }
            .maxByOrNull { it.revisedTime!! }
            ?.let { latestRevisedGoods ->
                publishInfo.latestRevisedTime = latestRevisedGoods.revisedTime
                publishInfo.latestRevisedUserName = latestRevisedGoods.reviserName
            }

        saleGoodsList.filter { it.publishTime != null }.minByOrNull { it.publishTime!! }
            ?.let {
                publishInfo.firstPublishTime = it.publishTime
                publishInfo.publishUserName = it.publishUserName
            }
        return publishInfo
    }

    private fun savePicture(product: Product) {
        val imageRepository = imageRepositoryRepository.ktQuery()
            .eq(ImageRepository::spuCode, product.spuCode)
            .one()

        if (imageRepository == null) {
            return
        }

        val picture = productPictureRepository.getOneByProductId(product.productId!!)
        if (Objects.isNull(picture)) {
            val productPicture = ProductPicture().apply {
                this.productId = product.productId
                this.spuCode = product.spuCode
                this.sourceImageUrl = imageRepository.mainUrl
            }
            productPictureRepository.save(productPicture)
        }
    }

    /**
     * 更新SaleGoods列表信息
     * @param req 更新请求
     * @param saleGoodsList 销售商品列表
     */
    private fun updateSaleGoodsList(req: ProductLazadaPublishedUpdateReq, saleGoodsList: List<SaleGoods>) {
        saleGoodsList.forEach { saleGoods ->
            var update = false
            val updateEntity = SaleGoods().apply {
                this.saleGoodsId = saleGoods.saleGoodsId
            }

            // 根据saleGoods的country从标题列表中找到对应的标题
            req.productTitleList?.firstOrNull { it.country == saleGoods.country }?.let { titleResp ->
                if (titleResp.title?.isNotBlank() == true && ObjectUtils.notEqual(titleResp.title, saleGoods.productTitle)) {
                    updateEntity.productTitle = titleResp.title
                    saleGoods.productTitle = titleResp.title
                    update = true
                }
            }

            // 更新品牌名
            if (req.brandName?.isNotBlank() == true && ObjectUtils.notEqual(req.brandName, saleGoods.brandName)) {
                updateEntity.brandName = req.brandName
                saleGoods.brandName = req.brandName
                update = true
            }
            // 更新包裹重量
            if (req.packageDimensionsLength?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsLength, saleGoods.packageDimensionsLength)) {
                updateEntity.packageDimensionsLength = req.packageDimensionsLength
                saleGoods.packageDimensionsLength = req.packageDimensionsLength
                update = true
            }
            if (req.packageDimensionsWidth?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsWidth, saleGoods.packageDimensionsWidth)) {
                updateEntity.packageDimensionsWidth = req.packageDimensionsWidth
                saleGoods.packageDimensionsWidth = req.packageDimensionsWidth
                update = true
            }
            if (req.packageDimensionsHeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageDimensionsHeight, saleGoods.packageDimensionsHeight)) {
                updateEntity.packageDimensionsHeight = req.packageDimensionsHeight
                saleGoods.packageDimensionsHeight = req.packageDimensionsHeight
                update = true
            }
            if (req.packageWeight?.isNotBlank() == true && ObjectUtils.notEqual(req.packageWeight, saleGoods.packageWeight)) {
                updateEntity.packageWeight = req.packageWeight
                saleGoods.packageWeight = req.packageWeight
                update = true
            }

            // 只有在有字段变更时才执行更新操作
            if (update) {
                saleGoodsRepository.updateById(updateEntity)
                log.info { "更新saleGoods信息, saleGoodsId: ${saleGoods.saleGoodsId}, country: ${saleGoods.country}" }
            }
        }
    }
}
