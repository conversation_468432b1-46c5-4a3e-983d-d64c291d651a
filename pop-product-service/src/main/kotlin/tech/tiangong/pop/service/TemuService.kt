package tech.tiangong.pop.service

import tech.tiangong.eis.temu.resp.TemuAttrsGetResp
import tech.tiangong.pop.common.resp.TemuCategoryResp
import tech.tiangong.pop.req.product.temu.TemuBaseReq
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo

interface TemuService {

    /**
     * 获取类目属性，如果有缓存会从缓存中取
     */
    fun getAttrsByCategoryWithCache(categoryId: Int):TemuAttrsGetResp?

    fun syncTemuCategory(req: TemuBaseReq)

    fun listByPlatformCategoryIds(categoryIds: MutableList<Long>?): List<TemuCategoryResp>

    fun updateTemuCategoryTreeCache()

    fun updateTemuCategoryPathList()

    fun getTemuPlatformCategoryTree(): List<PublishPlatformCategoryVo>
}