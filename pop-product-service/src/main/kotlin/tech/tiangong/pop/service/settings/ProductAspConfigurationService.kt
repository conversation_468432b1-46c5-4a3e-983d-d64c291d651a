package tech.tiangong.pop.service.settings

import tech.tiangong.pop.dao.entity.ProductAspConfiguration
import tech.tiangong.pop.req.settings.ProductAspConfigQueryReq
import tech.tiangong.pop.req.settings.ProductAspConfigSaveReq
import tech.tiangong.pop.resp.settings.ProductAspConfigResp

interface ProductAspConfigurationService {
    /**
     * 查询商品ASP配置列表
     *
     * @param req 查询请求参数
     * @return 商品ASP配置列表
     * @date 2025/04/27
     */
    fun queryAspConfigs(req: ProductAspConfigQueryReq): List<ProductAspConfigResp>

    /**
     * 保存或更新商品ASP配置
     *
     * @param req 保存/更新请求参数
     * @return 操作是否成功
     * @throws BusinessException 当配置重复或记录不存在时
     * @date 2025/04/27
     */
    fun saveOrUpdate(req: ProductAspConfigSaveReq): Boolean

    /**
     * 批量保存或更新商品ASP配置
     *
     * @param reqList 批量保存/更新请求参数列表
     * @return 操作是否成功
     * @throws BusinessException 当配置重复或记录不存在时
     * @date 2025/04/27
     */
    fun batchSaveOrUpdate(reqList: List<ProductAspConfigSaveReq>): Boolean

    /**
     * 获取指定条件的商品ASP配置
     *
     * @param platformId 平台
     * @param countryType 店铺类型
     * @param shopBusinessType 运营模式
     * @param printType 印花类型
     * @param prototypeNum 版型号
     * @return 商品ASP配置
     * @date 2025/04/27
     */
    fun getAspConfigWithCache(
        platformId: Long,
        countryType: String,
        shopBusinessType: Int,
        printType: String,
        prototypeNum: String
    ): ProductAspConfiguration?

    /**
     * 刷新商品ASP配置缓存
     */
    fun refreshCache(): Boolean
}