package tech.tiangong.pop.service

import org.springframework.web.multipart.MultipartFile
import jakarta.servlet.http.HttpServletRequest

interface CategoryMappingImportService {
    /**
     * 导入品类映射
     * @return ImportResult 导入结果
     */
    fun importCategoryMapping(
        file: MultipartFile,
        platformId: Long,
        channelId: Long,
        country: String,
        channelName: String,
        platformName: String
    ): ImportResult

    /**
     * 导入品类属性
     */
    fun importCategoryAttr(file: MultipartFile): ImportResult

    /**
     * 导入平台属性映射
     */
    fun importPlatformAttributeMapping(
        file: MultipartFile,
        country: String,
        platformId: Long,
        request: HttpServletRequest
    ): ImportResult
}

data class ImportResult(
    val total: Int,
    val success: Int,
    val failed: Int,
    val errorMessages: List<String>,
    val duplicateCount: Int = 0  // 新增重复数据计数
)
