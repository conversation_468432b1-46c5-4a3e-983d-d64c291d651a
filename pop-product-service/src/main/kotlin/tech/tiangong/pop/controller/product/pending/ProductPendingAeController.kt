package tech.tiangong.pop.controller.product.pending

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.failed
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.ae.CallAeComponent
import tech.tiangong.pop.component.export.AeProductExportComponent
import tech.tiangong.pop.component.export.pending.error.publish.AePublishErrorLogExportComponent
import tech.tiangong.pop.req.product.ProductPendingPublishLogPageReq
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.resp.product.ProductPendingPublishLogPageResp
import tech.tiangong.pop.resp.product.ProductPendingPublishLogStatisticsResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAeDetailResp
import tech.tiangong.pop.resp.product.ae.ProductPendingAePageResp
import tech.tiangong.pop.resp.product.ae.ProductPendingStatisticsResp
import tech.tiangong.pop.service.product.pending.ProductPendingAeService
import java.io.IOException

/**
 * 待上架商品-AE
 */
@RestController
@RequestMapping("/web/v1/product/pending/ae")
class ProductPendingAeController(
    private val productPendingAeService: ProductPendingAeService,
    private val callAeComponent: CallAeComponent,
    private val aePublishErrorLogExportComponent: AePublishErrorLogExportComponent,
    private val aeProductExportComponent: AeProductExportComponent,
) {

    /**
     * 商品列表-分页
     * @tags v0904
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<PageVo<ProductPendingAePageResp>> {
        return ok(productPendingAeService.page(req))
    }

    /**
     * 商品列表-统计
     * @tags v0904
     */
    @PostMapping("/statistics")
    fun statistics(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<ProductPendingStatisticsResp> {
        return ok(productPendingAeService.statistics(req))
    }

    /**
     * 商品详情
     * @tags v0904
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductPendingAeDetailReq): DataResponse<ProductPendingAeDetailResp> {
        return ok(productPendingAeService.detail(req))
    }

    /**
     * 商品更新
     * @tags v0904
     */
    @PostMapping("/update")
    fun update(@Validated @RequestBody req: ProductPendingAeUpdateReq): DataResponse<Unit> {
        productPendingAeService.update(req)
        return ok()
    }

    /**
     * 任务操作-开始任务
     * @tags v0904
     */
    @PostMapping("/task/start")
    fun start(@Validated @RequestBody aeSpuIds: List<Long>): DataResponse<Unit> {
        if (aeSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        productPendingAeService.start(aeSpuIds)
        return ok()
    }

    /**
     * 任务操作-更换人员
     * @tags v0904
     */
    @PostMapping("/task/change")
    fun change(@Validated @RequestBody req: ProductPendingAeTaskChangeReq): DataResponse<Unit> {
        if (req.aeSpuIds.isEmpty()) {
            return failed("请选择商品")
        }
        if (req.users.isEmpty()) {
            return failed("请选择人员")
        }
        productPendingAeService.change(req)
        return ok()
    }

    /**
     * 任务操作-取消上架
     * @tags v0904
     */
    @PostMapping("/task/cancel")
    fun cancel(@Validated @RequestBody req: ProductPendingAeTaskCancelReq): DataResponse<Unit> {
        productPendingAeService.cancel(req)
        return ok()
    }

    /**
     * 上架AE平台
     * @tags v0904
     *
     * @param req 入参
     */
    @PostMapping("/platform/update")
    fun platformUpdate(@Validated @RequestBody req: List<ProductPendingAePlatformUpdateReq>): DataResponse<Unit> {
        if (req.isEmpty()) {
            return failed("请选择商品")
        }
        req.forEach {
            callAeComponent.publishProduct(it)
        }
        return ok()
    }

    /**
     * 导出-待上架-上架失败
     * @tags v0904
     */
    @PostMapping("/export/publish-error-log")
    fun exportPublishErrorLog(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<Unit> {
        aePublishErrorLogExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 上架记录-统计
     * @tags v0904
     */
    @PostMapping("/publish/log/statistics")
    fun publishLogStatistics(): DataResponse<ProductPendingPublishLogStatisticsResp> {
        return ok(productPendingAeService.publishLogStatistics())
    }

    /**
     * 上架记录-分页列表
     * @tags v0904
     */
    @PostMapping("/publish/log/page")
    fun publishLogPage(@RequestBody req: ProductPendingPublishLogPageReq): DataResponse<PageVo<ProductPendingPublishLogPageResp>> {
        return ok(productPendingAeService.publishLogPage(req))
    }

    /**
     * 导出-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/export-product")
    fun exportProductPublishDataAsync(@Validated @RequestBody req: ProductPendingAePageReq): DataResponse<Unit> {
        aeProductExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导入-待上架-商品数据
     * @tags v0904
     */
    @PostMapping("/import-product")
    @Throws(IOException::class)
    fun importExcel(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productPendingAeService.importExcel(excelFile)
        return ok()
    }
}