package tech.tiangong.pop.controller.product

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.temu.ShopTemplateCreateReq
import tech.tiangong.pop.req.product.temu.ShopTemplatePageReq
import tech.tiangong.pop.req.product.temu.ShopTemplateStateReq
import tech.tiangong.pop.req.product.temu.ShopTemplateUpdateReq
import tech.tiangong.pop.resp.product.temu.ShopTemplateDetailResp
import tech.tiangong.pop.resp.product.temu.ShopTemplatePageResp
import tech.tiangong.pop.service.product.ShopTemplateService

/**
 * 店铺上架模板
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/shop-template")
class ShopTemplateController(
    private val shopTemplateService: ShopTemplateService
) {

    /**
     * 上架模板分页
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ShopTemplatePageReq): DataResponse<PageVo<ShopTemplatePageResp>> {
        return ok(shopTemplateService.page(req))
    }

    /**
     * 上架模板详情   todo 出参json字段
     * @return
     */
    @GetMapping("/detail/{shopTemplateId}")
    fun detail(@PathVariable(name = "shopTemplateId") shopTemplateId: Long): DataResponse<ShopTemplateDetailResp> {
        return ok(shopTemplateService.detail(shopTemplateId))
    }

    /**
     * 创建上架模板  todo 入参json字段
     */
    @PostMapping("/create")
    fun create(@Validated @RequestBody req: ShopTemplateCreateReq): DataResponse<Long> {
        return ok(shopTemplateService.addTemplate(req))
    }

    /**
     * 编辑保存上架模板 todo 入参json字段
     */
    @PostMapping("/edit")
    fun edit(@Validated @RequestBody req: ShopTemplateUpdateReq): DataResponse<Unit> {
        shopTemplateService.editTemplate(req)
        return ok()
    }

    /**
     * 启动停用
     */
    @PostMapping("/update-status")
    fun updateStatus(@Validated @RequestBody req: ShopTemplateStateReq): DataResponse<Unit> {
        shopTemplateService.updateStatus(req)
        return ok()
    }

    /**
     * 复制上架模板
     */
    @PostMapping("/copy/{shopTemplateId}")
    fun copyListingTemplate(@PathVariable(name = "shopTemplateId") shopTemplateId: Long): DataResponse<Long> {
        return ok(shopTemplateService.copyTemplate(shopTemplateId))
    }

}