package tech.tiangong.pop.controller.product.inner

import cn.hutool.core.collection.CollectionUtil
import cn.hutool.core.collection.ListUtil
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.http.ResponseEntity
import org.springframework.util.CollectionUtils
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.constant.UrlVersionConstant.INNER
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.PlatformEnum.LAZADA
import tech.tiangong.pop.common.req.ETAUpdateReq
import tech.tiangong.pop.common.req.FixETAUpdateReq
import tech.tiangong.pop.component.TranslateComponent
import tech.tiangong.pop.component.ae.AeUpdateProductComponent
import tech.tiangong.pop.component.ae.CallAeComponent
import tech.tiangong.pop.component.lazada.CallLazadaComponent
import tech.tiangong.pop.config.AliexpressProperties.AePlatformConfig
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.dao.repository.SaleGoodsRepository
import tech.tiangong.pop.dto.product.ProductInitDto
import tech.tiangong.pop.req.fix.FixProductInfoReq
import tech.tiangong.pop.req.product.*
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp
import tech.tiangong.pop.service.FixData2Service
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.*
import tech.tiangong.pop.service.settings.ProductTitleDictionaryTranslateExcelService
import tech.tiangong.pop.service.tryon.TryOnTaskService
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.ExecutorService
import java.util.stream.Collectors


/**
 * 内部接口-临时使用
 */
@Slf4j
@RestController
@RequestMapping("$WEB$INNER/test-test")
@PreCheckIgnore
class TestInnerController(
    private val fixDataService: FixProductService,
    private val fixData2Service: FixData2Service,
    private val productLazadaService: ProductLazadaService,
    private val productBarcodeService: ProductBarcodeService,
    private val productManageService: ProductManageService,
    private val aeUpdateProductComponent: AeUpdateProductComponent,
    private val repairProductService: RepairProductService,
    private val commonProperties: CommonProperties,
    private val productETAService: ProductETAService,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val lazadaApiService: LazadaApiService,
    private val priceGenerateExcelService: PriceGenerateExcelService,
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val translateComponent: TranslateComponent,
    private val productTitleDictionaryTranslateExcelService: ProductTitleDictionaryTranslateExcelService,
    private val callLazadaComponent: CallLazadaComponent,
    private val callAeComponent: CallAeComponent,
    private val productRepository: ProductRepository,
    private val tryOnTaskService: TryOnTaskService,
) {

    /**
     * 更新商品尺码信息
     * @param req
     */
    @PostMapping("/update-sale-sku-size-names")
    fun updateSaleSkuSizeNames(@RequestBody req: SaleSkuFetchReq?): DataResponse<Unit> {
        withSystemUser { fixDataService.updateSaleSkuSizeNames(req) }
        return ok()
    }


    /**
     * 拉取LZD商品信息(包含DELETED)
     * @param req
     */
    @PostMapping("/pull/lzd/product")
    fun pullLzdProduct(@RequestBody req: PullLzdSellerSkuReq): DataResponse<Unit> {
        withSystemUser {
            for ((sellerSku, shopName, country) in req.list) {
                productLazadaService.pullLazadaProductDetailBySellerSku(sellerSku, shopName, country)
            }
        }
        return ok()
    }

    /**
     * 补充sellerSku到barcode
     * @param req
     */
    @PostMapping("/set/sku-to-barcode")
    fun setSellerSkuToBarcode(@RequestBody req: SetSellerSkuToBarcode): DataResponse<Unit> {
        withSystemUser { productBarcodeService.setSellerSkuToBarcode(req) }
        return ok()
    }

    /**
     * 初始化模板表
     * @param req
     */
    @PostMapping("/init/template")
    fun initTemplate(@RequestBody req: ProductInitDto): DataResponse<Unit> {
        withSystemUser { productManageService.initTemplateByProduct(req) }
        return ok()
    }

    /**
     * 异常商品检测
     * @param req
     */
    @PostMapping("/check/error/product")
    fun checkErrorProduct(@RequestBody req: CheckErrorProductReq): DataResponse<Unit> {
        withSystemUser {
            when (req.platform) {
                LAZADA -> {
                    val saleGoodsList = saleGoodsRepository.listByIds(req.saleGoodsIds)
                    if (saleGoodsList.isNotEmpty()) {
                        saleGoodsList.forEach { saleGoods ->
                            repairProductService.lazadaCheckError(saleGoods)
                        }
                    }
                }

                AE -> {
                    val saleGoodsList = aeSaleGoodsRepository.listByIds(req.saleGoodsIds)
                    if (saleGoodsList.isNotEmpty()) {
                        saleGoodsList.forEach { saleGoods ->
                            repairProductService.aeCheckError(saleGoods)
                        }
                    }
                }

                else -> {
                    throw IllegalArgumentException("平台类型不支持")
                }
            }
        }
        return ok()
    }

    /**
     * 测试更新AE产品
     * @param saleGoodsId
     */
    @PostMapping("/update/ae/product/test/{saleGoodsId}")
    fun testUpdateAeProduct(@PathVariable("saleGoodsId") saleGoodsId: Long): DataResponse<Unit> {
        aeUpdateProductComponent.testUpdate(saleGoodsId)
        return ok()
    }

    /**
     * 测试ae配置更新
     */
    @GetMapping("/update/ae/product/test/config")
    fun testUpdateAeProductConfig(): DataResponse<AePlatformConfig> {
        return ok(aeUpdateProductComponent.testConfig())
    }

    /**
     * 测试下载配置
     */
    @GetMapping("/test/config")
    fun testDownloadConfig(): DataResponse<String> {
        return ok(mapOf("barCodeLimitExport" to commonProperties.barCodeLimitExport).toJson())
    }

    /**
     * 补充发货期/商品状态
     */
    @PostMapping("/fix-eta-update")
    fun fixEtaUpdate(@RequestBody req: FixETAUpdateReq): DataResponse<Unit> {
//        withSystemUser {
        if (!CollectionUtils.isEmpty(req.spuCode)) {
            val reqList = req.spuCode?.map { v ->
                ETAUpdateReq().apply {
                    spuCode = v
                    platformId = req.platformId
                }
            }?.toMutableList() ?: mutableListOf()
            productETAService.fixEtaUpdate(reqList)
        } else {
            val spuCodes: MutableList<String> = mutableListOf()
            if (req.platformId == LAZADA.platformId) {
                val list = saleGoodsRepository.list()
                list.stream().map { it.spuCode }.distinct().toList().forEach {
                    it?.let { it1 -> spuCodes.add(it1) }
                }

            } else if (req.platformId == AE.platformId) {
                val list = aeSaleGoodsRepository.list()
                list.stream().map { it.spuCode }.distinct().toList().forEach {
                    it?.let { it1 -> spuCodes.add(it1) }
                }
            }
            log.info { "======总共需要更新记录数量===${spuCodes.size}" }
            val spuCodesList = CollectionUtil.split(spuCodes, 500)
            for (tempList in spuCodesList) {
                val reqListTemp = tempList.stream()
                    .filter { v -> StringUtils.isNotBlank(v) }
                    .map { v: String? ->
                        ETAUpdateReq().apply {
                            spuCode = v!!
                            platformId = req.platformId
                        }
                    }.collect(Collectors.toList())

                runAsync(asyncExecutor) {
                    log.info { "======当前更新记录数量===${reqListTemp.size}" }
                    productETAService.fixEtaUpdate(reqListTemp)
                }
//                    CompletableFuture.runAsync {
//                        log.info { "======当前更新记录数量===${reqListTemp.size}" }
//                        productETAService.fixEtaUpdate(reqListTemp)
//                    }
            }
        }
//        }
        return ok()
    }

    /**
     * lazada-根据商品ID获取商品详情
     */
    @GetMapping("/lazada/detail-by-item-id")
    fun getProductDetail(
        @RequestParam countryCode: String,
        @RequestParam itemId: String,
        @RequestParam token: String,
    ): DataResponse<LazadaProductItemDetailResp> {
        val result = lazadaApiService.getProductDetailByItemId(countryCode, itemId, token)
        return ok(result)
    }

    /**
     * lazada-根据商品ID获取商品详情
     */
    @PostMapping("/fix/product/info")
    fun getProductDetail(@RequestBody req: FixProductInfoReq): DataResponse<Unit> {
        fixData2Service.fixProductInfo(req)
        return ok()
    }

    /**
     * 价格对比 生成本地文件
     */
    @PostMapping("/compare")
    fun comparePrices(@RequestParam("file") file: MultipartFile): ResponseEntity<Map<String, Any>> {
        // Create temp directory if it doesn't exist
        val tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "price-comparison")
        Files.createDirectories(tempDir)

        // Save uploaded file
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        val inputFilePath = tempDir.resolve("input-$timestamp.xlsx").toString()
        file.transferTo(File(inputFilePath))

        // Generate output file path
        val outputFilePath = tempDir.resolve("output-$timestamp.xlsx").toString()

        // Process file and generate comparison
        val resultPath = priceGenerateExcelService.comparePrices(inputFilePath, outputFilePath)

        // Return response with file download path
        return ResponseEntity.ok(
            mapOf(
                "status" to "success",
                "message" to "Price comparison completed",
                "resultFile" to resultPath,
                "timestamp" to timestamp
            )
        )
    }

    /**
     * 测试翻译
     */
    @PostMapping("/translate")
    fun testTranslate(@RequestParam("text") text: String, @RequestParam("country") country: String): DataResponse<String> {
        val translatedText = translateComponent.translateAndSetTitle(text, country)
        return ok(translatedText ?: "Translation failed")
    }


    /**
     * 测试标题字典翻译Excel
     */
    @PostMapping("/title-dictionary-translate-excel")
    fun testDictionaryTranslateExcel(): DataResponse<String> {
        val str = productTitleDictionaryTranslateExcelService.generateDictionaryExcel()
        return ok(str)
    }

    /**
     * 根据JSON数据生成标题字典翻译Excel
     *
     * @param dictDataMap 字段名称与字典项列表的映射
     * @return Excel文件路径
     */
    @PostMapping("/title-translate-from-json")
    fun translateFromJson(@RequestBody dictDataMap: Map<String, List<ProductTitleDictionaryTranslateExcelService.SimpleDictItem>>): DataResponse<String> {
        val excelPath = productTitleDictionaryTranslateExcelService.generateDictionaryExcelFromJson(dictDataMap)
        return ok(excelPath)
    }

    /**
     * 绑定条码关系
     */
    @PostMapping("/bind/barcode/ref")
    fun bindBarcodeRef(@RequestBody req: TestBindBarcodeRefReq): DataResponse<Unit> {
        when (req.platform) {
            LAZADA -> {
                val saleGoods = saleGoodsRepository.listByIds(req.saleGoodsIds)
                if (saleGoods.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val productIds = saleGoods.mapNotNull { it.productId }.toSet()
                if (productIds.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val products = productRepository.listByIds(productIds)
                if (products.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val productMap = products.associateBy { it.productId }
                saleGoods.forEach {
                    val product = productMap[it.productId]
                    product?.let { it1 -> callLazadaComponent.bindBarcode(it1, it) }
                }
            }

            AE -> {
                val saleGoods = aeSaleGoodsRepository.listByIds(req.saleGoodsIds)
                if (saleGoods.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val productIds = saleGoods.mapNotNull { it.productId }.toSet()
                if (productIds.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val products = productRepository.listByIds(productIds)
                if (products.isEmpty()) {
                    throw IllegalArgumentException("没有找到对应的商品")
                }
                val productMap = products.associateBy { it.productId }
                saleGoods.forEach {
                    val product = productMap[it.productId]
                    product?.let { it1 -> callAeComponent.bindBarcode(it1, it) }
                }
            }

            else -> {
                throw IllegalArgumentException("平台类型不支持")
            }
        }
        return ok()
    }

    /**
     * 补录商品的款式类型
     */
    @PostMapping("/fill-product-style-type")
    fun fillProductStyleType(@RequestBody req: FillProductStyleTypeReq): DataResponse<Unit> {
        if (req.productIds.isNullOrEmpty()) {
            val productList = productRepository.ktQuery().select(Product::productId).list()
            if (!productList.isNullOrEmpty()) {
                val products = ListUtil.split(productList, 500) as List<List<Product>>
                products.forEach { product ->
                    val ids = product.mapNotNull { it.productId }.toList()
                    runAsync(asyncExecutor) {
                        log.info { "======当前更新记录数量===${ids.size}" }
                        val tempReq = FillProductStyleTypeReq()
                        tempReq.productIds = ids
                        withSystemUser {
                            productManageService.fillStyleType(tempReq)
                        }
                    }
                }
            }
        } else {
            runAsync(asyncExecutor) {
                log.info { "======当前更新记录数量===${req.productIds?.size ?: 0}" }
                withSystemUser {
                    productManageService.fillStyleType(req)
                }
            }
        }

        return ok()
    }

    /**
     * 刷新商品的图包状态
     */
    @PostMapping("/refresh-image-package-state")
    fun refreshImagePackageState(@RequestBody req: RefreshProductImagePackageStateReq): DataResponse<Unit> {
        if (req.productIds.isNullOrEmpty()) {
            val productList = productRepository.ktQuery().select(Product::productId).list()
            if (!productList.isNullOrEmpty()) {
                val products = ListUtil.split(productList, 500) as List<List<Product>>
                products.forEach { product ->
                    val ids = product.mapNotNull { it.productId }.toList()
                    runAsync(asyncExecutor) {
                        log.info { "======当前更新记录数量===${ids.size}" }
                        val tempReq = RefreshProductImagePackageStateReq()
                        tempReq.productIds = ids
                        withSystemUser {
                            productManageService.refreshImagePackageState(tempReq)
                        }
                    }
                }
            }
        } else {
            runAsync(asyncExecutor) {
                log.info { "======当前更新记录数量===${req.productIds?.size ?: 0}" }
                withSystemUser {
                    productManageService.refreshImagePackageState(req)
                }
            }
        }

        return ok()
    }

    /**
     * 修复模板SKU的店铺ID
     */
    @PostMapping("/fix/template/sku/shop-id")
    fun fixTemplateSkuShopId(): DataResponse<Unit> {
        fixData2Service.fixTemplateSkuShopId()
        return ok()
    }

    /**
     * 手动推送tryOn任务到灵感中心
     */
    @PostMapping("/push-try-on-task")
    fun pushTryOnTask(@RequestBody taskIds:List<Long>): DataResponse<Unit> {
        tryOnTaskService.pushTask(taskIds)
        return ok()
    }

    /**
     * 补充商品尺码范围
     */
    @PostMapping("/fill-product-skc-size-names")
    fun fillProductSkcSizeNames(@RequestBody productIds:List<Long>?): DataResponse<Unit> {
        fixData2Service.fillProductSkcSizeNames(productIds)
        return ok()
    }
}
