package tech.tiangong.pop.controller.product.center

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.export.center.*
import tech.tiangong.pop.component.importfile.center.*
import tech.tiangong.pop.req.product.ProductCheckCreatePublishTaskReq
import tech.tiangong.pop.req.product.ProductCreatePublishTaskReq
import tech.tiangong.pop.req.product.center.ProductCenterDetailReq
import tech.tiangong.pop.req.product.center.ProductCenterPageReq
import tech.tiangong.pop.req.product.center.SaveProductCenterDetailReq
import tech.tiangong.pop.resp.product.ProductCheckCreatePublishTaskResp
import tech.tiangong.pop.resp.product.center.ProductCenterDetailResp
import tech.tiangong.pop.resp.product.center.ProductCenterPageResp
import tech.tiangong.pop.service.product.ProductCenterService
import java.io.IOException

/**
 * 商品中心-商品
 */
@RestController
@RequestMapping("/web/v1/product/center")
class ProductCenterController(
    private val productCenterService: ProductCenterService,
    private val productCenterBasicInfoExportComponent: ProductCenterBasicInfoExportComponent,
    private val productCenterAttrExportComponent: ProductCenterAttrExportComponent,
    private val productCenterLogisticsPackageExportComponent: ProductCenterLogisticsPackageExportComponent,
    private val productCenterPlatformAttrExportComponent: ProductCenterPlatformAttrExportComponent,
    private val productCenterAllExportComponent: ProductCenterAllExportComponent,
    private val productCenterMissingInfoExportComponent: ProductCenterMissingInfoExportComponent,
    private val productCenterBasicInfoImportComponent: ProductCenterBasicInfoImportComponent,
    private val productCenterAttrImportComponent: ProductCenterAttrImportComponent,
    private val productCenterLogisticsPackageImportComponent: ProductCenterLogisticsPackageImportComponent,
    private val productCenterPlatformAttrImportComponent: ProductCenterPlatformAttrImportComponent,
    private val productCenterAllImportComponent: ProductCenterAllImportComponent,
) {

    /**
     * 商品列表-分页
     * @tags v0904
     * @param req
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<PageVo<ProductCenterPageResp>> {
        return ok(productCenterService.page(req))
    }

    /**
     * 商品详情
     * @tags v0904
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductCenterDetailReq): DataResponse<ProductCenterDetailResp> {
        return ok(productCenterService.detail(req))
    }

    /**
     * 保存商品详情
     * @tags v0904
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/save-product-detail")
    fun saveProductDetail(@Validated @RequestBody req: SaveProductCenterDetailReq): DataResponse<Unit> {
        return ok(productCenterService.saveProductDetail(req))
    }

    /**
     * 导入-商品中心-商品基本信息
     * @tags v0904
     */
    @PostMapping("/import-product-basic-info")
    @Throws(IOException::class)
    fun importProductBasicInfo(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productCenterBasicInfoImportComponent.importExcel(excelFile)
        return ok()
    }

    /**
     * 导入-商品中心-商品属性
     * @tags v0904
     */
    @PostMapping("/import-attr")
    @Throws(IOException::class)
    fun importAttr(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productCenterAttrImportComponent.importExcel(excelFile)
        return ok()
    }

    /**
     * 导入-商品中心-物流包装
     * @tags v0904
     */
    @PostMapping("/import-logistics-package")
    @Throws(IOException::class)
    fun importLogisticsPackage(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productCenterLogisticsPackageImportComponent.importExcel(excelFile)
        return ok()
    }

    /**
     * 导入-商品中心-平台属性
     * @tags v0904
     */
    @PostMapping("/import-platform-attr")
    @Throws(IOException::class)
    fun importPlatformAttr(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productCenterPlatformAttrImportComponent.importExcel(excelFile)
        return ok()
    }

    /**
     * 导入-商品中心-全部信息
     * @tags v0904
     */
    @PostMapping("/import-all")
    @Throws(IOException::class)
    fun importAll(@RequestParam("excelFile") excelFile: MultipartFile): DataResponse<Unit> {
        productCenterAllImportComponent.importExcel(excelFile)
        return ok()
    }

    /**
     * 导出-商品中心-商品基本信息
     * @tags v0904
     */
    @PostMapping("/export-product-basic-info")
    @Throws(IOException::class)
    fun exportProductBasicInfo(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<Unit> {
        productCenterBasicInfoExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导出-商品中心-商品属性
     * @tags v0904
     */
    @PostMapping("/export-attr")
    @Throws(IOException::class)
    fun exportAttr(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<Unit> {
        productCenterAttrExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导出-商品中心-物流包装
     * @tags v0904
     */
    @PostMapping("/export-logistics-package")
    @Throws(IOException::class)
    fun exportLogisticsPackage(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<Unit> {
        productCenterLogisticsPackageExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导出-商品中心-平台属性
     * @tags v0904
     */
    @PostMapping("/export-platform-attr")
    @Throws(IOException::class)
    fun exportPlatformAttr(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<Unit> {
        productCenterPlatformAttrExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导出-商品中心-全部信息
     * @tags v0904
     */
    @PostMapping("/export-all")
    @Throws(IOException::class)
    fun exportAll(@Validated @RequestBody req: ProductCenterPageReq): DataResponse<Unit> {
        productCenterAllExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 导出-商品中心-信息缺失
     * @tags v0904
     */
    @PostMapping("/export-missing-info")
    @Throws(IOException::class)
    fun exportMissingInfo(@Validated @RequestBody req: ProductCreatePublishTaskReq): DataResponse<Unit> {
        productCenterMissingInfoExportComponent.createExportTask(req)
        return ok()
    }

    /**
     * 检查-创建上架任务
     * @tags v0904
     */
    @PostMapping("/check/create-publish-task")
    fun checkCreatePublishTask(@Validated @RequestBody req: ProductCheckCreatePublishTaskReq): DataResponse<ProductCheckCreatePublishTaskResp> {
        productCenterService.checkCreatePublishTask(req)
        return ok()
    }

    /**
     * 创建上架任务
     * @tags v0904
     */
    @PostMapping("/create-publish-task")
    fun createPublishTask(@Validated @RequestBody req: ProductCreatePublishTaskReq): DataResponse<Unit> {
        productCenterService.createPublishTask(req)
        return ok()
    }

}