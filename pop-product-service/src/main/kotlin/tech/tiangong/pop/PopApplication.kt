package tech.tiangong.pop

import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.ComponentScan

@SpringBootApplication
@EnableFeignClients(
    basePackages = [
        "tech.tiangong.ofp",
        "team.aikero.admin.sdk.client",
        "team.aikero.pigeon.sdk",
        "tech.tiangong.sdp.sdk.client",
        "tech.tiangong.pop.external",
        "tech.tiangong.pop.helper",
        "tech.tiangong.eis.client",
        "tech.tiangong.butted.client",
        "com.zjkj.aigc.client",
        "com.zjkj.aigc.feign.client",
        "team.aikero.blade.uacs.sdk",
    ]
)
@ComponentScan(
    basePackages = [
        "tech.tiangong.pop",
        "tech.tiangong.ofp",
        "tech.tiangong.eis",
        "com.zjkj.aigc",
    ]
)
@MapperScan(basePackages = ["tech.tiangong.**.mapper"])
class PopApplication

fun main(args: Array<String>) {
    runApplication<PopApplication>(*args)
}
