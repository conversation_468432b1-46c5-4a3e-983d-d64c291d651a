package tech.tiangong.pop.service.impl

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import tech.tiangong.pop.req.sdk.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.*

/**
 * AliexpressLocalServiceImpl 转换逻辑测试
 */
class AliexpressLocalServiceImplTest {

    @Test
    fun `测试 AliexpressLocalServiceProductsListResponse 转换为 AliexpressProductPageResponse`() {
        // 准备测试数据
        val skuInfo = SkuInfo(
            suggestNote = null,
            skuWarehouseStockList = emptyList(),
            effectiveSupplyPrice = "10.50",
            sellerSku = "SKU001",
            skuStock = 100,
            skuId = 12345L,
            supplyPrice = "10.50",
            suggestPrice = null,
            skuAuditStatus = "1",
            variation = "Color:Red",
            status = "active"
        )

        val product = Product(
            productMaxPrice = "20.00",
            createdTime = "2023-12-01 10:30:00",
            productMinPrice = "10.00",
            imageUrls = "http://img1.jpg,http://img2.jpg,http://img3.jpg",
            title = "测试商品标题",
            auditFailureReason = null,
            currencyCode = "USD",
            productAuditStatus = 1,
            searchSkuInfoList = listOf(skuInfo),
            shippingTemplate = "TEMPLATE123",
            modifiedTime = "2023-12-01 15:45:00",
            leafCategoryId = 100001L,
            productId = 987654321L,
            totalStocks = 100,
            auditFailureType = null
        )

        val result = Result(
            success = true,
            totalCount = 1,
            totalPage = 1,
            currentPage = 1,
            pageSize = 20,
            productList = listOf(product)
        )

        val sourceResponse = AliexpressLocalServiceProductsListResponse(result = result)

        // 执行转换
        val targetResponse = sourceResponse.toAliexpressProductPageResponse()

        // 验证转换结果
        assertNotNull(targetResponse)
        assertNotNull(targetResponse.result)
        
        val targetResult = targetResponse.result!!
        assertEquals(true, targetResult.success)
        assertEquals(1, targetResult.productCount)
        assertEquals(1, targetResult.totalPage)
        assertEquals(1, targetResult.currentPage)
        assertNull(targetResult.errorMsg)
        assertNull(targetResult.errorCode)
        assertNotNull(targetResult.dtoList)
        assertEquals(1, targetResult.dtoList!!.size)

        // 验证商品转换
        val targetProduct = targetResult.dtoList!![0]
        assertEquals(987654321L, targetProduct.productId)
        assertEquals("测试商品标题", targetProduct.subject)
        assertEquals("20.00", targetProduct.productMaxPrice)
        assertEquals("10.00", targetProduct.productMinPrice)
        assertEquals("USD", targetProduct.currencyCode)
        assertEquals("2023-12-01 10:30:00", targetProduct.gmtCreate)
        assertEquals("2023-12-01 15:45:00", targetProduct.gmtModified)
        assertEquals("TEMPLATE123", targetProduct.freightTemplateId)
        
        // 验证图片URL分隔符转换
        assertEquals("http://img1.jpg;http://img2.jpg;http://img3.jpg", targetProduct.imageURLs)
        
        // 验证默认为null的字段
        assertNull(targetProduct.productType)
        assertNull(targetProduct.groupId)
        assertNull(targetProduct.ownerMemberId)
        assertNull(targetProduct.ownerMemberSeq)
        assertNull(targetProduct.src)
        assertNull(targetProduct.wsDisplay)
        assertNull(targetProduct.wsOfflineDate)
        assertNull(targetProduct.couponEndDate)
        assertNull(targetProduct.couponStartDate)
    }

    @Test
    fun `测试失败响应的转换`() {
        val result = Result(
            success = false,
            totalCount = 0,
            totalPage = 0,
            currentPage = 1,
            pageSize = 20,
            productList = emptyList()
        )

        val sourceResponse = AliexpressLocalServiceProductsListResponse(result = result)
        val targetResponse = sourceResponse.toAliexpressProductPageResponse()

        val targetResult = targetResponse.result!!
        assertEquals(false, targetResult.success)
        assertEquals("查询失败", targetResult.errorMsg)
        assertEquals(-1, targetResult.errorCode)
        assertEquals(0, targetResult.productCount)
        assertTrue(targetResult.dtoList!!.isEmpty())
    }

    @Test
    fun `测试图片URL转换逻辑`() {
        // 测试正常的逗号分隔
        assertEquals("img1.jpg;img2.jpg", convertImageUrls("img1.jpg,img2.jpg"))
        
        // 测试单个图片
        assertEquals("img1.jpg", convertImageUrls("img1.jpg"))
        
        // 测试空字符串
        assertNull(convertImageUrls(""))
        assertNull(convertImageUrls("   "))
        
        // 测试null值
        assertNull(convertImageUrls(null))
        
        // 测试已经是分号分隔的情况
        assertEquals("img1.jpg;img2.jpg", convertImageUrls("img1.jpg;img2.jpg"))
    }

    @Test
    fun `测试商品审核失败原因的传递`() {
        val product = Product(
            productMaxPrice = "20.00",
            createdTime = "2023-12-01 10:30:00",
            productMinPrice = "10.00",
            imageUrls = "http://img1.jpg",
            title = "测试商品",
            auditFailureReason = "图片不符合要求",
            currencyCode = "USD",
            productAuditStatus = 0,
            searchSkuInfoList = emptyList(),
            shippingTemplate = "TEMPLATE123",
            modifiedTime = "2023-12-01 15:45:00",
            leafCategoryId = 100001L,
            productId = 987654321L,
            totalStocks = 0,
            auditFailureType = "IMAGE_VIOLATION"
        )

        val targetProduct = product.toAliexpressProductPageResult()
        assertEquals("图片不符合要求", targetProduct.auditFailureReason)
    }

    // 扩展函数，用于测试
    private fun AliexpressLocalServiceProductsListResponse.toAliexpressProductPageResponse(): AliexpressProductPageResponse {
        return AliexpressProductPageResponse(
            result = this.result.toAliexpressProductPageResultDto()
        )
    }

    private fun Result.toAliexpressProductPageResultDto(): AliexpressProductPageResultDto {
        return AliexpressProductPageResultDto(
            currentPage = this.currentPage,
            errorMsg = if (!this.success) "查询失败" else null,
            productCount = this.totalCount,
            success = this.success,
            totalPage = this.totalPage,
            errorCode = if (!this.success) -1 else null,
            errorMessage1 = null,
            dtoList = this.productList.map { it.toAliexpressProductPageResult() }
        )
    }

    private fun Product.toAliexpressProductPageResult(): AliexpressProductPageResult {
        return AliexpressProductPageResult(
            productId = this.productId,
            productType = null,
            auditFailureReason = this.auditFailureReason,
            currencyCode = this.currencyCode,
            gmtCreate = this.createdTime,
            gmtModified = this.modifiedTime,
            groupId = null,
            imageURLs = convertImageUrls(this.imageUrls),
            ownerMemberId = null,
            ownerMemberSeq = null,
            productMaxPrice = this.productMaxPrice,
            productMinPrice = this.productMinPrice,
            src = null,
            subject = this.title,
            wsDisplay = null,
            wsOfflineDate = null,
            freightTemplateId = this.shippingTemplate,
            couponEndDate = null,
            couponStartDate = null
        )
    }

    private fun convertImageUrls(imageUrls: String?): String? {
        return when {
            imageUrls.isNullOrBlank() -> null
            imageUrls.contains(",") -> imageUrls.replace(",", ";")
            else -> imageUrls
        }
    }
}
