package tech.tiangong.pop.service

import com.alibaba.fastjson2.JSONObject
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.req.category.CopyCategoryAttributeValueMappingReq
import tech.tiangong.pop.req.category.PublishCategoryMappingDetailQueryReq
import tech.tiangong.pop.req.category.SaveCategoryAttributeValueMappingReq
import tech.tiangong.pop.req.category.SaveCategoryAttributeValueMappingReq.AttributeMapper
import tech.tiangong.pop.req.category.SaveCategoryAttributeValueMappingReq.AttributeValueMapper
import tech.tiangong.pop.req.category.SavePublishCategoryMappingReq
import tech.tiangong.pop.service.category.PublishCategoryAttributeService
import tech.tiangong.pop.service.category.PublishCategoryMappingService

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class PublishCategoryMappingServiceTest {

    @Autowired
    lateinit var publishCategoryMappingService: PublishCategoryMappingService
    @Autowired
    lateinit var publishCategoryAttributeService: PublishCategoryAttributeService
    /**
     * 测试查询1688类目树列表
     */
    @Test
    fun testQueryCategoryTreeList() {
        withSystemUser {
            try {
                var req = PublishCategoryMappingDetailQueryReq()
                req.platformId = PlatformEnum.YI_LIU_BA_BA.platformId
                var list = publishCategoryMappingService.queryPublishCategoryByPlatformCategoryId(req)
                log.info { "result:=====${JSONObject.toJSONString(list) }" }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    @Test
    fun getPlatformAttributeMappingDetailByCategoryMappingId(){
        var categoryMappingId = 7272487474156007447
        var result = publishCategoryAttributeService.getPlatformAttributeMappingDetailByCategoryMappingId(categoryMappingId)
        log.info { "result:=====${JSONObject.toJSONString(result) }" }
    }

    @Test
    fun saveCategoryMapping(){
        withSystemUser {
            var req = SavePublishCategoryMappingReq()
            req.publishCategoryId = 7270576779235684433
            req.channelId = 1
            req.channelName = "阿里"
            req.platformId = PlatformEnum.AE.platformId
            req.platformName = PlatformEnum.AE.platformName
            req.platformCategoryId = "3120601"
            req.platformCategoryName = "Underwear>Women's Intimates>Bra & Brief Sets"
            req.country = "US"
            req.remark = "测试"
            publishCategoryMappingService.save(req)
        }
    }

    @Test
    fun saveCategoryAttributeValueMapping(){
        withSystemUser {
            var req = SaveCategoryAttributeValueMappingReq()
            req.categoryMappingId = 7272487474160201752
            var attributeMapperList: MutableList<AttributeMapper> = mutableListOf()
            attributeMapperList.add(AttributeMapper().apply {
                this.attributeId = 1871750964441128960
                this.attributeName = "图案元素"
                this.platformAttributeKeyName = "fa_pattern"
                this.platformAttributeLabelName = "Pattern"
//                this.platformAttrId = 7272759405178516224
                var attributeValueMapperList: MutableList<AttributeValueMapper> = mutableListOf()
                attributeValueMapperList.add(AttributeValueMapper().apply {
                    this.attributeValue = "图形"
                    this.attributeValueId = 1871750967398113280
                    this.platformAttributeValue = "Band"
                    this.platformAttributeCode = "508"
//                    this.platformAttrValueId = 2
                })
                this.attributeValueList = attributeValueMapperList
            })
            req.attributeMapperList = attributeMapperList
            publishCategoryAttributeService.saveCategoryAttributeValueMapping(req)
        }
    }


    @Test
    fun getMappingCategoryTree(){
        val result = publishCategoryAttributeService.getPlatformAttributeMappingDetailByCategoryMappingId(7356219441678970881)
        log.info { "result:=====${JSONObject.toJSONString(result) }" }
    }

    @Test
    fun copyCategoryAttributeValueMapping(){
        withSystemUser {
            val req = CopyCategoryAttributeValueMappingReq()
            req.sourceCategoryMappingId = 7272487474160201752
            req.targetCategoryIds = mutableListOf<Long>(7272487474156007447)
            publishCategoryAttributeService.copyCategoryAttributeValueMapping(req)
        }
    }

    @Test
    fun listEnableAttributesByCategoryId(){
        val result = publishCategoryMappingService.listEnableAttributesByCategoryId("女装-裙装类-连衣裙")
        log.info { "result:=====${JSONObject.toJSONString(result) }" }
    }
}
