alter table product_template_ae_spu
    add column shop_id            bigint not null comment '店铺id' after product_id,
    add column task_status        int    not null default 0 comment '-2 取消上架, -1上架失败, 0 待开始, 1 进行中, 2 已完成' after spu_code,
    add column cancel_reason      varchar(255) comment '取消原因' after task_status,
    add column task_executor_id   bigint comment '上架任务执行人员id' after cancel_reason,
    add column task_executor_name varchar(255) comment '上架任务执行人员name' after task_executor_id,
    add column task_complete_time datetime comment '任务完成时间' after task_executor_name;

alter table product_template_lazada_spu
    add column shop_id            bigint not null comment '店铺id' after product_id,
    add column task_status        int    not null default 0 comment '-2 取消上架, -1上架失败, 0 待开始, 1 进行中, 2 已完成' after spu_code,
    add column cancel_reason      varchar(255) comment '取消原因' after task_status,
    add column task_executor_id   bigint comment '上架任务执行人员id' after cancel_reason,
    add column task_executor_name varchar(255) comment '上架任务执行人员name' after task_executor_id,
    add column task_complete_time datetime comment '任务完成时间' after task_executor_name;

alter table product_template_temu_spu
    add column shop_id            bigint not null comment '店铺id' after product_id,
    add column task_status        int    not null default 0 comment '-2 取消上架, -1上架失败, 0 待开始, 1 进行中, 2 已完成' after spu_code,
    add column cancel_reason      varchar(255) comment '取消原因' after task_status,
    add column task_executor_id   bigint comment '上架任务执行人员id' after cancel_reason,
    add column task_executor_name varchar(255) comment '上架任务执行人员name' after task_executor_id,
    add column task_complete_time datetime comment '任务完成时间' after task_executor_name;

alter table product_sync_log
    add column template_spu_id bigint null comment '模板spu id' after product_id;

alter table import_product_record
    add column import_data_type tinyint(1) default 1 comment '导入数据类型, 1-全部, 2-平台属性, 3-商品规格, 4-物流包装, 5-商品属性' after platform_id,
    add column import_source    tinyint(1) default 1 comment '导入来源, 1-商品中心, 2-待上架' after import_data_type;


ALTER TABLE `publish_attribute_group`
    ADD COLUMN `group_type` tinyint NULL DEFAULT 0 COMMENT '分组类型0品类属性1公共属性2平台属性' AFTER `attr_amount`,
    ADD COLUMN `state`      tinyint NULL DEFAULT 1 COMMENT '状态1启用0停用' AFTER `group_type`;

ALTER TABLE `publish_attribute`
    ADD COLUMN `state`     tinyint      NULL DEFAULT 1 COMMENT '状态1启用0停用' AFTER `attribute_code`,
    ADD COLUMN `show_type` varchar(255) NULL COMMENT '前端展示元素类型input输入框,singleSelect下拉单选,multiSelect下拉多选,radio单选,uploadFile文件上传' AFTER `state`;

ALTER TABLE `publish_category_attr`
    ADD COLUMN `request_flag` tinyint NULL DEFAULT 0 COMMENT '是否品类必填属性1是0否' AFTER `attribute_id`;

ALTER TABLE `publish_attribute_value`
    ADD COLUMN `default_flag` tinyint     NULL DEFAULT 0 COMMENT '是否为默认值1是0否' AFTER `state`,
    ADD COLUMN `unit`         varchar(30) NULL COMMENT '单位' AFTER `default_flag`;

ALTER TABLE `publish_category`
    ADD COLUMN `state`                     tinyint NULL DEFAULT 1 COMMENT '品类状态1启用0停用' AFTER `sort_num`,
    ADD COLUMN `attribute_associate_state` tinyint NULL DEFAULT 0 COMMENT '属性关联状态0无配置1已配置' AFTER `state`,
    ADD COLUMN `platform_associate_state`  tinyint NULL DEFAULT 0 COMMENT '平台关联状态0未关联平台品类，1已关联平台品类，2已关联平台品类属性' AFTER `attribute_associate_state`,
    ADD COLUMN `package_config_state`      tinyint NULL DEFAULT 0 COMMENT '包装配置状态0无配置1已配置' AFTER `platform_associate_state`;

ALTER TABLE `publish_platform_attr`
    ADD COLUMN `request_flag` tinyint NULL DEFAULT 0 COMMENT '是否平台必填属性1是0否' AFTER `country`;

ALTER TABLE `product`
    ADD COLUMN `source_type`                   tinyint       NULL DEFAULT 1 COMMENT '商品来源1款式平台2手动导入' AFTER `shop_gross_margin`,
    ADD COLUMN `product_type`                  int           NULL COMMENT '商品类型1AI时装2AI现货3AI POD' AFTER `source_type`,
    ADD COLUMN `package_info`                  json          NULL COMMENT '物流包装信息' AFTER `product_type`,
    ADD COLUMN `instructions`                  json          NULL COMMENT '电子说明书' AFTER `package_info`,
    ADD COLUMN `resource_state`                tinyint       NULL COMMENT '资料完善状态0待补全1已完善' AFTER `instructions`,
    ADD COLUMN `video_state`                   tinyint       NULL COMMENT '视频状态1未完成2已完成3更新中4已更新' AFTER `resource_state`,
    ADD COLUMN `spu_canceled`                  tinyint       NULL DEFAULT 0 COMMENT '上游款式是否已取消1是0否' AFTER `video_state`,
    ADD COLUMN `buyer_id`                      bigint        NULL DEFAULT NULL COMMENT '买手id' AFTER `spu_canceled`,
    ADD COLUMN `buyer_name`                    varchar(32)   NULL DEFAULT NULL COMMENT '买手名称' AFTER `buyer_id`,
    ADD COLUMN `scene_name`                    varchar(32)   NULL DEFAULT NULL COMMENT '场景名称(ops: JV_scene)' AFTER `buyer_name`,
    ADD COLUMN `scene_code`                    varchar(255)  NULL DEFAULT NULL COMMENT '场景编码' AFTER `scene_name`,
    ADD COLUMN `quality_level`                 varchar(32)   NULL DEFAULT NULL COMMENT '品质等级' AFTER `scene_code`,
    ADD COLUMN `quality_level_code`            varchar(64)   NULL DEFAULT NULL COMMENT '品质等级编号' AFTER `quality_level`,
    ADD COLUMN `weave_mode_code`               varchar(32)   NULL DEFAULT NULL COMMENT '织造方式code' AFTER `quality_level_code`,
    ADD COLUMN `weave_mode`                    varchar(32)   NULL DEFAULT NULL COMMENT '织造方式' AFTER `weave_mode_code`,
    ADD COLUMN `inspiration_image_source_code` varchar(500)  NULL DEFAULT NULL COMMENT '灵感图来源编码' AFTER `weave_mode`,
    ADD COLUMN `inspiration_image_source`      varchar(500)  NULL DEFAULT NULL COMMENT '灵感图来源名称' AFTER `inspiration_image_source_code`,
    ADD COLUMN `inspiration_brand_code`        varchar(500)  NULL DEFAULT NULL COMMENT '灵感源品牌编码' AFTER `inspiration_image_source`,
    ADD COLUMN `inspiration_brand`             varchar(500)  NULL DEFAULT NULL COMMENT '灵感源品牌名称' AFTER `inspiration_brand_code`,
    ADD COLUMN `planning_source_name`          varchar(32)   NULL DEFAULT NULL COMMENT '企划来源name' AFTER `inspiration_brand`,
    ADD COLUMN `planning_source_code`          varchar(32)   NULL DEFAULT NULL COMMENT '企划来源code' AFTER `planning_source_name`,
    ADD COLUMN `product_link`                  varchar(1024) NULL DEFAULT NULL COMMENT '商品链接' AFTER `model_name`,
    ADD COLUMN `spu_name`                      varchar(1000) NULL DEFAULT NULL COMMENT '商品名（中文）' AFTER `product_link`,
    ADD COLUMN `spu_name_trans`                varchar(1000) NULL DEFAULT NULL COMMENT '商品名翻译' AFTER `spu_name`,
    ADD COLUMN `elastic_code`                  varchar(32)   NULL DEFAULT NULL COMMENT '弹性编码-OPS' AFTER `spu_name_trans`,
    ADD COLUMN `elastic_name`                  varchar(32)   NULL DEFAULT NULL COMMENT '弹性名称' AFTER `elastic_code`;

ALTER TABLE `ae_sale_goods`
    ADD COLUMN `gross_margin` decimal(10, 2) NULL DEFAULT NULL COMMENT '毛利率' AFTER `image_pack_rule_version`;

ALTER TABLE `product_skc`
    ADD COLUMN `size_names`         json NULL COMMENT '尺码名数组[S,X,2XL]' AFTER `import_flag`,
    ADD COLUMN `special_size_names` json NULL COMMENT '特殊尺码名数组[加大码]' AFTER `size_names`;

CREATE TABLE `publish_category_package_config`
(
    `package_config_id`   bigint         NOT NULL COMMENT '包装配置ID',
    `publish_category_id` bigint         NULL     DEFAULT NULL COMMENT '品类id',
    `packing_volume`      json           NULL COMMENT '包装体积JSON对象',
    `weight`              decimal(13, 4) NULL     DEFAULT NULL COMMENT '商品重量（单位g）',
    `outer_packing_image` json           NULL COMMENT '外包装图片JSON对象',
    `outer_packing_type`  tinyint        NULL     DEFAULT NULL COMMENT '外包装类型1硬包装2软包装+硬物3软包装+软物',
    `outer_packing_shape` tinyint        NULL     DEFAULT NULL COMMENT '外包装形状1长方体2圆柱体3其他不规则形状',
    `remark`              varchar(100)   NULL     DEFAULT NULL COMMENT '备注',
    `creator_id`          bigint         NOT NULL COMMENT '创建人id',
    `creator_name`        varchar(32)    NOT NULL COMMENT '创建人名称',
    `created_time`        datetime       NOT NULL COMMENT '创建时间',
    `reviser_id`          bigint         NULL     DEFAULT NULL COMMENT '最近修改人id',
    `reviser_name`        varchar(32)    NULL     DEFAULT NULL COMMENT '最近修改人名称',
    `revised_time`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `is_deleted`          tinyint(1)     NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `deleted`             tinyint(1)     NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `tenant_id`           bigint         NULL     DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`package_config_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '品类包装配置表'
  ROW_FORMAT = Dynamic;

CREATE TABLE `product_publish_store`
(
    `publish_id`        bigint       NOT NULL COMMENT '商品上架id',
    `product_id`        bigint       NOT NULL COMMENT '商品源ID',
    `spu_code`          varchar(250) NULL COMMENT 'spu编码',
    `channel_id`        bigint       NULL     DEFAULT NULL COMMENT '渠道ID',
    `platform_id`       bigint       NULL     DEFAULT NULL COMMENT '平台ID',
    `shop_id`           bigint       NULL     DEFAULT NULL COMMENT '店铺ID',
    `state`             tinyint      NULL     DEFAULT NULL COMMENT '在架状态1在线2已下架3审核中',
    `publish_time`      datetime     NULL     DEFAULT NULL COMMENT '首次上架时间',
    `publish_user_id`   bigint       NULL     DEFAULT NULL COMMENT '首次发布人id',
    `publish_user_name` varchar(32)  NULL     DEFAULT NULL COMMENT '首次发布人名称',
    `is_deleted`        tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `deleted`           tinyint(1)   NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`        bigint       NOT NULL COMMENT '创建人id',
    `creator_name`      varchar(32)  NOT NULL COMMENT '创建人',
    `created_time`      datetime     NOT NULL COMMENT '创建时间',
    `reviser_id`        bigint       NULL     DEFAULT NULL COMMENT '最近修改人id;新增时取创建人id',
    `reviser_name`      varchar(32)  NULL     DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`      datetime     NULL     DEFAULT NULL COMMENT '更新时间',
    `tenant_id`         bigint       NULL     DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`publish_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '商品上架店铺记录'
  ROW_FORMAT = Dynamic;

CREATE TABLE `product_attributes_v2`
(
    `product_attributes_id` bigint      NOT NULL COMMENT '主键',
    `product_id`            bigint      NULL     DEFAULT NULL COMMENT '商品ID',
    `category_id`           bigint      NULL     DEFAULT NULL COMMENT '业务品类id',
    `attribute_id`          bigint      NULL     DEFAULT NULL COMMENT '属性名称ID',
    `attribute_value_id`    bigint      NULL     DEFAULT NULL COMMENT '业务属性值ID',
    `attribute_value`       text        NULL COMMENT '属性值',
    `unit`                  varchar(30) NULL     DEFAULT NULL COMMENT '单位',
    `is_deleted`            tinyint(1)  NULL     DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `deleted`               tinyint(1)  NOT NULL DEFAULT 0 COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`            bigint      NULL     DEFAULT NULL COMMENT '创建人id',
    `creator_name`          varchar(32) NOT NULL COMMENT '创建人',
    `created_time`          datetime    NULL     DEFAULT NULL COMMENT '创建时间',
    `reviser_id`            bigint      NULL     DEFAULT NULL COMMENT '最近修改人id;',
    `reviser_name`          varchar(32) NULL     DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`          datetime    NULL     DEFAULT NULL COMMENT '更新时间',
    `tenant_id`             bigint      NULL     DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_attributes_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '商品属性表'
  ROW_FORMAT = DYNAMIC;

CREATE TABLE `product_size_detail`
(
    `size_detail_id` bigint       NOT NULL COMMENT '商品尺码表id',
    `product_id`     bigint       NOT NULL COMMENT '商品主表id',
    `part_name`      varchar(100) NULL     DEFAULT NULL COMMENT '部位名称',
    `size_json`      json         NULL COMMENT '尺寸数据json [{\"data\":\"11\",\"size\":\"S\"},{\"data\":\"11\",\"size\":\"XS\"}]',
    `creator_id`     bigint       NOT NULL COMMENT '创建人ID',
    `created_time`   datetime     NOT NULL COMMENT '创建时间',
    `creator_name`   varchar(50)  NULL     DEFAULT NULL COMMENT '创建人姓名',
    `reviser_id`     bigint       NULL     DEFAULT NULL COMMENT '最近修改者ID',
    `revised_time`   datetime     NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    `reviser_name`   varchar(50)  NULL     DEFAULT NULL COMMENT '更新人姓名',
    `deleted`        tinyint      NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是',
    `tenant_id`      bigint       NULL     DEFAULT 2 COMMENT '租户id',
    PRIMARY KEY (`size_detail_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '商品尺码表'
  ROW_FORMAT = DYNAMIC;

CREATE TABLE `product_template_size_detail`
(
    `size_detail_id`  bigint       NOT NULL COMMENT '商品尺码表id',
    `template_spu_id` bigint       NOT NULL COMMENT '待上架商品SPU ID',
    `platform_id`     bigint       NOT NULL COMMENT '平台id',
    `product_id`      bigint       NOT NULL COMMENT '商品主表id',
    `part_name`       varchar(100) NULL     DEFAULT NULL COMMENT '部位名称',
    `size_json`       json         NULL COMMENT '尺寸数据json [{\"data\":\"11\",\"size\":\"S\"},{\"data\":\"11\",\"size\":\"XS\"}]',
    `creator_id`      bigint       NOT NULL COMMENT '创建人ID',
    `created_time`    datetime     NOT NULL COMMENT '创建时间',
    `creator_name`    varchar(50)  NULL     DEFAULT NULL COMMENT '创建人姓名',
    `reviser_id`      bigint       NULL     DEFAULT NULL COMMENT '最近修改者ID',
    `revised_time`    datetime     NULL     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
    `reviser_name`    varchar(50)  NULL     DEFAULT NULL COMMENT '更新人姓名',
    `deleted`         tinyint      NOT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是',
    `tenant_id`       bigint       NULL     DEFAULT 2 COMMENT '租户id',
    PRIMARY KEY (`size_detail_id`) USING BTREE,
    INDEX `idx_template_id` (`template_spu_id`) USING BTREE,
    INDEX `idx_platform_id` (`platform_id`) USING BTREE,
    INDEX `idx_product_id` (`product_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '待上架商品尺码表'
  ROW_FORMAT = DYNAMIC;

CREATE TABLE `gross_margin_record`
(
    `record_id`    bigint         NOT NULL COMMENT '记录ID',
    `product_id`   bigint         NULL     DEFAULT NULL COMMENT '商品ID',
    `shop_id`      bigint         NULL     DEFAULT NULL COMMENT '店铺ID',
    `gross_margin` decimal(10, 2) NULL     DEFAULT NULL COMMENT '毛利率',
    `creator_id`   bigint         NULL     DEFAULT NULL COMMENT '创建人id',
    `creator_name` varchar(50)    NULL     DEFAULT NULL COMMENT '创建人名称',
    `created_time` datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `reviser_id`   bigint         NULL     DEFAULT NULL COMMENT '最近修改者ID',
    `reviser_name` varchar(50)    NULL     DEFAULT NULL COMMENT '最近修改者',
    `revised_time` datetime       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    `deleted`      tinyint(1)     NOT NULL DEFAULT 0 COMMENT '是否已删除:0未删除,1已删除',
    `tenant_id`    bigint         NULL     DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '商品毛利率调整记录'
  ROW_FORMAT = Dynamic;


CREATE TABLE `product_template_attributes_v2`
(
    `product_attributes_id` bigint      NOT NULL COMMENT '主键',
    `template_spu_id`       bigint      NOT NULL COMMENT '待上架SPU ID',
    `platform_id`           bigint      NOT NULL COMMENT '平台ID',
    `product_id`            bigint      NOT NULL COMMENT '商品ID',
    `category_id`           bigint               DEFAULT NULL COMMENT '业务品类id',
    `attribute_id`          bigint               DEFAULT NULL COMMENT '属性名称ID',
    `attribute_value_id`    bigint               DEFAULT NULL COMMENT '业务属性值ID',
    `attribute_value`       text COMMENT '属性值',
    `unit`                  varchar(30)          DEFAULT NULL COMMENT '单位',
    `is_deleted`            tinyint(1)           DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `deleted`               tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`            bigint               DEFAULT NULL COMMENT '创建人id',
    `creator_name`          varchar(32) NOT NULL COMMENT '创建人',
    `created_time`          datetime             DEFAULT NULL COMMENT '创建时间',
    `reviser_id`            bigint               DEFAULT NULL COMMENT '最近修改人id;',
    `reviser_name`          varchar(32)          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`          datetime             DEFAULT NULL COMMENT '更新时间',
    `tenant_id`             bigint               DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_attributes_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='待上架商品属性表';


CREATE TABLE `product_sale_attributes_v2`
(
    `product_attributes_id` bigint      NOT NULL COMMENT '主键',
    `sale_goods_id`         bigint      NOT NULL COMMENT '已上架SPU ID',
    `platform_id`           bigint      NOT NULL COMMENT '平台ID',
    `product_id`            bigint      NOT NULL COMMENT '商品ID',
    `category_id`           bigint               DEFAULT NULL COMMENT '业务品类id',
    `attribute_id`          bigint               DEFAULT NULL COMMENT '属性名称ID',
    `attribute_value_id`    bigint               DEFAULT NULL COMMENT '业务属性值ID',
    `attribute_value`       text COMMENT '属性值',
    `unit`                  varchar(30)          DEFAULT NULL COMMENT '单位',
    `is_deleted`            tinyint(1)           DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `deleted`               tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否已删除;0未删除，1已删除',
    `creator_id`            bigint               DEFAULT NULL COMMENT '创建人id',
    `creator_name`          varchar(32) NOT NULL COMMENT '创建人',
    `created_time`          datetime             DEFAULT NULL COMMENT '创建时间',
    `reviser_id`            bigint               DEFAULT NULL COMMENT '最近修改人id;',
    `reviser_name`          varchar(32)          DEFAULT NULL COMMENT '最近修改人名称;新增时取创建人名称',
    `revised_time`          datetime             DEFAULT NULL COMMENT '更新时间',
    `tenant_id`             bigint               DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`product_attributes_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='已上架商品属性表';
