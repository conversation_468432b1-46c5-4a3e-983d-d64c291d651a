[versions]
lombok = "1.18.34"
google_guava = "33.4.8-jre"
easyexcel = "4.0.3"
easypoi_base = "4.5.0"
pagehelper = "5.3.3"
commons_lang3 = "3.17.0"
alibaba-alimt = "1.4.0"
commons-io = "2.18.0"
commons-compress = "1.27.1"
apache-poi = "5.3.0"
xstream = "1.4.21"
thumbnailator = "0.4.20"

ofp-sdk = "0.0.7"
sdp-curation-sdk = "0.0.1"
sdp-material-sdk = "1.0.0-RELEASE"
sdp-sample-clothes-sdk = "1.0.12-RELEASE"
sdp-design-sdk = "1.0.19"
dict-sdk = "0.0.2"
eis-center-sdk = "0.0.4-SNAPSHOT"
pigeon-sdk = "0.0.3"
aigc-digital-print-sdk = "3.0.3-RELEASE"

lazada-sdk = "1.2.0"
aliexpress-sdk = "1.3.5-ae"

aigc-server-sdk = "0.0.3"
butted-sdk = "3.3.2"
uacs-sdk = "0.0.12"

[libraries]
lombok = { group = "org.projectlombok", name = "lombok", version.ref = "lombok" }
google_guava = { group = "com.google.guava", name = "guava", version.ref = "google_guava" }
easyexcel = { group = "com.alibaba", name = "easyexcel", version.ref = "easyexcel" }
easypoi_base = { group = "cn.afterturn", name = "easypoi-base", version.ref = "easypoi_base" }
pagehelper = { group = "com.github.pagehelper", name = "pagehelper", version.ref = "pagehelper" }
commons_lang3 = { group = "org.apache.commons", name = "commons-lang3", version.ref = "commons_lang3" }
alimt = { group = "com.aliyun", name = "alimt20181012", version.ref = "alibaba-alimt" }

ofp-sdk = { group = "tech.tiangong.ofp", name = "ofp-order-sdk", version.ref = "ofp-sdk" }
sdp-curation-sdk = { group = "tech.tiangong.sdp", name = "sdp-curation-sdk", version.ref = "sdp-curation-sdk" }
sdp-material-sdk = { group = "tech.tiangong.sdp", name = "sdp-clothing-foundation-material-sdk", version.ref = "sdp-material-sdk" }
sdp-sample-clothes-sdk = { group = "tech.tiangong.sdp", name = "sdp-sample-clothes-service-sdk", version.ref = "sdp-sample-clothes-sdk" }
sdp-design-sdk = { group = "tech.tiangong.sdp", name = "sdp-design-service-sdk", version.ref = "sdp-design-sdk" }
dict-sdk = { group = "team.aikero.arsenal", name = "dict-sdk", version.ref = "dict-sdk" }
pigeon-sdk = { group = "team.aikero.pigeon", name = "pigeon-sdk", version.ref = "pigeon-sdk" }


lazada-sdk = { group = "com.lazada", name = "lazop-api-sdk", version.ref = "lazada-sdk" }
aliexpress-sdk = {group = "com.aliexpress.sdk", name = "aliexpress-sdk", version.ref = "aliexpress-sdk"}
xstream = { group = "com.thoughtworks.xstream", name = "xstream", version.ref = "xstream" }
thumbnailator = { group = "net.coobird", name = "thumbnailator", version.ref = "thumbnailator" }

commons-io = {group = "commons-io", name="commons-io", version.ref = "commons-io"}
commons-compress = {group = "org.apache.commons", name="commons-compress", version.ref = "commons-compress"}
apache-poi = {group = "org.apache.poi", name="poi", version.ref = "apache-poi"}
eis-center-sdk = {group = "tech.tiangong.eis",name="eis-center-sdk",version.ref = "eis-center-sdk"}
aigc-digital-print-sdk = {group = "tech.tiangong.fashion",name = "aigc-digital-print-sdk",version.ref = "aigc-digital-print-sdk"}

aigc-server-client = {group = "tech.tiangong.fashion",name = "aigc-server-client",version.ref = "aigc-server-sdk"}
aigc-server-common = {group = "tech.tiangong.fashion",name = "aigc-server-common",version.ref = "aigc-server-sdk"}
butted-sdk-client = {group = "tech.tiangong.butted",name = "butted-sdk",version.ref = "butted-sdk"}
uacs-sdk-client = {group = "team.aikero.blade.uacs",name = "uacs-sdk",version.ref = "uacs-sdk"}
