package tech.tiangong.pop.common.resp

/**
 * 品类属性
 */
class CategoryAttributeResp {

    /**
     * 品类ID
     */
    var categoryId: Long? = null

    /**
     * 属性ID
     */
    var attributeId: Long? = null

    /**
     * 属性组ID
     */
    var attributeGroupId: Long? = null
    /**
     * 属性组名称
     */
    var groupName: String? = null
    /**
     * 分组类型0品类属性1公共属性2平台属性
     * 参考 AttributeGroupTypeEnum
     */
    var groupType:Int? = null
    /**
     * 属性名称
     */
    var attributeName: String? = null
    /**
     * 属性编码
     */
    var attributeCode: String? = null
    /**
     * 属性状态1启用0停用
     */
    var state: Int? = null
    /**
     * 前端展示元素类型
     * input输入框,
     * singleSelect下拉单选,
     * multiSelect下拉多选,
     * num数值-输入数值和单位,
     * property_choose_and_num属性勾选和数值录入
     * 参考 PublishAttributeShowTypeEnum
     */
    var showType: String? = null
    /**
     * POP品类的属性是否必填1是0否
     */
    var popRequestFlag: Int? = null
    /**
     * 第三方平台的属性是否必填1是0否
     */
    var platformRequestFlag: Int? = null
    /**
     * 是否必填1是0否（可能是第三方平台必填，可能是POP自定义品类属性必填）
     */
    var requestFlag: Int? = null
    /**
     * 属性值集合
     */
    var attributeValueList: List<CategoryAttributeValueResp>? = null
}
