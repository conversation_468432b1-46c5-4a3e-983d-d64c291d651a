package tech.tiangong.pop.common.enums

/**
 * 数据类型【1取消SKC；2供货价更新；3新增SKC;4创建商品；5更新品类；6更新颜色】
 * <AUTHOR>
 * @date 2021/1/14
 */
enum class ProductUpdateTypeEnum(
    val code: Int,
    val desc: String,
) {
    UNKNOWN(-999, "未知操作"),
    CANCLE(1, "取消SKC"),
    UPDATE_PRICE(2, "供货价更新"),
    ADD_SKC(3, "新增SKC"),
    CREATE(4, "创建商品"),
    UPDATE_CATEGORY(5, "更新品类"),
    UPDATE_COLOR(6, "更新颜色"),
    UPDATE_PURCHASES_PRICE(7, "更新采购价"),
    UPDATE_SIZE(8, "更新尺码信息"),
    UPDATE_PRICING_TYPE(9, "更新定价类型"),
    UPDATE_CLOTHING_STYLE(10, "更新款式风格"),
    UPDATE_PRODUCT_SPU(11, "更新商品(SPU)基本信息"),
    UPDATE_PRODUCT_ATTRIBUTE(12, "更新商品属性"),
    UPDATE_PRODUCT_SIZE_DETAIL(13, "更新尺码表"),
    ;

    companion object {
        fun findByCode(code: Int?): ProductUpdateTypeEnum {
            return entries.find { it.code == code } ?: UNKNOWN
        }
    }
}
