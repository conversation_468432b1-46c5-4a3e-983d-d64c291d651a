package tech.tiangong.pop.common.dto

import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import tech.tiangong.pop.common.jackson.MultiFormatLocalDateTimeDeserializer
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 创建商品 DTO
 * <AUTHOR>
 * @since 2024-11-19 11:19
 */
data class CreateProductDto(

    /** 灵感来源ID */
    var inspiraSourceId: Long? = null,

    /** 选款时间 */
    @JsonDeserialize(using = MultiFormatLocalDateTimeDeserializer::class)
    var selectStyleTime: LocalDateTime? = null,

    /** 企划来源ID */
    var planSourceId: Long? = null,

    /** 版型号 */
    var prototypeNum: String? = null,

    /** 设计师ID */
    var designerId: String? = null,

    /** 设计师 */
    var designName: String? = null,

    /** 设计组 */
    var designGroup: String? = null,

    /** 选款人ID */
    var selectStyleId: Long? = null,

    /** 选款人姓名 */
    var selectStyleName: String? = null,

    /** 灵感图URL */
    var inspiraImgUrl: String? = null,

    /** 灵感图来源站点 */
    var inspiraCountry: String? = null,

    /** 货盘类型 */
    var goodsRepType: String? = null,

    /** 商品类型 */
    var goodsType: String? = null,

    /** 竞品销售价（美元） */
    var cmpSalePrice: BigDecimal? = null,

    /** 竞品划线价格（美元） */
    var cmpRetailPrice: BigDecimal? = null,

    /** 供给方式 */
    var supplyMode: String? = null,

    /** 买手备注 */
    var buyerRemark: String? = null,

    /** 主图 URL */
    var mainImgUrl: String? = null,

    /** 波次 */
    var waves: String? = null,

    /** 定价类型【1按返单规则；2不返单规则】 */
    var pricingType: Int? = null,

    /** 现货类型【1现货 try on(反季)；2 iFashion】 */
    var spotType: Int? = null,

    /** 现货类型 OPS 编码 */
    var spotTypeCode: String? = null,

    /** SPU 编码 */
    @field:NotEmpty(message = "spu编码不能为空")
    var spuCode: String? = null,

    /** 商品标题 */
    var productTitle: String? = null,

    /** 图案元素 */
    var titleList: List<String>? = null,

    /** 店铺ID */
    var shopId: Long? = null,

    /** 店铺名称 */
    var shopName: String? = null,

    /** 国家站点（多站点-分割） */
    var countrys: List<String>? = null,

    /** 品类编码（多级-分割） */
    var categoryCode: String? = null,

    /** 品类名称（多级-分割） */
    var categoryName: String? = null,

    /** 尺码组名称 */
    var sizeGroupName: String? = null,

    /** 尺码组编码 */
    var sizeGroupCode: String? = null,

    /** 花形图片集合 */
    @field:Valid
    var imageFlowerUrls: List<Image>? = null,

    /** SKC 数据 */
    @field:Valid
    @field:NotEmpty(message = "SKC数据不能为空")
    var dataList: List<Skc>? = null,

    /**
     * 商品属性 改用 attributesV2List
     * */
    @Deprecated("用attributesV2List替代")
    var attributesList: List<Attributes>? = null,

    /** 款式风格名称 */
    var clothingStyleName: String? = null,

    /** 款式风格编码 */
    var clothingStyleCode: String? = null,
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: Int? = null,

    /**
     * 市场编码
     */
    var marketCode: String? = null,

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null,

    /**
     * 商品主题code
     */
    var productThemeCode: String? = null,

    /**
     * 商品主题name
     */
    var productThemeName: String? = null,

    /** 图片标签信息 */
    var imageLabelInfoList: List<ImageLabelInfoDto>? = null,

    /** 印花类型 */
    var printType: String? = null,

    /** 来源业务ID */
    var sourceBizId: Long? = null,

    /** 生产资料列表 */
    @field:Valid
    var materialImageList: List<Image>? = null,

    /** 元素名称 */
    var styleElementName: String? = null,

    /** 元素编码 */
    var styleElementCode: String? = null,

    /** 季节 JSON */
    var styleSeason: String? = null,

    /** 合身编码(版型) */
    var fitCode: String? = null,

    /** 合身名称(版型) */
    var fitName: String? = null,

    /**
     * 弹性编码-OPS
     */
    var elasticCode: String? = null,

    /**
     * 弹性名称
     */
    var elasticName: String? = null,
    /** 款式类型1设计款2现货款3数据印花款 */
    var styleType: Int? = null,

    /**
     * 商品分类1AI-时装2AI-现货3AI-POD
     */
    var productType: Int? = null,
    /**
     * 买手id
     */
    var buyerId: Long? = null,

    /**
     * 买手名称
     */
    var buyerName: String? = null,
    /**
     * 场景名称(ops: JV_scene)
     */
    var sceneName: String? = null,

    /**
     * 场景编码
     */
    var sceneCode: String? = null,
    /**
     * 品质等级
     */
    var qualityLevel: String? = null,

    /**
     * 品质等级编号
     */
    var qualityLevelCode: String? = null,
    /**
     * 织造方式code
     */
    var weaveModeCode: String? = null,
    /**
     * 织造方式
     */
    var weaveMode: String? = null,
    /**
     * 灵感图来源编码
     */
    var inspirationImageSourceCode: String? = null,
    /**
     * 灵感图来源
     */
    var inspirationImageSource: String? = null,
    /**
     * 灵感源品牌编码
     */
    var inspirationBrandCode: String? = null,
    /**
     * 灵感源品牌
     */
    var inspirationBrand: String? = null,
    /**
     * 企划来源name
     */
    var planningSourceName: String? = null,
    /**
     * 企划来源code
     */
    var planningSourceCode: String? = null,
    /**
     * 商品链接
     */
    var productLink: String? = null,
    /**
     * 商品名（中文）
     */
    var spuName: String? = null,
    /**
     * 商品名翻译
     */
    var spuNameTrans: String? = null,
    /**
     * 商品属性
     */
    var attributesV2List: List<ProductAttributesV2>? = null,
    /**
     * 尺寸表
     */
    var sizeDetails: List<ProductSizeDetail>? = null,
) {

    /** 商品属性（attributeName 为面料成分，attributeValue 为成分值） */
    data class Attributes(
        var attributeName: String? = null,
        var attributeValue: String? = null
    )

    /** SKC 信息 */
    data class Skc(
        /** SKC */
        @field:NotEmpty(message = "SKC不能为空")
        var skc: String? = null,

        /** 颜色 */
        var color: String? = null,

        /** 颜色编码 */
        var colorCode: String? = null,

        /** 颜色缩写编码 */
        var colorAbbrCode: String? = null,

        /** 平台颜色 */
        var platformColor: String? = null,

        /** 序号 */
        var sortNum: Int? = null,

        /** skc 图片URL（多值用逗号分隔） */
        var pictures: List<String>? = null,

        /** 跨境价 */
        var cbPrice: BigDecimal? = null,

        /** 本土价 */
        var localPrice: BigDecimal? = null,

        /** 采购价 */
        var purchasePrice: BigDecimal? = null,

        /** SKU 列表 */
        var skuList: List<Sku>? = null,
    )

    /** SKU 信息 */
    data class Sku(
        /** 尺码名称 */
        var sizeName: String? = null,
        /**
         * 特殊尺码名称
         */
        var specialSizeName: String? = null,
    )

    /**
     * 商品属性表
     */
    data class ProductAttributesV2(
        /**
         * 属性名称ID
         */
        var attributeId: Long? = null,

        /**
         * 业务属性值ID
         */
        var attributeValueId: Long? = null,

        /**
         * 业务属性值
         */
        var attributeValue: String? = null
    ){
    }

    data class ProductSizeDetail(
        /**
         * 部位名称
         */
        var partName: String? = null,
        /**
         * 尺寸数据
         */
        var sizeJson: List<ProductSizeJson>? = null,
    ){}

    class ProductSizeJson(

        /**
         * 尺码
         */
        var size: String? = null,

        /**
         * 数值
         */
        var data: String? = null
    ) {

    }
}

/**
 * 图套图片信息（营销图）
 */
data class PictureKitInfo(
    /** 颜色名称 */
    var colorName: String? = null,

    /** 颜色编码 */
    var colorCode: String? = null,

    /** 颜色缩写编码 */
    var colorAbbrCode: String? = null,

    /** 图片链接 */
    @field:NotBlank(message = "图套图片链接不能为空")
    var imageUrl: String? = null,

    /** 图套名称 */
    @field:NotBlank(message = "图套名称不能为空")
    var kitName: String? = null
)


